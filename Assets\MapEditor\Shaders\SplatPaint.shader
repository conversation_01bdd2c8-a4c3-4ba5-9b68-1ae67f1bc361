Shader "MapEditor/SplatPaint"
{
    Properties
    {
        _MainTex ("Alpha Mask", 2D) = "white" {}
        _MaskTex ("Mask Tex", 2D) = "white" {}
        _Strength ("Strength", Range(0,1)) = 1
        _Channel ("Channel", Int) = 0
        _Offset ("Offset", Vector) = (0,0,0,0)
        _Prev ("Previous Frame", 2D) = "white" {}
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        Cull Off ZWrite Off ZTest Always

        // ---------- PASS 0 : 生成 mask * strength 到 R 通道 ----------
        Pass
        {
            Name "GenerateMask"
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MainTex;
            float4 _MainTex_ST;
            float _Strength;

            struct appdata { float4 vertex : POSITION; float2 uv : TEXCOORD0; };
            struct v2f { float4 pos : SV_POSITION; float2 uv : TEXCOORD0; };
            v2f vert (appdata v)
            {
                v2f o; o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }
            fixed4 frag (v2f i) : SV_Target
            {
                fixed a = tex2D(_MainTex, i.uv).a;
                return fixed4(a * _Strength, 0,0,0);
            }
            ENDHLSL
        }

        // ---------- PASS 1 : 把 Mask 写入目标 RT 的指定通道 (简化：仅 Add) ----------
        Pass
        {
            Name "PaintWeight"
            Blend One Zero // 覆盖输出像素

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MaskTex;          // 上一步生成的 mask
            float4    _MaskTex_ST;
            int       _Channel;
            float4    _Offset;           // xy: brush uv offset (lower-left), z: scale (diameter/chunk)
            sampler2D _Prev;

            struct appdata { float4 vertex:POSITION; float2 uv:TEXCOORD0; };
            struct v2f { float4 pos:SV_POSITION; float2 uv:TEXCOORD0; };
            v2f vert(appdata v){ v2f o; o.pos = UnityObjectToClipPos(v.vertex); o.uv = v.uv; return o; }

            fixed4 frag(v2f i) : SV_Target
            {
                float2 local = (i.uv - _Offset.xy) / _Offset.z;
                fixed mask = 0;
                if(local.x >= 0 && local.x <= 1 && local.y >= 0 && local.y <= 1)
                {
                    mask = tex2D(_MaskTex, local).r; // 已乘 strength
                }

                fixed4 prev = tex2D(_Prev, i.uv);
                // 按通道累加 mask，其他通道可选归一化，这里简单保留
                if(_Channel==0)
                {
                    prev.r = prev.r + mask * (1 - prev.r);
                }
                else if(_Channel==1)
                {
                    prev.g = prev.g + mask * (1 - prev.g);
                }
                else if(_Channel==2)
                {
                    prev.b = prev.b + mask * (1 - prev.b);
                }
                else if(_Channel==3)
                {
                    prev.a = prev.a + mask * (1 - prev.a);
                }
                return prev;
            }
            ENDHLSL
        }
    }
    Fallback Off
} 