using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Core
{
    /// <summary>
    /// 图层渲染器接口，定义图层渲染器的基本行为
    /// </summary>
    public interface ILayerRenderer
    {
        /// <summary>
        /// 图层渲染器的唯一标识符
        /// </summary>
        string ID { get; }
        
        /// <summary>
        /// 关联的图层
        /// </summary>
        IMapLayer LinkedLayer { get; }
        
        /// <summary>
        /// 图层的排序权重
        /// </summary>
        int LayerOrder { get; }
        
        /// <summary>
        /// 初始化图层渲染器
        /// </summary>
        /// <param name="layer">要关联的图层</param>
        void Initialize(IMapLayer layer);
        
        /// <summary>
        /// 刷新指定视口范围内的渲染内容
        /// </summary>
        /// <param name="viewBounds">视口边界</param>
        void RefreshChunks(Bounds viewBounds);
        
        /// <summary>
        /// 当图层被移除时的清理逻辑
        /// </summary>
        void OnLayerRemoved();
        
        /// <summary>
        /// 设置渲染器可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        void SetVisible(bool visible);
    }
    
    /// <summary>
    /// 图层渲染服务接口，负责管理基于图层的渲染器创建、移除和更新
    /// </summary>
    public interface ILayerRenderService
    {
        /// <summary>
        /// 清理所有渲染器
        /// </summary>
        void ClearAll();
        
        /// <summary>
        /// 为图层创建渲染器
        /// </summary>
        /// <param name="layer">要创建渲染器的图层</param>
        /// <returns>创建的渲染器，如果创建失败返回null</returns>
        ILayerRenderer CreateRenderer(IMapLayer layer);
        
        /// <summary>
        /// 移除指定图层的渲染器
        /// </summary>
        /// <param name="layerId">图层ID</param>
        void RemoveRenderer(string layerId);
        
        /// <summary>
        /// 移除指定图层的渲染器
        /// </summary>
        /// <param name="layer">图层对象</param>
        void RemoveRenderer(IMapLayer layer);
        
        /// <summary>
        /// 获取指定图层的渲染器
        /// </summary>
        /// <param name="layerId">图层ID</param>
        /// <returns>找到的渲染器，如果不存在返回null</returns>
        ILayerRenderer GetRenderer(string layerId);
        
        /// <summary>
        /// 获取指定图层的渲染器
        /// </summary>
        /// <param name="layer">图层对象</param>
        /// <returns>找到的渲染器，如果不存在返回null</returns>
        ILayerRenderer GetRenderer(IMapLayer layer);
        
        /// <summary>
        /// 更新所有渲染器
        /// </summary>
        void UpdateAll();
        
        /// <summary>
        /// 设置图层渲染器的可见性
        /// </summary>
        /// <param name="layerId">图层ID</param>
        /// <param name="visible">是否可见</param>
        void SetRendererVisibility(string layerId, bool visible);
        
        /// <summary>
        /// 设置图层渲染器的可见性
        /// </summary>
        /// <param name="layer">图层对象</param>
        /// <param name="visible">是否可见</param>
        void SetRendererVisibility(IMapLayer layer, bool visible);
        
        /// <summary>
        /// 获取指定层级的容器Transform
        /// </summary>
        /// <param name="layer">渲染层级</param>
        /// <returns>容器Transform</returns>
        Transform GetLayerContainer(RenderLayer layer);
        
        /// <summary>
        /// 获取指定层级的排序顺序
        /// </summary>
        /// <param name="layer">渲染层级</param>
        /// <param name="sublayerOffset">子层偏移量</param>
        /// <returns>排序顺序值</returns>
        int GetLayerSortingOrder(RenderLayer layer, int sublayerOffset = 0);
        
        /// <summary>
        /// 获取所有已注册的图层渲染器
        /// </summary>
        IEnumerable<ILayerRenderer> GetAllRenderers();
    }
} 