

namespace MapEditor.Core
{
    /// <summary>
    /// 渲染系统中的 Chunk 代理，负责生成/缓存与数据 Chunk 对应的渲染资源。
    /// </summary>
    public abstract class ChunkRenderProxy
    {
        public ChunkCoord Coord { get; protected set; }
        protected int lastRenderedVersion = -1;
        protected LayerRenderer layerRenderer;

        protected ChunkRenderProxy(ChunkCoord coord, LayerRenderer layerRenderer)
        {
            Coord = coord;
            this.layerRenderer = layerRenderer;
        }

        /// <summary>
        /// 当 Chunk 数据版本变化时，重新构建渲染内容。
        /// </summary>
        public void RebuildIfNeeded(ChunkBase chunk)
        {
            if (chunk == null) return;
            if (chunk.Version == lastRenderedVersion) return;
            InternalRebuild(chunk);
            lastRenderedVersion = (int)chunk.Version;
        }

        protected abstract void InternalRebuild(ChunkBase chunk);

        /// <summary>
        /// 释放渲染资源。
        /// </summary>
        public abstract void Dispose();

        // 可选可见性控制（由 LayerRenderer 调度）
        public virtual void SetVisibility(bool visible) { }
    }
} 