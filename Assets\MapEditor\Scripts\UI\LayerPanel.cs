using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using System;
using MapEditor.Tools;
using MapEditor.Event;

// #if false  // 若IDE报错，可取消注释以下占位声明。实际实现见 LayerSettingsDialog.cs
// class LayerSettingsDialog : MapEditor.UI.UIPanel { public override string PanelId => ""; public override string DisplayName => ""; }
// #endif

namespace MapEditor.UI
{
    /// <summary>
    /// 图层面板，显示和管理地图图层
    /// </summary>
    public class LayerPanel : UIPanel
    {
        public override string PanelId => "Layers";
        public override string DisplayName => "图层";
        
        private VisualElement layerListContainer;
        private Button addLayerButton;
        private Button hideButton; // 隐藏按钮
        private readonly Dictionary<string, LayerListItem> layerItems = new();
        private MapService _mapService; 
        private AddLayerDialog currentAddLayerDialog; // 当前显示的添加图层对话框
        private bool isHidden = false; // 面板隐藏状态
        
        public LayerPanel(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
        }
        
        public override void Initialize()
        {
            _mapService = MapEditorCore.Instance.GetService<MapService>();
            var eventService = MapEditorCore.Instance.EventSystem;

            layerListContainer = FindElement<VisualElement>("LayerListContainer");
            addLayerButton = FindElement<Button>("AddLayerButton");
            
            if (layerListContainer == null)
            {
                Debug.LogError("LayerListContainer not found in layers template");
                return;
            }
            
            // 注册添加图层按钮事件
            if (addLayerButton != null)
            {
                addLayerButton.clicked += OnAddLayerButtonClicked;
            }
            else
            {
                // 尝试查找子元素中带有特定类或文本的按钮
                var buttons = root.Query<Button>().ToList();
                addLayerButton = buttons.Find(b => b.text == "添加图层");
                
                if (addLayerButton != null)
                {
                    addLayerButton.clicked += OnAddLayerButtonClicked;
                }
                else
                {
                    Debug.LogWarning("添加图层按钮未找到");
                }
            }
            
            // 创建隐藏按钮
            CreateHideButton();
            
            // 注册图层相关事件
            eventService.Subscribe<LayerAddedEvent>(OnLayerAdded);
            eventService.Subscribe<LayerRemovedEvent>(OnLayerRemoved);
            eventService.Subscribe<LayerChangedEvent>(OnLayerChanged);
            eventService.Subscribe<MapCreatedEvent>(OnMapCreated);
            eventService.Subscribe<MapLoadedEvent>(OnMapLoaded);
            eventService.Subscribe<LayerCreateFailedEvent>(OnLayerCreateFailed);
            
            // 监听面板可见性变更事件，同步内部状态
            eventService.Subscribe<LayerPanelVisibilityChangedEvent>(OnPanelVisibilityChanged);
            
            // 初始化图层列表
            RefreshLayerList();
        }

        private void OnMapCreated(MapCreatedEvent @event)
        {
            RefreshLayerList();
        }

        private void OnMapLoaded(MapLoadedEvent evt)
        {
            RefreshLayerList();
        }

        public override void UpdatePanel()
        {
            RefreshLayerList();
        }
        
        private void RefreshLayerList()
        {
            // 如果容器是 ScrollView，使用其 contentContainer；否则直接使用容器本身
            var container = (layerListContainer as ScrollView)?.contentContainer ?? layerListContainer;

            container.Clear();
            layerItems.Clear();
            var layers = _mapService.GetAllLayers();
            Debug.Log("RefreshLayerList: " + layers.Length);

            if (layers.Length == 0)
            {
                return;
            }

            // 显示顺序应与渲染一致：sortingOrder 越大越靠前，面板中应该排在上方
            var sortedLayers = layers.OrderByDescending(layer => layer.Order).ToArray();

            // 创建图层列表项
            foreach (var layer in sortedLayers)
            {
                CreateLayerListItem(layer);
            }
        }
        
        private void CreateLayerListItem(IMapLayer layer)
        {
            Debug.Log("CreateLayerListItem: " + layer.LayerName);
            if (layerItems.ContainsKey(layer.LayerId))
            {
                return;
            }

            var item = new LayerListItem(layer, _mapService, MapEditorCore.Instance.EventSystem, UIManager.Instance as IUIManager);

            // 如果该图层是当前活动图层，则标记为选中状态
            var activeLayer = _mapService?.GetActiveLayer();
            if (activeLayer != null && activeLayer.LayerId == layer.LayerId)
            {
                item.AddToClassList("selected");
            }

            // 同样使用 ScrollView 的 contentContainer 作为父级
            var parent = (layerListContainer as ScrollView)?.contentContainer ?? layerListContainer;
            parent.Add(item);
            layerItems[layer.LayerId] = item;
        }
        
        private void OnAddLayerButtonClicked()
        {
            // 显示添加图层对话框
            var addLayerDialog = UIManager.Instance?.GetPanel("addLayerDialog") as AddLayerDialog;
            if (addLayerDialog == null)
            {
                var template = Resources.Load<VisualTreeAsset>("UI/AddLayerDialog");
                if (template == null)
                {
                    Debug.LogError("找不到AddLayerDialog模板文件");
                    RequestShowMessage("错误", "无法加载添加图层对话框", MessageType.Error);
                    return;
                }
                
                addLayerDialog = new AddLayerDialog(null, template);
                RequestRegisterPanel(addLayerDialog);
            }
            
            // 保存当前对话框引用
            currentAddLayerDialog = addLayerDialog;
            
            // 为避免重复注册，先移除已存在的事件处理器，再添加一次
            addLayerDialog.OnLayerCreated -= OnNewLayerCreated;
            addLayerDialog.OnCancelled -= OnAddLayerCancelled;

            addLayerDialog.OnLayerCreated += OnNewLayerCreated;
            addLayerDialog.OnCancelled += OnAddLayerCancelled;
            
            // 显示对话框
            addLayerDialog.ShowDialog();
        }
        
        /// <summary>
        /// 处理新图层创建
        /// </summary>
        private void OnNewLayerCreated(AddLayerDialogData layerData)
        {
            if (_mapService != null)
            {
                var newLayer = _mapService.CreateLayer(layerData.layerName, layerData.layerType);
                if (newLayer != null)
                {
                    // 设置图层属性
                    newLayer.IsVisible = layerData.isVisible;
                    newLayer.IsLocked = layerData.isLocked;
                    
                    // 创建成功，隐藏对话框
                    if (currentAddLayerDialog != null)
                    {
                        currentAddLayerDialog.IsVisible = false;
                        currentAddLayerDialog = null;
                    }
                    
                    Debug.Log($"成功创建图层: {newLayer.LayerName}");
                }
                // 注意：如果创建失败(newLayer == null)，对话框保持显示，等待LayerCreateFailedEvent事件
            }
            else
            {
                Debug.LogError("MapService 未初始化");
                RequestShowMessage("错误", "MapService 未初始化", MessageType.Error);
            }
        }
        
        /// <summary>
        /// 处理添加图层取消
        /// </summary>
        private void OnAddLayerCancelled()
        {
            // 清除对话框引用
            currentAddLayerDialog = null;
            Debug.Log("用户取消添加图层");
        }
        
        private void OnLayerAdded(LayerAddedEvent evt)
        {
            // 每当添加图层时，刷新整个列表以确保正确的排序
            RefreshLayerList();
        }
        
        private void OnLayerRemoved(LayerRemovedEvent evt)
        {
            if (layerItems.TryGetValue(evt.LayerId, out var item))
            {
                layerListContainer.Remove(item);
                layerItems.Remove(evt.LayerId);
            }
        }
        
        private void OnLayerChanged(LayerChangedEvent evt)
        {
            // 列表项的属性（名称、可见性等）可能已变，且顺序也可能改变，故直接刷新列表
            RefreshLayerList();
        }
        
        private void OnLayerCreateFailed(LayerCreateFailedEvent evt)
        {
            RequestShowMessage("错误", evt.Reason, MessageType.Error);
        }
        
        /// <summary>
        /// 获取图层类型对应的渲染层级
        /// </summary>
        private RenderLayer GetRenderLayerFromLayerType(LayerType layerType)
        {
            switch (layerType)
            {
                case LayerType.Ground:
                    return RenderLayer.Ground;
                case LayerType.Object:
                    return RenderLayer.Object;
                case LayerType.Decoration:
                    return RenderLayer.Preview;
                case LayerType.Logic:
                    return RenderLayer.UI;
                case LayerType.Grid:
                    return RenderLayer.Grid;
                default:
                    return RenderLayer.Ground;
            }
        }
        
        /// <summary>
        /// 创建隐藏按钮
        /// </summary>
        private void CreateHideButton()
        {
            // 在图层标题旁边添加隐藏按钮
            var titleContainer = root.Q<VisualElement>(className: "layer-title-container");
            if (titleContainer == null)
            {
                // 如果没有找到标题容器，查找包含"图层"文本的Label
                var titleLabel = root.Q<Label>();
                if (titleLabel != null && titleLabel.text == "图层")
                {
                    // 记住原始父容器和插入位置
                    var originalParent = titleLabel.parent;
                    int originalIndex = originalParent.IndexOf(titleLabel);
                    
                    // 创建一个容器来包含标题和按钮
                    var newTitleContainer = new VisualElement();
                    newTitleContainer.style.flexDirection = FlexDirection.Row;
                    newTitleContainer.style.justifyContent = Justify.SpaceBetween;
                    newTitleContainer.style.alignItems = Align.Center;
                    newTitleContainer.style.marginBottom = 10;
                    
                    // 将标题移动到新容器中
                    originalParent.Remove(titleLabel);
                    newTitleContainer.Add(titleLabel);
                    
                    // 创建隐藏按钮
                    hideButton = new Button()
                    {
                        text = "◀"
                    };
                    hideButton.style.width = 20;
                    hideButton.style.height = 20;
                    hideButton.style.fontSize = 12;
                    
                    // 使用与UI主题一致的颜色
                    hideButton.style.backgroundColor = new StyleColor(new Color(0.236f, 0.236f, 0.236f, 1f)); // --color-background-light (#3C3C3C)
                    hideButton.style.borderTopLeftRadius = 3;
                    hideButton.style.borderTopRightRadius = 3;
                    hideButton.style.borderBottomLeftRadius = 3;
                    hideButton.style.borderBottomRightRadius = 3;
                    hideButton.style.borderTopWidth = 1;
                    hideButton.style.borderRightWidth = 1;
                    hideButton.style.borderBottomWidth = 1;
                    hideButton.style.borderLeftWidth = 1;
                    hideButton.style.borderTopColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f)); // --color-border (#555555)
                    hideButton.style.borderRightColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
                    hideButton.style.borderBottomColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
                    hideButton.style.borderLeftColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
                    hideButton.style.color = new StyleColor(new Color(0.878f, 0.878f, 0.878f, 1f)); // --color-text (#E0E0E0)
                    
                    // 添加鼠标悬停效果
                    hideButton.RegisterCallback<MouseEnterEvent>(evt => {
                        hideButton.style.backgroundColor = new StyleColor(new Color(0.286f, 0.565f, 0.886f, 0.2f)); // accent color with opacity
                        hideButton.style.borderTopColor = new StyleColor(new Color(0.286f, 0.565f, 0.886f, 1f)); // --color-accent (#4990E2)
                        hideButton.style.borderRightColor = new StyleColor(new Color(0.286f, 0.565f, 0.886f, 1f));
                        hideButton.style.borderBottomColor = new StyleColor(new Color(0.286f, 0.565f, 0.886f, 1f));
                        hideButton.style.borderLeftColor = new StyleColor(new Color(0.286f, 0.565f, 0.886f, 1f));
                    });
                    hideButton.RegisterCallback<MouseLeaveEvent>(evt => {
                        hideButton.style.backgroundColor = new StyleColor(new Color(0.236f, 0.236f, 0.236f, 1f)); // restore original
                        hideButton.style.borderTopColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f)); // restore original border
                        hideButton.style.borderRightColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
                        hideButton.style.borderBottomColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
                        hideButton.style.borderLeftColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
                    });
                    
                    hideButton.clicked += TogglePanelVisibility;
                    
                    newTitleContainer.Add(hideButton);
                    
                    // 将新容器插入到原标题的位置
                    originalParent.Insert(originalIndex, newTitleContainer);
                }
            }
        }
        
        /// <summary>
        /// 切换面板可见性
        /// </summary>
        private void TogglePanelVisibility()
        {
            isHidden = !isHidden;
            
            // 发布可见性变更事件
            _eventSystem?.Publish(new LayerPanelVisibilityChangedEvent(isHidden));
            
            Debug.Log($"图层面板可见性变更: {(isHidden ? "隐藏" : "显示")}");
        }
        
        /// <summary>
        /// 处理面板可见性变更事件（用于同步状态）
        /// </summary>
        private void OnPanelVisibilityChanged(LayerPanelVisibilityChangedEvent evt)
        {
            // 同步内部状态，避免状态不一致
            if (isHidden != evt.IsHidden)
            {
                isHidden = evt.IsHidden;
                
                // 更新隐藏按钮文本
                if (hideButton != null)
                {
                    hideButton.text = isHidden ? "▶" : "◀";
                }
                
                Debug.Log($"图层面板状态同步: {(isHidden ? "隐藏" : "显示")}");
            }
        }
        
        private void ShowLayerSettings(IMapLayer layer)
        {
            if (layer == null) return;
            var dialog = UIManager.Instance?.GetPanel("layerSettingsDialog") as LayerSettingsDialog;
            if (dialog == null)
            {
                var tpl = Resources.Load<UnityEngine.UIElements.VisualTreeAsset>("UI/LayerSettingsDialog");
                dialog = new LayerSettingsDialog(UIManager.Instance, tpl);
                UIManager.Instance.RegisterPanel(dialog);
            }
            dialog.ShowDialog(layer);
        }
        
        // 图层列表项控件
        private class LayerListItem : VisualElement
        {
            private readonly IMapLayer layer;
            private readonly MapService _mapService;
            private readonly IEventSystem _eventSystem;
            private readonly IUIManager _uiManager;

            private readonly Toggle visibilityToggle;
            private readonly Toggle lockToggle;
            private readonly Label nameLabel;
            private readonly Button deleteButton;
            private readonly Button expandButton;
            private readonly VisualElement objectListContainer;
            private bool isExpanded = false;
            private ObjectService objectService;
            private SelectionTool selectionTool;
            
            // 存储当前选中的对象ID列表
            private readonly HashSet<string> selectedObjectIds = new HashSet<string>();
            
            public LayerListItem(IMapLayer layer, MapService mapService, IEventSystem eventSystem, IUIManager uiManager)
            {
                this.layer = layer;
                this._mapService = mapService;
                this._eventSystem = eventSystem;
                this._uiManager = uiManager;

                // 获取服务
                this.objectService = MapEditorCore.Instance.GetService<ObjectService>();
                var toolService = MapEditorCore.Instance.GetService<ToolService>();
                this.selectionTool = toolService?.GetTool("SelectionTool") as Tools.SelectionTool;
                
                this.AddToClassList("layer-item");
                
                // 创建图层控件
                var controlsContainer = new VisualElement();
                controlsContainer.AddToClassList("layer-controls");
                
                // 可见性切换
                visibilityToggle = new Toggle();
                visibilityToggle.AddToClassList("visibility-toggle");
                visibilityToggle.value = layer.IsVisible;
                visibilityToggle.RegisterValueChangedCallback(evt =>
                {
                    _mapService?.SetLayerVisibility(layer, evt.newValue);
                });
                
                // 锁定切换
                lockToggle = new Toggle();
                lockToggle.AddToClassList("lock-toggle");
                lockToggle.value = layer.IsLocked;
                lockToggle.RegisterValueChangedCallback(evt =>
                {
                    layer.IsLocked = evt.newValue;
                });
                
                // 特殊处理网格层：禁用锁定功能
                if (layer.Type == LayerType.Grid)
                {
                    lockToggle.SetEnabled(false);
                    lockToggle.value = false;
                }
                
                // 删除按钮
                deleteButton = new Button()
                {
                    text = "✖"
                };
                deleteButton.AddToClassList("delete-button");
                deleteButton.clicked += () =>
                {
                    _uiManager?.ShowConfirmDialog("删除图层", $"确定要删除图层 {layer.LayerName} 吗？", () =>
                    {
                        _mapService?.DeleteLayer(layer.LayerId);
                    });
                };
                
                // 特殊处理网格层：禁用删除功能
                if (layer.Type == LayerType.Grid)
                {
                    deleteButton.SetEnabled(false);
                    deleteButton.style.opacity = 0.3f;
                }
                
                // 展开/折叠按钮 (只对对象图层显示)
                if (layer.Type == LayerType.Object)
                {
                    expandButton = new Button()
                    {
                        text = "▶"
                    };
                    expandButton.AddToClassList("expand-button");
                    expandButton.style.width = 16;
                    expandButton.style.height = 16;
                    expandButton.style.fontSize = 10;
                    expandButton.style.backgroundColor = StyleKeyword.Null;
                    expandButton.style.borderLeftWidth = 0;
                    expandButton.style.borderRightWidth = 0;
                    expandButton.style.borderTopWidth = 0;
                    expandButton.style.borderBottomWidth = 0;
                    expandButton.clicked += ToggleExpanded;
                }
                else
                {
                    // 非对象图层用空占位符
                    expandButton = new Button() { text = "" };
                    expandButton.style.width = 16;
                    expandButton.style.height = 16;
                    expandButton.style.backgroundColor = StyleKeyword.Null;
                    expandButton.style.borderLeftWidth = 0;
                    expandButton.style.borderRightWidth = 0;
                    expandButton.style.borderTopWidth = 0;
                    expandButton.style.borderBottomWidth = 0;
                    expandButton.SetEnabled(false);
                }
                
                // 图层名称
                nameLabel = new Label(layer.LayerName);
                nameLabel.AddToClassList("layer-name");
                
                // 创建名称编辑框（默认隐藏）
                var nameEditField = new TextField();
                nameEditField.AddToClassList("layer-name-edit");
                nameEditField.style.display = DisplayStyle.None;
                nameEditField.value = layer.LayerName;
                
                // 名称容器，包含标签和编辑框
                var nameContainer = new VisualElement();
                nameContainer.AddToClassList("layer-name-container");
                nameContainer.style.flexGrow = 1;
                nameContainer.Add(nameLabel);
                nameContainer.Add(nameEditField);
                
                // 双击名称标签进入编辑模式（网格层除外）
                if (layer.Type != LayerType.Grid)
                {
                    nameLabel.RegisterCallback<MouseDownEvent>(evt =>
                    {
                        if (evt.clickCount == 2)
                        {
                            StartEditingName(nameLabel, nameEditField);
                            evt.StopPropagation();
                        }
                    });
                }
                
                // 编辑框的事件处理
                nameEditField.RegisterCallback<FocusOutEvent>(evt =>
                {
                    FinishEditingName(nameLabel, nameEditField);
                });
                
                nameEditField.RegisterCallback<KeyDownEvent>(evt =>
                {
                    if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
                    {
                        FinishEditingName(nameLabel, nameEditField);
                        evt.StopPropagation();
                    }
                    else if (evt.keyCode == KeyCode.Escape)
                    {
                        CancelEditingName(nameLabel, nameEditField);
                        evt.StopPropagation();
                    }
                });
                
                // 图层类型图标
                var typeIcon = new VisualElement();
                typeIcon.AddToClassList("layer-type-icon");
                typeIcon.AddToClassList(layer.Type.ToString().ToLower() + "-layer-icon");
                
                controlsContainer.Add(visibilityToggle);
                controlsContainer.Add(lockToggle);
                controlsContainer.Add(deleteButton);
                
                // 主容器（图层信息）
                var mainContainer = new VisualElement();
                mainContainer.AddToClassList("layer-main");
                mainContainer.style.flexDirection = FlexDirection.Row;
                mainContainer.style.alignItems = Align.Center;
                
                mainContainer.Add(expandButton);
                mainContainer.Add(typeIcon);
                mainContainer.Add(nameContainer);
                mainContainer.Add(controlsContainer);
                
                // 对象列表容器
                objectListContainer = new VisualElement();
                objectListContainer.AddToClassList("layer-object-list");
                objectListContainer.style.display = DisplayStyle.None; // 默认隐藏
                objectListContainer.style.flexDirection = FlexDirection.Column;
                
                this.Add(mainContainer);
                this.Add(objectListContainer);
                
                // 注册拖拽事件(简化版本，实际需要实现完整拖拽)
                mainContainer.RegisterCallback<MouseDownEvent>(OnMouseDown);
                
                // 如果是对象图层，初始化对象列表
                if (layer.Type == LayerType.Object)
                {
                    RefreshObjectList();
                    // 订阅选择变更事件以更新对象列表
                    _eventSystem?.Subscribe<SelectionChangedEvent>(OnSelectionChanged);
                    // 订阅对象重命名事件
                    _eventSystem?.Subscribe<ObjectRenamedEvent>(OnObjectRenamed);
                }

                // 右键打开图层设置对话框
                mainContainer.RegisterCallback<MouseDownEvent>(evt =>
                {
                    if (evt.button == 1) // 1 = 右键
                    {
                        ShowLayerSettingsDialog();
                        evt.StopPropagation();
                    }
                });
            }
            
            public void UpdateFromLayer(IMapLayer updatedLayer)
            {
                if (updatedLayer == null) return;
                
                nameLabel.text = updatedLayer.LayerName;
                visibilityToggle.value = updatedLayer.IsVisible;
                lockToggle.value = updatedLayer.IsLocked;
            }
            
            private void OnMouseDown(MouseDownEvent evt)
            {
                // 通过LayerManager设置为当前活动图层
                if (_mapService != null)
                {
                    _mapService.SetActiveLayer(layer.LayerId);
                    Debug.Log($"设置活动图层: {layer.LayerName}");
                }

                // 先取消同级元素的选中状态，确保只有一个图层处于选中状态
                if (this.parent != null)
                {
                    foreach (var child in this.parent.Children())
                    {
                        child.RemoveFromClassList("selected");
                    }
                }
                // 为当前项添加选中样式
                this.AddToClassList("selected");
            }
            
            private void ToggleExpanded()
            {
                isExpanded = !isExpanded;
                
                if (isExpanded)
                {
                    expandButton.text = "▼";
                    objectListContainer.style.display = DisplayStyle.Flex;
                    RefreshObjectList();
                }
                else
                {
                    expandButton.text = "▶";
                    objectListContainer.style.display = DisplayStyle.None;
                }
            }
            
            private void RefreshObjectList()
            {
                if (layer.Type != LayerType.Object || !isExpanded) return;
                
                objectListContainer.Clear();
                
                // 直接获取该图层的对象列表，避免切换活动图层导致的副作用
                if (objectService != null)
                {
                    var objects = objectService.GetObjectsInLayer(layer.LayerId);
                    
                    Debug.Log($"图层 {layer.LayerName} 中找到 {objects.Count} 个对象");
                    
                    if (objects.Count == 0)
                    {
                        var emptyLabel = new Label("No objects in this layer");
                        emptyLabel.AddToClassList("empty-object-list");
                        objectListContainer.Add(emptyLabel);
                    }
                    else
                    {
                        foreach (var obj in objects)
                        {
                            CreateObjectListItem(obj);
                        }
                    }
                }
                else
                {
                    Debug.LogWarning("无法获取ObjectService实例");
                }
            }
            
            private void CreateObjectListItem(Data.Chunks.ObjectInstance obj)
            {
                var item = new VisualElement();
                item.AddToClassList("ps-object-item");
                item.style.flexDirection = FlexDirection.Row;
                item.style.alignItems = Align.Center;
                item.style.height = 22;
                item.style.paddingLeft = 8;
                item.style.paddingRight = 8;
                item.style.paddingTop = 2;
                item.style.paddingBottom = 2;
                
                // 检查是否被选中
                bool isSelected = selectedObjectIds.Contains(obj.InstanceId);
                if (isSelected)
                {
                    item.AddToClassList("selected");
                    item.style.backgroundColor = new StyleColor(new Color(0.2f, 0.4f, 0.8f, 0.3f));
                }
                
                // 对象图标
                var objectIcon = new VisualElement();
                objectIcon.AddToClassList("ps-object-icon");
                objectIcon.style.width = 14;
                objectIcon.style.height = 14;
                objectIcon.style.marginRight = 6;
                objectIcon.style.backgroundColor = new StyleColor(new Color(0.8f, 0.4f, 0.2f)); // 橙色表示对象
                objectIcon.style.borderTopLeftRadius = 2;
                objectIcon.style.borderTopRightRadius = 2;
                objectIcon.style.borderBottomLeftRadius = 2;
                objectIcon.style.borderBottomRightRadius = 2;
                objectIcon.style.borderLeftWidth = 1;
                objectIcon.style.borderRightWidth = 1;
                objectIcon.style.borderTopWidth = 1;
                objectIcon.style.borderBottomWidth = 1;
                objectIcon.style.borderLeftColor = new StyleColor(new Color(1f, 1f, 1f, 0.2f));
                objectIcon.style.borderRightColor = new StyleColor(new Color(1f, 1f, 1f, 0.2f));
                objectIcon.style.borderTopColor = new StyleColor(new Color(1f, 1f, 1f, 0.2f));
                objectIcon.style.borderBottomColor = new StyleColor(new Color(1f, 1f, 1f, 0.2f));
                
                // 双击图标移动相机到对象位置
                objectIcon.RegisterCallback<MouseDownEvent>(evt => {
                    if (evt.clickCount == 2)
                    {
                        objectService?.FocusOnObject(obj);
                        evt.StopPropagation();
                    }
                });
                
                // 对象名称标签
                var nameLabel = new Label(obj.GetDisplayName());
                nameLabel.AddToClassList("ps-object-name");
                nameLabel.style.fontSize = 11;
                nameLabel.style.color = new StyleColor(Color.white);
                nameLabel.style.flexGrow = 1;
                nameLabel.style.unityTextAlign = TextAnchor.MiddleLeft;
                
                // 创建名称编辑框（默认隐藏）
                var nameEditField = new TextField();
                nameEditField.AddToClassList("ps-object-name-edit");
                nameEditField.style.display = DisplayStyle.None;
                nameEditField.style.fontSize = 11;
                nameEditField.value = obj.GetDisplayName();
                
                // 双击名称标签进入编辑模式
                nameLabel.RegisterCallback<MouseDownEvent>(evt => {
                    if (evt.clickCount == 2)
                    {
                        StartEditingObjectName(nameLabel, nameEditField, obj);
                        evt.StopPropagation();
                    }
                });
                
                // 编辑框的事件处理
                nameEditField.RegisterCallback<FocusOutEvent>(evt => {
                    FinishEditingObjectName(nameLabel, nameEditField, obj);
                });
                
                nameEditField.RegisterCallback<KeyDownEvent>(evt => {
                    if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
                    {
                        FinishEditingObjectName(nameLabel, nameEditField, obj);
                        evt.StopPropagation();
                    }
                    else if (evt.keyCode == KeyCode.Escape)
                    {
                        CancelEditingObjectName(nameLabel, nameEditField, obj);
                        evt.StopPropagation();
                    }
                });
                
                // 点击整个行来选择对象
                item.RegisterCallback<MouseDownEvent>(evt => {
                    // 如果不是双击名称，则选择对象
                    if (evt.target != nameLabel || evt.clickCount == 1)
                    {
                        Debug.Log($"点击选择对象: {obj.InstanceId}");
                        // 发布选择事件，让SelectionTool处理
                        _eventSystem?.Publish(new RequestSelectObjectEvent 
                        { 
                            ObjectId = obj.InstanceId,
                            MultiSelect = false 
                        });
                        evt.StopPropagation(); // 防止触发图层选择
                    }
                });
                
                // 可见性控制（小眼睛图标）
                var visibilityIcon = new VisualElement();
                visibilityIcon.AddToClassList("ps-visibility-icon");
                visibilityIcon.style.width = 14;
                visibilityIcon.style.height = 14;
                visibilityIcon.style.marginRight = 4;
                visibilityIcon.style.backgroundColor = new StyleColor(new Color(1f, 1f, 1f, 0.1f));
                visibilityIcon.style.borderTopLeftRadius = 2;
                visibilityIcon.style.borderTopRightRadius = 2;
                visibilityIcon.style.borderBottomLeftRadius = 2;
                visibilityIcon.style.borderBottomRightRadius = 2;
                // 这里可以添加眼睛图标的背景图片
                
                item.Add(objectIcon);
                item.Add(nameLabel);
                item.Add(nameEditField);
                item.Add(visibilityIcon);
                
                objectListContainer.Add(item);
            }
            
            private void OnSelectionChanged(SelectionChangedEvent evt)
            {
                // 处理选择事件，且当前图层已展开
                if (isExpanded && layer.Type == LayerType.Object)
                {
                    // 更新选中对象ID列表
                    selectedObjectIds.Clear();
                    if (evt.SelectedObjectInstances != null)
                    {
                        foreach (var obj in evt.SelectedObjectInstances)
                        {
                            selectedObjectIds.Add(obj.InstanceId);
                        }
                    }
                    
                    RefreshObjectList();
                }
            }
            
            /// <summary>
            /// 处理对象重命名事件
            /// </summary>
            private void OnObjectRenamed(ObjectRenamedEvent evt)
            {
                if (isExpanded && layer.Type == LayerType.Object)
                {
                    RefreshObjectList();
                }
            }
            
            /// <summary>
            /// 开始编辑图层名称
            /// </summary>
            private void StartEditingName(Label nameLabel, TextField nameEditField)
            {
                nameLabel.style.display = DisplayStyle.None;
                nameEditField.style.display = DisplayStyle.Flex;
                nameEditField.value = layer.LayerName;
                nameEditField.Focus();
                nameEditField.SelectAll();
            }
            
            /// <summary>
            /// 完成编辑图层名称
            /// </summary>
            private void FinishEditingName(Label nameLabel, TextField nameEditField)
            {
                string newName = nameEditField.value.Trim();
                if (!string.IsNullOrEmpty(newName) && newName != layer.LayerName)
                {
                    // 调用MapService的重命名方法
                    if (_mapService != null)
                    {
                        _mapService.RenameLayer(layer.LayerId, newName);
                        nameLabel.text = newName;
                    }
                }
                
                nameLabel.style.display = DisplayStyle.Flex;
                nameEditField.style.display = DisplayStyle.None;
            }
            
            /// <summary>
            /// 取消编辑图层名称
            /// </summary>
            private void CancelEditingName(Label nameLabel, TextField nameEditField)
            {
                nameEditField.value = layer.LayerName;
                nameLabel.style.display = DisplayStyle.Flex;
                nameEditField.style.display = DisplayStyle.None;
            }
            
            /// <summary>
            /// 开始编辑对象名称
            /// </summary>
            private void StartEditingObjectName(Label nameLabel, TextField nameEditField, Data.Chunks.ObjectInstance obj)
            {
                nameLabel.style.display = DisplayStyle.None;
                nameEditField.style.display = DisplayStyle.Flex;
                nameEditField.value = obj.GetDisplayName();
                nameEditField.Focus();
                nameEditField.SelectAll();
            }
            
            /// <summary>
            /// 完成编辑对象名称
            /// </summary>
            private void FinishEditingObjectName(Label nameLabel, TextField nameEditField, Data.Chunks.ObjectInstance obj)
            {
                string newName = nameEditField.value.Trim();
                if (newName != obj.GetDisplayName())
                {
                    // 使用ObjectService重命名
                    objectService?.RenameObject(obj, newName);
                    nameLabel.text = newName;
                }
                
                nameLabel.style.display = DisplayStyle.Flex;
                nameEditField.style.display = DisplayStyle.None;
            }
            
            /// <summary>
            /// 取消编辑对象名称
            /// </summary>
            private void CancelEditingObjectName(Label nameLabel, TextField nameEditField, Data.Chunks.ObjectInstance obj)
            {
                nameEditField.value = obj.GetDisplayName();
                nameLabel.style.display = DisplayStyle.Flex;
                nameEditField.style.display = DisplayStyle.None;
            }

            private RenderLayer GetRenderLayerFromLayerType(LayerType layerType)
            {
                switch (layerType)
                {
                    case LayerType.Ground:
                        return RenderLayer.Ground;
                    case LayerType.Object:
                        return RenderLayer.Object;
                    case LayerType.Decoration:
                        return RenderLayer.Preview;
                    case LayerType.Logic:
                        return RenderLayer.UI;
                    default:
                        return RenderLayer.Ground;
                }
            }

            /// <summary>
            /// 显示图层设置对话框
            /// </summary>
            private void ShowLayerSettingsDialog()
            {
                var dialog = UIManager.Instance?.GetPanel("layerSettingsDialog") as LayerSettingsDialog;
                if (dialog == null)
                {
                    var tpl = Resources.Load<UnityEngine.UIElements.VisualTreeAsset>("UI/LayerSettingsDialog");
                    dialog = new LayerSettingsDialog(UIManager.Instance, tpl);
                    UIManager.Instance.RegisterPanel(dialog);
                }
                dialog.ShowDialog(layer);
            }
        }
    }
    
    // 图层相关事件
    public class LayerAddedEvent
    {
        public IMapLayer Layer { get; set; }
    }
    
    public class LayerRemovedEvent
    {
        public string LayerId { get; set; }
    }
    
    public class LayerChangedEvent
    {
        public IMapLayer Layer { get; set; }
    }
}