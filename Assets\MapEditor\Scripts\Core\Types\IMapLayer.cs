using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 地图图层接口
    /// </summary>
    public interface IMapLayer
    {
        /// <summary>
        /// 图层唯一标识符
        /// </summary>
        string LayerId { get; set; }
        
        /// <summary>
        /// 图层名称
        /// </summary>
        string LayerName { get; set; }
        
        /// <summary>
        /// 图层类型
        /// </summary>
        LayerType Type { get; }
        
        /// <summary>
        /// 图层是否可见
        /// </summary>
        bool IsVisible { get; set; }
        
        /// <summary>
        /// 图层是否锁定
        /// </summary>
        bool IsLocked { get; set; }
        
        /// <summary>
        /// 图层顺序
        /// </summary>
        int Order { get; set; }
        
        /// <summary>
        /// Chunk 的边长（世界单位）
        /// </summary>
        int ChunkSize { get; }

        /// <summary>
        /// 图层大小
        /// </summary>
        Vector2Int LayerSize { get; set; }
        
        /// <summary>
        /// 在给定包围盒内获取可见的 Chunk 列表。
        /// </summary>
        /// <param name="viewBounds">视口的世界空间包围盒</param>
        /// <returns>可见的 Chunk 数据集合</returns>
        IEnumerable<ChunkBase> GetVisibleChunks(Bounds viewBounds);
    }
} 