using UnityEngine;

namespace MapEditor.Tools
{
    /// <summary>
    /// 可视化选择框，使用真实的GameObject和LineRenderer，带有缩放控制点
    /// </summary>
    public class VisualSelectionBox : MonoBehaviour
    {
        private LineRenderer lineRenderer;
        private GameObject[] scaleHandles = new GameObject[4]; // 四个角的控制点
        private GameObject rotationHandle; // 旋转控制点
        private const float handleSize = 0.15f; // 控制点大小
        private const float rotationHandleDistance = 0.5f; // 旋转控制点距离包围框的距离
        private Rect currentRect;
        private bool isVisible = false;
        private bool showHandles = true; // 是否显示控制点

        void Awake()
        {
            // 创建LineRenderer组件
            lineRenderer = gameObject.AddComponent<LineRenderer>();
            
            // 设置LineRenderer属性
            lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
            lineRenderer.startColor = Color.cyan;
            lineRenderer.endColor = Color.cyan;
            lineRenderer.startWidth = 0.05f;
            lineRenderer.endWidth = 0.05f;
            lineRenderer.positionCount = 5; // 矩形需要5个点（闭合）
            lineRenderer.useWorldSpace = true;
            lineRenderer.sortingOrder = 8500; // 使用UI区间
            
            // 设置为不可见
            lineRenderer.enabled = false;
            
            CreateScaleHandles();
            CreateRotationHandle();
        }

        /// <summary>
        /// 设置边框颜色
        /// </summary>
        public void SetColor(Color color)
        {
            if (lineRenderer != null)
            {
                lineRenderer.startColor = color;
                lineRenderer.endColor = color;
            }
        }

        /// <summary>
        /// 设置是否显示缩放控制点
        /// </summary>
        public void SetShowHandles(bool show)
        {
            showHandles = show;
            foreach (var handle in scaleHandles)
            {
                if (handle != null)
                    handle.SetActive(isVisible && showHandles);
            }
            
            if (rotationHandle != null)
                rotationHandle.SetActive(isVisible && showHandles);
        }

        private void CreateScaleHandles()
        {
            for (int i = 0; i < 4; i++)
            {
                GameObject handle = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                handle.name = $"ScaleHandle_{i}";
                handle.transform.SetParent(transform);
                handle.transform.localScale = Vector3.one * handleSize;
                
                // 设置材质为高亮颜色 (URP Unlit)
                var renderer = handle.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = CreateHandleMaterial(Color.yellow);
                    renderer.sortingOrder = 8501;
                }
                
                // 添加碰撞体用于检测点击
                var collider = handle.GetComponent<SphereCollider>();
                if (collider != null)
                {
                    collider.radius = handleSize * 1.5f; // 稍微放大碰撞体，便于点击
                }
                
                handle.SetActive(false);
                scaleHandles[i] = handle;
            }
        }

        public void SetRect(Rect rect)
        {
            currentRect = rect;
            UpdateLineRenderer();
            UpdateScaleHandles();
            UpdateRotationHandle();
        }

        public void SetVisible(bool visible)
        {
            isVisible = visible;
            lineRenderer.enabled = visible;
            
            // 如果隐藏选择框，清除当前矩形数据，避免下次显示时出现闪烁
            if (!visible)
            {
                currentRect = Rect.zero;
                // 立即更新渲染器以清除显示的内容
                UpdateLineRenderer();
                UpdateScaleHandles();
                UpdateRotationHandle();
            }
            
            foreach (var handle in scaleHandles)
            {
                if (handle != null)
                    handle.SetActive(visible && showHandles);
            }
            
            if (rotationHandle != null)
                rotationHandle.SetActive(visible && showHandles);
        }

        private void UpdateLineRenderer()
        {
            if (lineRenderer == null) return;

            // 计算矩形的四个角点
            Vector3[] positions = new Vector3[5];
            positions[0] = new Vector3(currentRect.xMin, currentRect.yMin, 0); // 左下
            positions[1] = new Vector3(currentRect.xMax, currentRect.yMin, 0); // 右下
            positions[2] = new Vector3(currentRect.xMax, currentRect.yMax, 0); // 右上
            positions[3] = new Vector3(currentRect.xMin, currentRect.yMax, 0); // 左上
            positions[4] = positions[0]; // 闭合回到起点

            lineRenderer.SetPositions(positions);
        }

        private void UpdateScaleHandles()
        {
            if (scaleHandles[0] == null) return;
            
            // 更新四个角落控制点的位置
            Vector3[] corners = {
                new Vector3(currentRect.xMin, currentRect.yMin, -0.1f), // 左下
                new Vector3(currentRect.xMax, currentRect.yMin, -0.1f), // 右下  
                new Vector3(currentRect.xMax, currentRect.yMax, -0.1f), // 右上
                new Vector3(currentRect.xMin, currentRect.yMax, -0.1f)  // 左上
            };
            
            for (int i = 0; i < 4; i++)
            {
                if (scaleHandles[i] != null)
                {
                    scaleHandles[i].transform.position = corners[i];
                }
            }
        }

        /// <summary>
        /// 检查给定的世界坐标是否在某个缩放控制点内
        /// </summary>
        public int GetHandleAtPosition(Vector2 worldPos)
        {
            for (int i = 0; i < 4; i++)
            {
                if (scaleHandles[i] != null && scaleHandles[i].activeInHierarchy)
                {
                    Vector2 handlePos = scaleHandles[i].transform.position;
                    float distance = Vector2.Distance(worldPos, handlePos);
                    if (distance <= handleSize * 1.5f) // 使用放大的碰撞范围
                    {
                        return i;
                    }
                }
            }
            return -1; // 没有命中任何控制点
        }

        /// <summary>
        /// 检查给定的世界坐标是否在旋转控制点内
        /// </summary>
        public bool GetRotationHandleAtPosition(Vector2 worldPos)
        {
            if (rotationHandle != null && rotationHandle.activeInHierarchy)
            {
                Vector2 handlePos = rotationHandle.transform.position;
                float distance = Vector2.Distance(worldPos, handlePos);
                float threshold = handleSize * 2.0f; // 增大检测范围
                
                return distance <= threshold;
            }
            return false;
        }

        /// <summary>
        /// 创建旋转控制点
        /// </summary>
        private void CreateRotationHandle()
        {
            GameObject handle = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            handle.name = "RotationHandle";
            handle.transform.SetParent(transform);
            handle.transform.localScale = Vector3.one * handleSize * 1.2f; // 稍大一些便于点击
            
            // 设置材质为绿色 (URP Unlit)
            var renderer = handle.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material = CreateHandleMaterial(Color.green);
                renderer.sortingOrder = 8502;
            }
            
            // 移除默认的3D碰撞体，我们用距离检测
            var collider = handle.GetComponent<SphereCollider>();
            if (collider != null)
            {
                Destroy(collider);
            }
            
            handle.SetActive(false);
            rotationHandle = handle;
        }

        /// <summary>
        /// 更新旋转控制点位置
        /// </summary>
        private void UpdateRotationHandle()
        {
            if (rotationHandle == null) return;
            
            // 将旋转控制点放在包围框顶部中心的上方
            Vector3 position = new Vector3(currentRect.center.x, currentRect.yMax + rotationHandleDistance, -0.1f);
            rotationHandle.transform.position = position;
        }

        private Material CreateHandleMaterial(Color color)
        {
            Shader urpUnlit = Shader.Find("Universal Render Pipeline/Unlit");
            Material mat = new Material(urpUnlit);
            if (mat.HasProperty("_BaseColor"))
            {
                mat.SetColor("_BaseColor", color);
            }
            else if (mat.HasProperty("_Color"))
            {
                mat.SetColor("_Color", color);
            }
            return mat;
        }

        void OnDestroy()
        {
            // 在Runtime下使用Destroy而不是DestroyImmediate来避免内存泄漏风险
            if (lineRenderer != null && lineRenderer.material != null)
            {
                Destroy(lineRenderer.material);
            }
            
            foreach (var handle in scaleHandles)
            {
                if (handle != null)
                {
                    if (handle.GetComponent<Renderer>()?.material != null)
                        Destroy(handle.GetComponent<Renderer>().material);
                }
            }
            
            if (rotationHandle != null)
            {
                if (rotationHandle.GetComponent<Renderer>()?.material != null)
                    Destroy(rotationHandle.GetComponent<Renderer>().material);
            }
        }
    }
} 