using UnityEngine;
using MapEditor.Core;

namespace MapEditor.Tools
{
    /// <summary>
    /// 网格设置工具（占位工具，无实际选择行为）
    /// </summary>
    public class GridSettingsTool : MapToolBase
    {
        public GridSettingsTool(IMapEditorCore editorCore) : base("GridSettingsTool", "网格设置", editorCore, new LayerType[] { LayerType.Grid })
        {
        }

        public override void OnActivate()
        {
            Debug.Log("网格设置工具已激活");
        }

        public override void OnDeactivate()
        {
            Debug.Log("网格设置工具已停用");
        }

        public override void OnSceneInput(InputContext context)
        {
            // 网格层无需处理场景输入
        }

        public override void UpdatePreview()
        {
            // 网格层无需更新预览
        }
    }
} 