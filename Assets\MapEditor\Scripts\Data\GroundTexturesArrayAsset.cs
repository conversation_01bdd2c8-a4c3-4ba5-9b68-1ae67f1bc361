using UnityEngine;

namespace MapEditor.Data
{
    /// <summary>
    /// 离线生成的地表 <see cref="Texture2DArray"/> 资产，配合 <see cref="GroundTextureArrayProvider"/> 使用。
    /// 
    /// 该文件应位于 Resources 目录下，默认建议路径：
    ///     Assets/MapEditor/Resources/GroundTexturesArray.asset
    /// </summary>
    [CreateAssetMenu(fileName = "GroundTexturesArray", menuName = "MapEditor/Ground Textures Array Asset", order = 15)]
    public class GroundTexturesArrayAsset : ScriptableObject
    {
        [Tooltip("实际用于渲染的 Texture2DArray 子资产")] public Texture2DArray surfaceArray;

        [Tooltip("与 Texture2DArray layer 对齐的全局索引")] public int[] globalIndices;

        [Tooltip("与 Texture2DArray layer 对齐的 UVScale（来自各 GroundTextureSO.uvScale）")] public Vector2[] uvScales;

        [Tooltip("Texture2DArray 宽度")] public int width;
        [Tooltip("Texture2DArray 高度")] public int height;
        [Tooltip("纹理格式")] public TextureFormat textureFormat;
    }
} 