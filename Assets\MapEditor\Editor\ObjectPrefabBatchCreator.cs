using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace MapEditor.EditorTools
{
    public class ObjectPrefabBatchCreator : EditorWindow
    {
        [MenuItem("Tools/MapEditor/Batch Create Object Prefabs", priority = 202)]
        private static void ShowWindow()
        {
            var window = GetWindow<ObjectPrefabBatchCreator>(true, "Batch Object Prefab Creator");
            window.minSize = new Vector2(450, 400);
        }

        private List<Texture2D> pendingTextures = new();
        private string saveFolder = "Assets/MapEditor/Resources/ObjectPrefabs/Environment";
        private string materialGuid = "a97c105638bdf8b4a8650670310a4cd3"; // URP 2D默认Lit材质
        private bool addCollider = true;
        private float pixelsPerUnit = 100f;
        private Vector2 pivot = new Vector2(0.5f, 0.5f);

        private void OnGUI()
        {
            GUILayout.Label("根据 Texture2D 批量生成对象预制体 (ObjectPrefabs)", EditorStyles.boldLabel);
            GUILayout.Space(6);

            EditorGUILayout.HelpBox("将纹理文件拖入下方区域，系统将为每个纹理创建包含 SpriteRenderer 和可选 BoxCollider2D 的预制体。", MessageType.Info);
            GUILayout.Space(4);

            // 拖拽和选择按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("添加选中的纹理"))
            {
                foreach (var obj in Selection.objects)
                {
                    if (obj is Texture2D tex && !pendingTextures.Contains(tex))
                        pendingTextures.Add(tex);
                }
            }
            if (GUILayout.Button("清空列表"))
            {
                pendingTextures.Clear();
            }
            EditorGUILayout.EndHorizontal();

            // 拖拽区域
            Rect dropArea = GUILayoutUtility.GetRect(0, 80, GUILayout.ExpandWidth(true));
            GUI.Box(dropArea, "将 Texture2D 拖入此处\n支持多选拖拽", EditorStyles.helpBox);
            HandleDragAndDrop(dropArea);

            GUILayout.Space(8);

            // 配置选项
            GUILayout.Label("预制体设置", EditorStyles.boldLabel);
            saveFolder = EditorGUILayout.TextField("保存目录", saveFolder);
            
            EditorGUILayout.Space(4);
            addCollider = EditorGUILayout.Toggle("添加 BoxCollider2D", addCollider);
            pixelsPerUnit = EditorGUILayout.FloatField("像素每单位 (PPU)", pixelsPerUnit);
            pivot = EditorGUILayout.Vector2Field("精灵锚点", pivot);

            EditorGUILayout.Space(4);
            materialGuid = EditorGUILayout.TextField("材质 GUID (可选)", materialGuid);
            EditorGUILayout.HelpBox("默认使用 URP 2D Renderer 的 Lit 材质。留空则使用 Sprites-Default 材质。", MessageType.None);

            GUILayout.Space(8);

            // 创建按钮
            GUI.enabled = pendingTextures.Count > 0;
            if (GUILayout.Button("创建预制体", GUILayout.Height(30)))
            {
                CreatePrefabs();
            }
            GUI.enabled = true;

            GUILayout.Space(6);
            GUILayout.Label($"待处理纹理: {pendingTextures.Count}");

            // 显示纹理列表
            if (pendingTextures.Count > 0)
            {
                GUILayout.Label("纹理列表:", EditorStyles.boldLabel);
                EditorGUILayout.BeginVertical("box");
                foreach (var tex in pendingTextures)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.ObjectField(tex, typeof(Texture2D), false);
                    if (GUILayout.Button("移除", GUILayout.Width(50)))
                    {
                        pendingTextures.Remove(tex);
                        break;
                    }
                    EditorGUILayout.EndHorizontal();
                }
                EditorGUILayout.EndVertical();
            }
        }

        private void HandleDragAndDrop(Rect dropArea)
        {
            UnityEngine.Event evt = UnityEngine.Event.current;
            switch (evt.type)
            {
                case EventType.DragUpdated:
                case EventType.DragPerform:
                    if (!dropArea.Contains(evt.mousePosition)) break;
                    
                    bool hasValidTextures = false;
                    foreach (var obj in DragAndDrop.objectReferences)
                    {
                        if (obj is Texture2D)
                        {
                            hasValidTextures = true;
                            break;
                        }
                    }

                    DragAndDrop.visualMode = hasValidTextures ? DragAndDropVisualMode.Copy : DragAndDropVisualMode.Rejected;
                    
                    if (evt.type == EventType.DragPerform && hasValidTextures)
                    {
                        DragAndDrop.AcceptDrag();
                        foreach (var obj in DragAndDrop.objectReferences)
                        {
                            if (obj is Texture2D tex && !pendingTextures.Contains(tex))
                                pendingTextures.Add(tex);
                        }
                    }
                    evt.Use();
                    break;
            }
        }

        private void CreatePrefabs()
        {
            if (pendingTextures.Count == 0)
            {
                EditorUtility.DisplayDialog("无纹理", "请先添加至少一张 Texture2D。", "确定");
                return;
            }

            // 确保保存目录存在
            if (!AssetDatabase.IsValidFolder(saveFolder))
            {
                if (!EditorUtility.DisplayDialog("目录不存在", $"保存路径 {saveFolder} 不存在，是否创建？", "是", "否"))
                    return;

                string parentFolder = Path.GetDirectoryName(saveFolder);
                string folderName = Path.GetFileName(saveFolder);
                AssetDatabase.CreateFolder(parentFolder, folderName);
            }

            // 获取材质
            Material material = null;
            if (!string.IsNullOrEmpty(materialGuid))
            {
                string materialPath = AssetDatabase.GUIDToAssetPath(materialGuid);
                if (!string.IsNullOrEmpty(materialPath))
                {
                    material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                }
            }

            int successCount = 0;
            int failureCount = 0;

            try
            {
                for (int i = 0; i < pendingTextures.Count; i++)
                {
                    var texture = pendingTextures[i];
                    string prefabName = texture.name + ".prefab";
                    string prefabPath = Path.Combine(saveFolder, prefabName);

                    // 显示进度
                    EditorUtility.DisplayProgressBar("创建预制体", $"处理 {texture.name} ({i + 1}/{pendingTextures.Count})", (float)i / pendingTextures.Count);

                    try
                    {
                        // 创建GameObject
                        GameObject prefabObj = new GameObject(texture.name);

                        // 创建Sprite
                        Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(AssetDatabase.GetAssetPath(texture));
                        if (sprite == null)
                        {
                            // 如果没有现成的Sprite，创建一个
                            sprite = Sprite.Create(texture, 
                                new Rect(0, 0, texture.width, texture.height), 
                                pivot, 
                                pixelsPerUnit);
                        }

                        // 添加SpriteRenderer组件
                        SpriteRenderer spriteRenderer = prefabObj.AddComponent<SpriteRenderer>();
                        spriteRenderer.sprite = sprite;
                        if (material != null)
                        {
                            spriteRenderer.sharedMaterial = material;
                        }

                        // 添加BoxCollider2D组件（如果需要）
                        if (addCollider)
                        {
                            BoxCollider2D collider = prefabObj.AddComponent<BoxCollider2D>();
                            // 自动设置碰撞体大小
                            if (sprite != null)
                            {
                                collider.size = sprite.bounds.size;
                            }
                        }

                        // 保存为预制体
                        GameObject prefab = PrefabUtility.SaveAsPrefabAsset(prefabObj, prefabPath);
                        if (prefab != null)
                        {
                            successCount++;
                        }
                        else
                        {
                            failureCount++;
                            Debug.LogError($"创建预制体失败: {prefabName}");
                        }

                        // 清理临时GameObject
                        DestroyImmediate(prefabObj);
                    }
                    catch (System.Exception ex)
                    {
                        failureCount++;
                        Debug.LogError($"处理纹理 {texture.name} 时发生错误: {ex.Message}");
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            // 刷新资源数据库
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // 显示结果
            string message = $"预制体创建完成!\n成功: {successCount}\n失败: {failureCount}";
            EditorUtility.DisplayDialog("完成", message, "确定");

            // 在Project窗口中选中保存目录
            var folderObj = AssetDatabase.LoadAssetAtPath<Object>(saveFolder);
            if (folderObj != null)
            {
                Selection.activeObject = folderObj;
                EditorGUIUtility.PingObject(folderObj);
            }
        }
    }
} 