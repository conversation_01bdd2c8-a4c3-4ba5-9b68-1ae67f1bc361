using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// UI管理器实现类，负责管理所有UI面板和控件
    /// </summary>
    public class UIManager : MonoBehaviour, IUIManager
    {
        private static UIManager instance;
        public static UIManager Instance => instance;
        [SerializeField] private UIDocument rootDocument;
        [SerializeField] private VisualTreeAsset messageBoxTemplate;
        [SerializeField] private VisualTreeAsset confirmDialogTemplate;

        private readonly Dictionary<string, IUIPanel> panels = new();
        private IEventSystem _eventSystem;

        
        public UIDocument RootDocument => rootDocument;
        public VisualElement Root => rootDocument?.rootVisualElement;

        public void Initialize()
        {
            instance = this;
            EnsureEventService();
            
            Debug.Log("UI Manager initialized");
        }
        
        /// <summary>
        /// 初始化UI资源和主面板
        /// </summary>
        public void InitializeUI(UIDocument document, VisualTreeAsset mainTemplate, VisualTreeAsset messageBox, VisualTreeAsset confirmDialog, StyleSheet styleSheet)
        {
            // 1. 设置UI资源
            rootDocument = document;
            messageBoxTemplate = messageBox;
            confirmDialogTemplate = confirmDialog;

            if (mainTemplate != null)
            {
                document.visualTreeAsset = mainTemplate;
            }

            if (styleSheet != null && document.rootVisualElement != null)
            {
                document.rootVisualElement.styleSheets.Add(styleSheet);
            }
            Debug.Log("UI resources setup complete.");

            // 2. 初始化主面板（避免重复实例化模板）
            if (document.rootVisualElement == null)
            {
                Debug.LogError("UIDocument rootVisualElement is null, cannot initialize MainPanel.");
                return;
            }

            var mainPanel = new MainPanel(null, null);
            mainPanel.SetContainer(document.rootVisualElement);

            RegisterPanel(mainPanel);
            Debug.Log("MainPanel initialized and registered using existing UIDocument root.");

            // 4. 注册 ColorPickerDialog（颜色选择器对话框）
            var colorPickerTpl = Resources.Load<VisualTreeAsset>("UI/ColorPickerDialog");
            if (colorPickerTpl != null)
            {
                var colorPickerDialog = new ColorPickerDialog(this, colorPickerTpl);
                RegisterPanel(colorPickerDialog);
                Debug.Log("ColorPickerDialog registered.");
            }
            else
            {
                Debug.LogWarning("ColorPickerDialog template not found in Resources/UI/");
            }

            // 4. 注册 BrushSettingsPanel（需在 Root 准备好后）
            // var brushTpl = Resources.Load<VisualTreeAsset>("UI/BrushSettingsPanel");
            // var brushPanel = new BrushSettingsPanel(this, brushTpl);
            // RegisterPanel(brushPanel);

        }
        
        private void OnToolChanged(ToolChangedEvent evt)
        {
            // 更新工具UI状态
            Debug.Log($"Tool changed from {evt.OldToolId} to {evt.NewToolId}");
        }
        
        /// <summary>
        /// 确保EventService已连接
        /// </summary>
        public void EnsureEventService()
        {
            if (_eventSystem == null)
            {
                if (MapEditorCore.Instance.EventSystem != null)
                {
                    _eventSystem = MapEditorCore.Instance.EventSystem;
                    _eventSystem.Subscribe<ToolChangedEvent>(OnToolChanged);
                    // 订阅来自各面板的 UI 请求事件
                    _eventSystem.Subscribe<RequestShowMessageEvent>(OnRequestShowMessage);
                    _eventSystem.Subscribe<RequestShowPanelEvent>(e => ShowPanel(e.PanelId));
                    _eventSystem.Subscribe<RequestHidePanelEvent>(e => HidePanel(e.PanelId));
                    _eventSystem.Subscribe<RequestRegisterPanelEvent>(e => RegisterPanel(e.Panel));
                    _eventSystem.Subscribe<RequestUnregisterPanelEvent>(e => UnregisterPanel(e.PanelId));
                    Debug.Log("EventService connected to UIManager");
                }
            }
        }

        private void OnRequestShowMessage(RequestShowMessageEvent evt)
        {
            ShowMessage(evt.Title, evt.Message, evt.Type);
        }

        public void RegisterPanel(IUIPanel panel)
        {
            if (panel == null || string.IsNullOrEmpty(panel.PanelId))
            {
                Debug.LogError("Cannot register null panel or panel with empty ID");
                return;
            }
            
            if (panels.ContainsKey(panel.PanelId))
            {
                Debug.LogWarning($"Panel with ID {panel.PanelId} already registered, replacing");
                panels[panel.PanelId] = panel;
            }
            else
            {
                panels.Add(panel.PanelId, panel);
                // 特殊处理：GridSettingsPanel会在自己的Initialize中添加到特定容器
                // 如果面板的根就是 UIDocument 的根，则无需再添加
                if (panel.Root != Root && panel.PanelId != "GridSettings")
                {
                    Root.Add(panel.Root);
                }

                panel.Initialize();
            }
            
            Debug.Log($"Panel {panel.DisplayName} registered with ID: {panel.PanelId}");
        }

        public void UnregisterPanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId) || !panels.ContainsKey(panelId))
            {
                Debug.LogWarning($"Panel with ID {panelId} not found for unregistering");
                return;
            }
            
            var panel = panels[panelId];
            panel.ClosePanel();
            Root.Remove(panel.Root);
            panels.Remove(panelId);
            
            Debug.Log($"Panel {panel.DisplayName} unregistered");
        }

        public void ShowPanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId) || !panels.ContainsKey(panelId))
            {
                Debug.LogWarning($"Panel with ID {panelId} not found for showing");
                return;
            }
            
            panels[panelId].IsVisible = true;
            panels[panelId].UpdatePanel();
        }

        public void HidePanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId) || !panels.ContainsKey(panelId))
            {
                Debug.LogWarning($"Panel with ID {panelId} not found for hiding");
                return;
            }
            
            panels[panelId].IsVisible = false;
        }

        public IUIPanel GetPanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId) || !panels.ContainsKey(panelId))
            {
                return null;
            }
            
            return panels[panelId];
        }

        public void ShowMessage(string title, string message, MessageType type = MessageType.Info)
        {
            if (messageBoxTemplate == null)
            {
                Debug.LogError("Message box template not assigned");
                return;
            }
            
            VisualElement messageBox = messageBoxTemplate.Instantiate();
            messageBox.Q<Label>("Title").text = title;
            messageBox.Q<Label>("Message").text = message;
            
            // 设置消息类型样式
            string typeClass = type.ToString().ToLower() + "-message";
            messageBox.AddToClassList(typeClass);
            
            // 添加关闭按钮事件
            messageBox.Q<Button>("CloseButton").clicked += () => Root.Remove(messageBox);
            
            Root.Add(messageBox);
        }

        public void ShowConfirmDialog(string title, string message, Action onConfirm, Action onCancel = null)
        {
            if (confirmDialogTemplate == null)
            {
                Debug.LogError("Confirm dialog template not assigned");
                return;
            }
            
            VisualElement dialog = confirmDialogTemplate.Instantiate();
            dialog.Q<Label>("Title").text = title;
            dialog.Q<Label>("Message").text = message;
            
            // 添加确认按钮事件
            dialog.Q<Button>("ConfirmButton").clicked += () => 
            {
                Root.Remove(dialog);
                onConfirm?.Invoke();
            };
            
            // 添加取消按钮事件
            dialog.Q<Button>("CancelButton").clicked += () => 
            {
                Root.Remove(dialog);
                onCancel?.Invoke();
            };
            
            Root.Add(dialog);
        }
    }
}