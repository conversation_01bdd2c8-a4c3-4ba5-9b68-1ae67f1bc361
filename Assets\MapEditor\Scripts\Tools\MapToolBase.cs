using UnityEngine;
using MapEditor.Core;
using System.Linq;
using System;

namespace MapEditor.Tools
{
    /// <summary>
    /// 地图工具基类，实现IMapTool接口的通用功能
    /// </summary>
    public abstract class MapToolBase : IMapTool
    {
        // 工具基本属性
        private string toolId;
        private string displayName;
        private LayerType[] supportedLayerTypes;

        private IEventSystem eventSystem;
        
        // 编辑器核心引用
        protected IMapEditorCore editorCore;
        
        // 图层管理器引用
        protected ILayerDataStore layerManager;
        
        /// <summary>
        /// 工具唯一标识符
        /// </summary>
        public string ToolId => toolId;
        
        /// <summary>
        /// 工具显示名称
        /// </summary>
        public string DisplayName => displayName;
        
        /// <summary>
        /// 获取此工具支持的图层类型列表
        /// </summary>
        public LayerType[] SupportedLayerTypes => supportedLayerTypes;
        
        /// <summary>
        /// 检查工具是否支持指定的图层类型
        /// </summary>
        public virtual bool SupportsLayerType(LayerType layerType)
        {
            if (supportedLayerTypes == null || supportedLayerTypes.Length == 0)
            {
                // 如果没有指定支持的图层类型，默认支持所有图层
                return true;
            }
            return supportedLayerTypes.Contains(layerType);
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="toolId">工具唯一ID</param>
        /// <param name="displayName">显示名称</param>
        /// <param name="editorCore">编辑器核心引用</param>
        protected MapToolBase(string toolId, string displayName, IMapEditorCore editorCore)
            : this(toolId, displayName, editorCore, null)
        {
        }
        
        /// <summary>
        /// 构造函数（带支持的图层类型）
        /// </summary>
        /// <param name="toolId">工具唯一ID</param>
        /// <param name="displayName">显示名称</param>
        /// <param name="editorCore">编辑器核心引用</param>
        /// <param name="supportedLayerTypes">支持的图层类型数组</param>
        protected MapToolBase(string toolId, string displayName, IMapEditorCore editorCore, LayerType[] supportedLayerTypes)
        {
            this.toolId = toolId;
            this.displayName = displayName;
            this.editorCore = editorCore;
            this.supportedLayerTypes = supportedLayerTypes;
            
            // 初始化图层管理器引用
            this.layerManager = editorCore?.LayerStore;
            eventSystem = editorCore.EventSystem;

        }

        public virtual void Update()
        {
            
        }
        
        /// <summary>
        /// 工具激活时调用
        /// </summary>
        public virtual void OnActivate()
        {
            Debug.Log($"Tool activated: {displayName}");
        }
        
        /// <summary>
        /// 工具停用时调用
        /// </summary>
        public virtual void OnDeactivate()
        {
            Debug.Log($"Tool deactivated: {displayName}");
        }
        
        /// <summary>
        /// 处理场景输入，由子类实现
        /// </summary>
        public abstract void OnSceneInput(InputContext context);
        
        /// <summary>
        /// 更新工具预览，由子类实现
        /// </summary>
        public abstract void UpdatePreview();
        

        
        /// <summary>
        /// 通用的射线检测方法
        /// </summary>
        protected Vector2 GetWorldPositionFromMousePosition(Vector2 mousePosition)
        {
            Camera camera = editorCore.SceneRenderer.RenderCamera;
            return camera.ScreenToWorldPoint(mousePosition);
        }
        
        /// <summary>
        /// 发布工具状态改变事件
        /// </summary>
        protected void PublishToolStatusChanged(string status)
        {
            // 可以在这里发布工具状态事件
            Debug.Log($"Tool {displayName} status: {status}");
        }


        #region Event
        protected void RegisterEvent<TEvent>(Action<TEvent> action, int priority = 0)
        {
            eventSystem.Subscribe(action, priority);
        }
        protected void UnregisterEvent<TEvent>(Action<TEvent> action)
        {
            eventSystem.Unsubscribe(action);
        }
        protected void PublishEvent<TEvent>(TEvent eventData)
        {
            eventSystem.Publish(eventData);
        }

        protected void PublishAsyncEvent<TEvent>(TEvent eventData)
        {
            eventSystem.PublishAsync(eventData);
        }
        #endregion Event



    }
}