using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using MapEditor.Data;

namespace MapEditor.EditorTools
{
    public class GroundTextureBatchCreator : EditorWindow
    {
        [MenuItem("Tools/MapEditor/Batch Create Ground Textures", priority = 200)]
        private static void ShowWindow()
        {
            var window = GetWindow<GroundTextureBatchCreator>(true, "Batch Ground Texture Creator");
            window.minSize = new Vector2(400, 300);
        }

        private List<Texture2D> pendingTextures = new();
        private int startIndex = 0;
        private string saveFolder = "Assets/MapEditor/Resources/GroundTextures";

        private void OnGUI()
        {
            GUILayout.Label("将 Texture2D 拖入下方列表，批量生成 GroundTextureSO 资产", EditorStyles.boldLabel);
            GUILayout.Space(4);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Selected Textures"))
            {
                foreach (var obj in Selection.objects)
                {
                    if (obj is Texture2D tex && !pendingTextures.Contains(tex))
                        pendingTextures.Add(tex);
                }
            }
            if (GUILayout.Button("Clear"))
            {
                pendingTextures.Clear();
            }
            EditorGUILayout.EndHorizontal();

            // Drag-and-Drop area
            Rect dropArea = GUILayoutUtility.GetRect(0, 60, GUILayout.ExpandWidth(true));
            GUI.Box(dropArea, "Drag Textures Here", EditorStyles.helpBox);
            HandleDragAndDrop(dropArea);

            GUILayout.Space(6);
            startIndex = EditorGUILayout.IntField("Starting Index", startIndex);
            saveFolder = EditorGUILayout.TextField("Save Folder", saveFolder);

            GUILayout.Space(6);
            if (GUILayout.Button("Create/Update Assets"))
            {
                CreateAssets();
            }

            GUILayout.Space(6);
            GUILayout.Label($"Pending Textures: {pendingTextures.Count}");
        }

        private void HandleDragAndDrop(Rect dropArea)
        {
            UnityEngine.Event evt = UnityEngine.Event.current;
            switch (evt.type)
            {
                case EventType.DragUpdated:
                case EventType.DragPerform:
                    if (!dropArea.Contains(evt.mousePosition)) break;
                    DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                    if (evt.type == EventType.DragPerform)
                    {
                        DragAndDrop.AcceptDrag();
                        foreach (var obj in DragAndDrop.objectReferences)
                        {
                            if (obj is Texture2D t && !pendingTextures.Contains(t))
                                pendingTextures.Add(t);
                        }
                    }
                    UnityEngine.Event.current.Use();
                    break;
            }
        }

        private void CreateAssets()
        {
            if (pendingTextures.Count == 0)
            {
                EditorUtility.DisplayDialog("No Textures", "请先拖入至少一张 Texture2D。", "OK");
                return;
            }

            if (!AssetDatabase.IsValidFolder(saveFolder))
            {
                if (!EditorUtility.DisplayDialog("Folder not exist", $"保存路径 {saveFolder} 不存在，是否创建？", "Yes", "No")) return;
                AssetDatabase.CreateFolder(System.IO.Path.GetDirectoryName(saveFolder), System.IO.Path.GetFileName(saveFolder));
            }

            int index = startIndex;
            foreach (var tex in pendingTextures)
            {
                string assetName = tex.name + "_GT.asset";
                string path = System.IO.Path.Combine(saveFolder, assetName);

                GroundTextureSO so = AssetDatabase.LoadAssetAtPath<GroundTextureSO>(path);
                if (so == null)
                {
                    so = ScriptableObject.CreateInstance<GroundTextureSO>();
                    AssetDatabase.CreateAsset(so, path);
                }

                so.texture = tex;
                so.textureIndex = index;
                so.displayName = tex.name;
                so.uvScale = Vector2.one;
                EditorUtility.SetDirty(so);

                index++;
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("Done", "GroundTextureSO assets created/updated successfully.", "OK");
        }
    }
} 