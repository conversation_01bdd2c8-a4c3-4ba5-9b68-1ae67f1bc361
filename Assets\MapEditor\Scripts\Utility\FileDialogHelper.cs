using UnityEngine;
using System;
using System.Threading.Tasks;
using SimpleFileBrowser;

namespace MapEditor.Utility
{
    /// <summary>
    /// 提供跨平台的简易文件/目录选择包装，当前仅在 Unity Editor 内启用。
    /// 运行时可接入 StandaloneFileBrowser 或自定义实现。
    /// </summary>
    public static class FileDialogHelper
    {
        /// <summary>
        /// 选择一个目录，返回绝对路径；取消返回空字符串。
        /// 运行时与 Editor 统一使用 SimpleFileBrowser 实现，避免平台差异。
        /// </summary>
        public static async System.Threading.Tasks.Task<string> SelectFolderAsync(string title = "选择地图目录")
        {
            // SimpleFileBrowser: 选择文件夹
            var tcs = new System.Threading.Tasks.TaskCompletionSource<string>();

            // 清空过滤器，文件夹选择不需要扩展名过滤
            SimpleFileBrowser.FileBrowser.SetFilters(false);

            SimpleFileBrowser.FileBrowser.ShowLoadDialog(
                // onSuccess
                paths =>
                {
                    if (paths != null && paths.Length > 0)
                        tcs.TrySetResult(paths[0]);
                    else
                        tcs.TrySetResult(string.Empty);
                },
                // onCancel
                () => tcs.TrySetResult(string.Empty),
                SimpleFileBrowser.FileBrowser.PickMode.Folders,
                false,
                UnityEngine.Application.dataPath,
                string.Empty,
                title,
                "选择");

            return await tcs.Task;
        }

        /// <summary>
        /// 选择单个 JSON 文件，返回绝对路径；取消返回空字符串。
        /// 运行时与 Editor 统一使用 SimpleFileBrowser 实现。
        /// </summary>
        public static async System.Threading.Tasks.Task<string> SelectJsonFileAsync(string title = "选择地图文件")
        {
            var tcs = new System.Threading.Tasks.TaskCompletionSource<string>();

            // 仅显示 .json 文件
            SimpleFileBrowser.FileBrowser.SetFilters(false, new SimpleFileBrowser.FileBrowser.Filter("JSON", ".json"));
            SimpleFileBrowser.FileBrowser.SetDefaultFilter(".json");

            SimpleFileBrowser.FileBrowser.ShowLoadDialog(
                paths =>
                {
                    if (paths != null && paths.Length > 0)
                        tcs.TrySetResult(paths[0]);
                    else
                        tcs.TrySetResult(string.Empty);
                },
                () => tcs.TrySetResult(string.Empty),
                SimpleFileBrowser.FileBrowser.PickMode.Files,
                false,
                UnityEngine.Application.dataPath,
                string.Empty,
                title,
                "选择");

            return await tcs.Task;
        }
    }
} 