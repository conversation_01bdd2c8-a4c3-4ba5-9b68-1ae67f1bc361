using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PlayerLoop;

namespace MapEditor.Core
{
    /// <summary>
    /// 地图编辑器核心实现，负责协调各子系统工作
    /// </summary>
    public class MapEditorCore : MonoBehaviour, IMapEditorCore
    {
        public static MapEditorCore Instance;
        // 内部字段，保存各子系统引用
        private ISceneRenderer sceneRenderer;
      
        private IMapDataStore mapDataStore;
        private IEventSystem eventSystem;
        private ILayerDataStore layerStore;

        
        private readonly List<IUpdate> updates = new List<IUpdate>();
        private readonly List<ILateUpdate> lateUpdates = new List<ILateUpdate>();
        private Dictionary<Type,IService> servicesDict = new Dictionary<Type,IService>();
        private List<IService> startServices = new List<IService>();





        /// <summary>
        /// 场景渲染器
        /// </summary>
        public ISceneRenderer SceneRenderer => sceneRenderer;



        /// <summary>
        /// 地图数据管理器
        /// </summary>
        public IMapDataStore MapDataStore => mapDataStore;

        public ILayerDataStore LayerStore => layerStore;

        /// <summary>
        /// 事件系统
        /// </summary>
        public IEventSystem EventSystem => eventSystem;

        /// <summary>
        /// 注册核心事件处理器
        /// </summary>
        private void RegisterEventHandlers()
        {

        }

        /// <summary>
        /// 关闭编辑器并清理资源
        /// </summary>
        public void Shutdown()
        {
            foreach (var service in servicesDict.Values)
            {
                service.OnDestroy();
            }
            servicesDict.Clear();
            // 清理事件系统
            eventSystem?.ClearAllSubscriptions();
            // 清理依赖容器;
            Instance = null;
        }

        /// <summary>
        /// 应用退出时清理资源
        /// </summary>
        private void OnApplicationQuit()
        {
            Shutdown();
        }

        private void OnDestroy()
        {
            Shutdown();
        }



        #region Service
        /// <summary>
        /// 注册服务
        /// </summary>
        /// <typeparam name="TService"></typeparam>
        /// <param name="service"></param>
        public void RegisterService<TService>(TService service) where TService : IService
        {
            servicesDict[typeof(TService)] = service;
            service.SetUp(this,eventSystem);

            if(service is IUpdate update)
            {
                updates.Add(update);
            }
            if(service is ILateUpdate lateUpdate)
            {
                lateUpdates.Add(lateUpdate);
            }
            service.Initialize();

            if(!service.IsStarted)
            {
                startServices.Add(service);
            }
        }

        /// <summary>
        /// 注销服务
        /// </summary>
        /// <typeparam name="TService"></typeparam>
        /// <param name="service"></param>
        public void UnregisterService<TService>() where TService : IService
        {
            if(servicesDict.TryGetValue(typeof(TService),out var service))
            {
                servicesDict.Remove(typeof(TService));
                service.OnDestroy();
            }

            if (service is IUpdate update)
            {
                updates.Remove(update);
            }
            if(service is ILateUpdate lateUpdate)
            {
                lateUpdates.Remove(lateUpdate);
            }
        }

        /// <summary>
        /// 获取服务
        /// </summary>
        /// <typeparam name="TService"></typeparam>
        /// <param name="service"></param>
        public TService GetService<TService>() where TService : IService
        {
            if(servicesDict.TryGetValue(typeof(TService),out var service))
            {
                return (TService)service;
            }
            Debug.LogError($"Service of type {typeof(TService).Name} not found");
            return default;
        }
        #endregion Service

        #region LifeCycle
        private void Update()
        {


            for(int i = startServices.Count-1; i >= 0; i--)
            {
                startServices[i].Start();
                startServices[i].IsStarted = true;
                startServices.RemoveAt(i);
            }

            foreach(var update in updates)
            {
                update.Update();
            }
        }
        private void LateUpdate()
        {
            foreach(var lateUpdate in lateUpdates)
            {
                lateUpdate.LateUpdate();
            }
        }
        #endregion LifeCycle





        public void Initialize(
            IEventSystem eventSystem,
            ISceneRenderer sceneRenderer,
       
            IMapDataStore mapDataStore, 
            ILayerDataStore layerStore)
        {
            Instance = this;
            Debug.Log("Initializing MapEditor Core...");
            this.eventSystem = eventSystem;
            this.sceneRenderer = sceneRenderer;

            this.mapDataStore = mapDataStore;
            this.layerStore = layerStore;


            // 配置URP 2D渲染设置
            MapEditorRenderSettings.ConfigureURP2D();

            // 注册核心事件处理器
            RegisterEventHandlers();

            Debug.Log("MapEditor Core initialized successfully");
        }
    }
}