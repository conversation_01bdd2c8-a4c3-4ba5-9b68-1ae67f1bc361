using System.IO;
using System.Collections.Generic;
using UnityEngine;
using MapEditor.Core;
using System.Linq;
using System;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.Data
{
    /// <summary>
    /// 地图数据管理器，负责地图数据的加载、保存和修改
    /// </summary>
    public class MapDataStore : IMapDataStore, ISavable
    {
        private MapData currentMap;
        private IEventSystem eventSystem;
        private bool hasUnsavedChanges = false;
        
        // 存储路径
        private const string MAP_SAVE_PATH = "MapData";
        
        // 新增：地图所在目录及主文件路径，由用户在新建/加载时指定
        private string currentMapDirectory;
        private string currentMapFilePath;

        // 自动保存相关常量
        private const string AUTOSAVE_FOLDER_NAME = "AutoSave";
        private const int AUTOSAVE_MAX_FILE_COUNT = 5;
        
        public IMapData CurrentMap => currentMap;
        public string CurrentMapDirectory => currentMapDirectory ?? string.Empty;
        
        public MapDataStore(IEventSystem eventSystem)
        {
            this.eventSystem = eventSystem;

            // 注册动作交由 SaveService 的 Initialize 处理
        }
        
        /// <summary>
        /// 创建新地图（由外部 UI 先弹出目录选择，再传入目录路径）
        /// </summary>
        /// <param name="mapName">地图名称</param>
        /// <param name="mapSize">地图尺寸</param>
        /// <param name="directoryPath">地图保存目录</param>
        public IMapData CreateNewMap(string mapName, Vector2Int mapSize, int chunkSize, string directoryPath)
        {
            // 在创建新地图之前，先检查未保存修改
            if (hasUnsavedChanges)
            {
                Debug.LogWarning("Creating new map with unsaved changes");
            }

            if (string.IsNullOrEmpty(directoryPath))
            {
                Debug.LogError("Directory path is null when creating map");
                return null;
            }

            currentMapDirectory = directoryPath;
            Directory.CreateDirectory(currentMapDirectory);

            // 创建新地图，设置 chunkSize
            currentMap = new MapData(mapName, mapSize, chunkSize);

            // 生成主文件路径（同名覆盖）
            currentMapFilePath = Path.Combine(currentMapDirectory, $"{mapName}.json");

            // 发布地图创建事件
            eventSystem.Publish(new MapCreatedEvent
            {
                MapData = currentMap
            });

            hasUnsavedChanges = true;
            SaveMap(); // 创建后立即保存一次空白地图

            return currentMap;
        }

        // 兼容旧调用：默认使用 256 的 chunkSize
        public IMapData CreateNewMap(string mapName, Vector2Int mapSize, string directoryPath)
        {
            return CreateNewMap(mapName, mapSize, 256, directoryPath);
        }
        
        public IMapData LoadMap(string mapId)
        {
            // 根据 ID 推断文件路径后转到新方法
            string fileName = $"{mapId}.json";
            string filePath = Path.Combine(Application.persistentDataPath, MAP_SAVE_PATH, fileName);
            return LoadMapFromFile(filePath);
        }
        
        public IMapData LoadMapFromFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                Debug.LogError($"Map file not found: {filePath}");
                return null;
            }

            currentMapFilePath = filePath;
            currentMapDirectory = Path.GetDirectoryName(filePath);

            try
            {
                string json = File.ReadAllText(filePath);

                // 使用 FromJsonOverwrite 兼容 SerializeReference
                var temp = new MapData("temp", UnityEngine.Vector2Int.zero);
                JsonUtility.FromJsonOverwrite(json, temp);

                currentMap = temp;
                currentMap.Initialize();

                eventSystem.Publish(new MapLoadedEvent
                {
                    MapData = currentMap
                });

                hasUnsavedChanges = false;
                return currentMap;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error loading map: {ex.Message}");
            }

            return null;
        }
        
        public void SaveMap()
        {
            if (currentMap == null)
            {
                Debug.LogError("No map to save");
                return;
            }
            
            // 如果没有记录文件路径，回退到默认行为
            if (string.IsNullOrEmpty(currentMapFilePath))
            {
                currentMapDirectory = Path.Combine(Application.persistentDataPath, MAP_SAVE_PATH);
                Directory.CreateDirectory(currentMapDirectory);
                currentMapFilePath = Path.Combine(currentMapDirectory, $"{currentMap.MapId}.json");
            }
            else
            {
                // 确保目录存在
                Directory.CreateDirectory(currentMapDirectory);
            }

            string filePath = currentMapFilePath;
            
            try
            {
                // 更新最后修改时间
                currentMap.LastModifiedTime = System.DateTime.Now;
                
                // 序列化地图数据
                string json = JsonUtility.ToJson(currentMap, true);
                File.WriteAllText(filePath, json);
                
                // 发布地图保存事件
                eventSystem.Publish(new MapSavedEvent 
                {
                    MapData = currentMap,
                    SavePath = filePath
                });
                
                hasUnsavedChanges = false;
                Debug.Log($"Map saved to {filePath}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error saving map: {ex.Message}");
            }
        }
        
        public IEnumerable<MapEditor.Core.MapInfo> ListSavedMaps()
        {
            string dirPath = Path.Combine(Application.persistentDataPath, MAP_SAVE_PATH);
            if (!Directory.Exists(dirPath))
            {
                return new List<MapEditor.Core.MapInfo>();
            }

            var infos = new List<MapEditor.Core.MapInfo>();
            var files = Directory.GetFiles(dirPath, "*.json");
            foreach (var file in files)
            {
                try
                {
                    string json = File.ReadAllText(file);
                    var map = JsonUtility.FromJson<MapData>(json);
                    if (map != null)
                    {
                        infos.Add(new MapEditor.Core.MapInfo
                        {
                            MapId = map.MapId,
                            MapName = map.MapName,
                            LastModified = map.LastModifiedTime,
                            FilePath = file
                        });
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"Error reading map file {file}: {ex.Message}");
                }
            }
            return infos;
        }
        
        public void SaveMapAs(string newMapName)
        {
            if (currentMap == null)
            {
                Debug.LogError("No map to save");
                return;
            }
            
            // 创建新地图副本
            var originalMapId = currentMap.MapId;

            // 更新名称并生成新的保存路径
            currentMap.MapName = newMapName;
            currentMapFilePath = Path.Combine(currentMapDirectory, $"{newMapName}.json");

            // 使用新 GUID
            var mapDataJson = JsonUtility.ToJson(currentMap);
            var newMap = JsonUtility.FromJson<MapData>(mapDataJson);

            currentMap = newMap;

            SaveMap();

            // 发布地图另存为事件
            eventSystem.Publish(new MapSavedAsEvent
            {
                MapData = currentMap,
                OriginalMapId = originalMapId
            });
        }
        
        public void CloseMap()
        {
            if (currentMap == null) return;
            
            if (hasUnsavedChanges)
            {
                Debug.LogWarning("Closing map with unsaved changes");
            }
            
            // 发布地图关闭事件
            eventSystem.Publish(new MapClosedEvent
            {
                MapId = currentMap.MapId
            });
            
            currentMap = null;
            hasUnsavedChanges = false;
            currentMapDirectory = null;
            currentMapFilePath = null;
        }
        
        public bool HasUnsavedChanges()
        {
            return hasUnsavedChanges;
        }
        
        /// <summary>
        /// 标记地图已修改
        /// </summary>
        public void MarkMapChanged()
        {
            hasUnsavedChanges = true;
            
            // 发布地图修改事件
            if (currentMap != null)
            {
                eventSystem.Publish(new MapChangedEvent
                {
                    MapData = currentMap
                });
            }
        }

        /// <summary>
        /// 自动保存当前地图到 AutoSave 子目录，保留最新 N 份
        /// </summary>
        public void SaveAutoBackup()
        {
            if (currentMap == null || string.IsNullOrEmpty(currentMapDirectory)) return;

            var autoSaveDir = Path.Combine(currentMapDirectory, AUTOSAVE_FOLDER_NAME);
            Directory.CreateDirectory(autoSaveDir);

            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupFile = Path.Combine(autoSaveDir, $"{currentMap.MapName}_autosave_{timestamp}.json");

            try
            {
                string json = JsonUtility.ToJson(currentMap, true);
                File.WriteAllText(backupFile, json);

                // 删除旧备份
                var backups = Directory.GetFiles(autoSaveDir, $"{currentMap.MapName}_autosave_*.json")
                                        .OrderByDescending(File.GetCreationTime)
                                        .ToList();

                for (int i = AUTOSAVE_MAX_FILE_COUNT; i < backups.Count; i++)
                {
                    File.Delete(backups[i]);
                }

                Debug.Log($"[AutoSave] Backup saved: {backupFile}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error during auto save: {ex.Message}");
            }
        }

        #region ISavable 实现

        /// <summary>
        /// SaveService 统一调用的保存入口。
        /// </summary>
        public void Save(SaveContext context)
        {
            // 1. 保存逻辑数据
            SaveMap();

            // 2. 自动备份仅在自动保存时进行
            if (context.IsAuto)
            {
                SaveAutoBackup();
            }

            // 3. 清理不再存在的贴图文件
            CleanOrphanTextures(context.MapDirectory);
        }

        /// <summary>
        /// 移除磁盘上与当前地图不再匹配的贴图 / 权重文件。
        /// </summary>
        private void CleanOrphanTextures(string mapDir)
        {
            if (string.IsNullOrEmpty(mapDir) || currentMap == null) return;

            // 收集仍在使用的 TilemapId
            var validIds = new HashSet<string>();
            foreach (var layer in currentMap.GetAllLayers())
            {
                if (layer is MapLayer ml)
                {
                    foreach (var chunk in ml.GetAllChunks())
                    {
                        if (chunk is MapEditor.Data.Chunks.TilemapChunk tc)
                        {
                            validIds.Add(tc.TilemapId);
                        }
                    }
                }
            }

            void ProcessDir(string dir, Func<string, string> extractId)
            {
                if (!System.IO.Directory.Exists(dir)) return;
                foreach (var file in System.IO.Directory.GetFiles(dir, "*.raw"))
                {
                    var name = System.IO.Path.GetFileNameWithoutExtension(file);
                    var id = extractId(name);
                    if (!validIds.Contains(id))
                    {
                        try { System.IO.File.Delete(file); }
                        catch (Exception ex) { UnityEngine.Debug.LogError($"[MapDataStore] Delete orphan file failed: {file} -> {ex.Message}"); }
                    }
                }
            }

            string texturesDir = System.IO.Path.Combine(mapDir, "TilemapTextures");
            string weightsDir = System.IO.Path.Combine(mapDir, "TilemapWeights");

            ProcessDir(texturesDir, n => n); // 直接文件名就是 id
            ProcessDir(weightsDir, n => n.Split('_')[0]); // 取 _ 之前的部分
        }

        #endregion
    }
} 