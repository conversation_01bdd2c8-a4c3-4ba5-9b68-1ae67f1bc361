using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Tools;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.UI
{
    public class BrushSettingsPanel : UIPanel
    {
        public override string PanelId => "BrushTool";
        public override string DisplayName => "画笔设置";

        private VisualElement shapeGrid;
        private VisualElement materialGrid;

        private System.Collections.Generic.List<Button> shapeButtons = new();
        private System.Collections.Generic.List<Button> materialButtons = new();
        private Slider sizeSlider;
        private Slider strengthSlider;
        private FloatField strengthInput;
        private SliderInt blurRadiusSlider;
        private IntegerField blurRadiusInput;
        private Slider hardnessSlider;
        private FloatField hardnessInput;
        private Slider bilateralFactorSlider;
        private FloatField bilateralFactorInput;

        private BrushShapeSO[] allShapes;
        private BrushTool brushTool;
        private GroundTextureSO[] groundTextures;

        public BrushSettingsPanel(IUIManager uiManager, VisualTreeAsset template) : base(uiManager, template) { }

        public override void Initialize()
        {
            // 获取 BrushTool 引用
            var toolService = MapEditorCore.Instance.GetService<ToolService>();
            brushTool = toolService?.GetTool("BrushTool") as BrushTool;
            if (brushTool == null)
            {
                Debug.LogError("BrushTool not found, BrushSettingsPanel disabled");
                return;
            }

            // 加载资源
            allShapes = Resources.LoadAll<BrushShapeSO>("BrushShapes");
            groundTextures = GroundTextureProvider.GetAllTextures();

            // UI 控件查找
            shapeGrid = FindElement<VisualElement>("ShapeGrid");
            materialGrid = FindElement<VisualElement>("MaterialGrid");
            sizeSlider = FindElement<Slider>("SizeSlider");
            strengthSlider = FindElement<Slider>("StrengthSlider");
            strengthInput = FindElement<FloatField>("StrengthInput");
            blurRadiusSlider = FindElement<SliderInt>("BlurRadiusSlider");
            blurRadiusInput = FindElement<IntegerField>("BlurRadiusInput");
            hardnessSlider = FindElement<Slider>("HardnessSlider");
            hardnessInput = FindElement<FloatField>("HardnessInput");
            bilateralFactorSlider = FindElement<Slider>("BilateralFactorSlider");
            bilateralFactorInput = FindElement<FloatField>("BilateralFactorInput");

            // ---------- 创建形状按钮 ----------
            if (shapeGrid != null)
            {
                shapeGrid.Clear();
                shapeButtons.Clear();
                for (int i = 0; i < allShapes.Length; i++)
                {
                    int idx = i; // 捕获索引
                    var btn = new Button(() => OnShapeSelected(idx)) { name = $"ShapeBtn_{idx}" };
                    btn.AddToClassList("icon-button");
                    btn.style.width = 48;
                    btn.style.height = 48;
                    // 设置背景图标（直接使用 alphaMask 纹理预览）
                    Texture2D tex = allShapes[idx].alphaMask != null ? allShapes[idx].alphaMask : Texture2D.whiteTexture;
                    btn.style.backgroundImage = new StyleBackground(tex);
                    shapeGrid.Add(btn);
                    shapeButtons.Add(btn);
                }
            }

            // ---------- 创建材质按钮 ----------
            if (materialGrid != null)
            {
                materialGrid.Clear();
                materialButtons.Clear();

                if (groundTextures == null) groundTextures = GroundTextureProvider.GetAllTextures();

                for (int i = 0; i < groundTextures.Length; i++)
                {
                    int idx = i; // 捕获局部索引，用于按钮点击
                    var btn = new Button(() => OnMaterialSelected(idx)) { name = $"MatBtn_{idx}" };
                    btn.AddToClassList("icon-button");
                    btn.style.width = 64;
                    btn.style.height = 64;

                    var texSO = groundTextures[idx];
                    Texture2D tex = texSO != null && texSO.texture != null ? texSO.texture : Texture2D.grayTexture;
                    btn.style.backgroundImage = new StyleBackground(tex);

                    // 允许选择任意全局纹理索引。是否可在当前 Chunk 绘制由运行时逻辑决定。

                    materialGrid.Add(btn);
                    materialButtons.Add(btn);
                }
            }

            if (sizeSlider != null)
            {
                sizeSlider.lowValue = 0.5f;
                sizeSlider.highValue = 10f;
                sizeSlider.RegisterValueChangedCallback(evt => brushTool.SetBrushSize(evt.newValue));
            }

            if (strengthSlider != null)
            {
                strengthSlider.lowValue = 0f;
                strengthSlider.highValue = 1f;
                strengthSlider.RegisterValueChangedCallback(evt =>
                {
                    // 同步到输入框
                    if (strengthInput != null) strengthInput.SetValueWithoutNotify(evt.newValue);
                    brushTool.SetBrushStrength(evt.newValue);
                });
            }

            if (strengthInput != null)
            {
                strengthInput.RegisterValueChangedCallback(evt =>
                {
                    float v = Mathf.Clamp(evt.newValue, 0f, 1f);
                    // 同步到滑块
                    if (strengthSlider != null) strengthSlider.SetValueWithoutNotify(v);
                    brushTool.SetBrushStrength(v);
                });
            }

            if (blurRadiusSlider != null)
            {
                blurRadiusSlider.lowValue = 0;
                blurRadiusSlider.highValue = 10; // 扩大范围
                blurRadiusSlider.RegisterValueChangedCallback(evt =>
                {
                    // 同步到输入框
                    if (blurRadiusInput != null) blurRadiusInput.SetValueWithoutNotify(evt.newValue);
                    brushTool.SetBlurRadius(evt.newValue);
                });
            }

            if (blurRadiusInput != null)
            {
                blurRadiusInput.RegisterValueChangedCallback(evt =>
                {
                    int v = Mathf.Max(0, evt.newValue); // 只需保证非负
                    // 同步到滑块
                    if (blurRadiusSlider != null) blurRadiusSlider.SetValueWithoutNotify(v);
                    brushTool.SetBlurRadius(v);
                });
            }

            // ---------- 新增：硬度控制 ----------
            if (hardnessSlider != null)
            {
                hardnessSlider.lowValue = 0f;
                hardnessSlider.highValue = 1f;
                hardnessSlider.RegisterValueChangedCallback(evt =>
                {
                    if (hardnessInput != null) hardnessInput.SetValueWithoutNotify(evt.newValue);
                    brushTool.SetHardness(evt.newValue);
                });
            }
            if (hardnessInput != null)
            {
                hardnessInput.RegisterValueChangedCallback(evt =>
                {
                    float v = Mathf.Clamp01(evt.newValue);
                    if (hardnessSlider != null) hardnessSlider.SetValueWithoutNotify(v);
                    brushTool.SetHardness(v);
                });
            }

            // ---------- 新增：保边强度控制 ----------
            if (bilateralFactorSlider != null)
            {
                bilateralFactorSlider.lowValue = 1f;
                bilateralFactorSlider.highValue = 20f;
                bilateralFactorSlider.RegisterValueChangedCallback(evt =>
                {
                    if (bilateralFactorInput != null) bilateralFactorInput.SetValueWithoutNotify(evt.newValue);
                    brushTool.SetBilateralFactor(evt.newValue);
                });
            }
            if (bilateralFactorInput != null)
            {
                bilateralFactorInput.RegisterValueChangedCallback(evt =>
                {
                    float v = Mathf.Clamp(evt.newValue, 1f, 20f);
                    if (bilateralFactorSlider != null) bilateralFactorSlider.SetValueWithoutNotify(v);
                    brushTool.SetBilateralFactor(v);
                });
            }

            // 订阅工具切换事件
            MapEditorCore.Instance.EventSystem?.Subscribe<ToolChangedEvent>(OnToolChanged);
            // 订阅画笔大小快捷键事件，及时刷新 UI
            MapEditorCore.Instance.EventSystem?.Subscribe<BrushSizeShortcutEvent>(OnBrushSizeShortcut);

            RefreshUIFromTool();

            // 设置面板样式
            root.style.flexGrow = 1;
        }

        public override void UpdatePanel()
        {
            RefreshUIFromTool();
        }

        private void RefreshUIFromTool()
        {
            if (brushTool == null) return;
            var settings = brushTool.CurrentSettings;

            // 更新各控件显值但避免触发回调
            if (sizeSlider != null) sizeSlider.SetValueWithoutNotify(settings.size);
            if (strengthSlider != null) strengthSlider.SetValueWithoutNotify(settings.strength);
            if (strengthInput != null) strengthInput.SetValueWithoutNotify(settings.strength);
            if (blurRadiusSlider != null) blurRadiusSlider.SetValueWithoutNotify(settings.blurRadius);
            if (blurRadiusInput != null) blurRadiusInput.SetValueWithoutNotify(settings.blurRadius);
            if (hardnessSlider != null) hardnessSlider.SetValueWithoutNotify(settings.hardness);
            if (hardnessInput != null) hardnessInput.SetValueWithoutNotify(settings.hardness);
            if (bilateralFactorSlider != null) bilateralFactorSlider.SetValueWithoutNotify(settings.bilateralFactor);
            if (bilateralFactorInput != null) bilateralFactorInput.SetValueWithoutNotify(settings.bilateralFactor);

            // 更新选择高亮
            UpdateShapeSelection(System.Array.IndexOf(allShapes, settings.shape));

            // 将全局材质索引映射为本地按钮索引
            int localIdx = -1;
            if (groundTextures != null)
            {
                for (int i = 0; i < groundTextures.Length; i++)
                {
                    if (groundTextures[i].textureIndex == settings.materialIndex)
                    {
                        localIdx = i;
                        break;
                    }
                }
            }
            UpdateMaterialSelection(localIdx);
        }

        // ---------- Callbacks ----------
        private void OnShapeSelected(int idx)
        {
            if (idx < 0 || idx >= allShapes.Length) return;
            brushTool.SetBrushShape(allShapes[idx]);
            UpdateShapeSelection(idx);
        }

        private void OnMaterialSelected(int localIdx)
        {
            if (groundTextures == null || localIdx < 0 || localIdx >= groundTextures.Length) return;
            int globalIndex = groundTextures[localIdx].textureIndex;
            brushTool.SetMaterialIndex(globalIndex);
            UpdateMaterialSelection(localIdx);
        }

        private void UpdateShapeSelection(int selectedIdx)
        {
            for (int i = 0; i < shapeButtons.Count; i++)
            {
                if (i == selectedIdx) shapeButtons[i].AddToClassList("selected");
                else shapeButtons[i].RemoveFromClassList("selected");
            }
        }

        private void UpdateMaterialSelection(int selectedLocalIdx)
        {
            for (int i = 0; i < materialButtons.Count; i++)
            {
                if (i == selectedLocalIdx) materialButtons[i].AddToClassList("selected");
                else materialButtons[i].RemoveFromClassList("selected");
            }
        }

        private void OnToolChanged(ToolChangedEvent e)
        {
            IsVisible = e.NewToolId == "BrushTool";
            if (IsVisible) RefreshUIFromTool();
        }

        /// <summary>
        /// 画笔大小快捷键回调
        /// </summary>
        private void OnBrushSizeShortcut(BrushSizeShortcutEvent evt)
        {
            // 画笔大小已在 BrushTool 内更新，直接刷新面板显示
            RefreshUIFromTool();
        }
    }
} 