<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="UnityEngine.UIElements" schemaLocation="UnityEngine.UIElements ../UIElementsSchema/UnityEngine.UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/MapEditor/Resources/UI/MapEditorStyles.uss?fileID=7433441132597879392&amp;guid=dacb3703fd47af342abe56ffb6eea127&amp;type=3#MapEditorStyles" />
    <ui:VisualElement name="ColorPickerDialog" class="color-picker-dialog" style="position: absolute; align-items: stretch;">
        <ui:Label name="Title" text="选择颜色" class="message-title" />
        <ui:VisualElement class="message-content" style="flex-grow: 1;">
            <ui:VisualElement class="input-group">
                <ui:Label text="颜色预览:" class="input-label" />
                <ui:VisualElement name="ColorPreview" class="color-preview" style="background-color: rgb(255, 255, 255);" />
            </ui:VisualElement>
            <ui:VisualElement class="input-group">
                <ui:Label text="预设颜色:" class="input-label" />
                <ui:VisualElement name="PresetColorsGrid" class="preset-colors-grid" style="flex-direction: row; flex-wrap: wrap;" />
            </ui:VisualElement>
            <ui:VisualElement class="input-group">
                <ui:Label text="自定义颜色:" class="input-label" />
                <ui:VisualElement name="RGBContainer">
                    <ui:VisualElement class="color-input-row" style="flex-direction: row; justify-content: flex-start; max-width: 100%;">
                        <ui:Label text="R:" style="color: rgb(255, 102, 102);" />
                        <ui:Slider name="RedSlider" low-value="0" high-value="255" value="255" style="max-width: initial; width: initial;" />
                        <ui:IntegerField name="RedField" value="255" style="position: relative; width: auto; max-width: none;" />
                    </ui:VisualElement>
                    <ui:VisualElement class="color-input-row" style="flex-direction: row;  ">
                        <ui:Label text="G:" style="color: rgb(102, 255, 102);" />
                        <ui:Slider name="GreenSlider" low-value="0" high-value="255" value="255" />
                        <ui:IntegerField name="GreenField" value="255" />
                    </ui:VisualElement>
                    <ui:VisualElement class="color-input-row" style="flex-direction: row;  ">
                        <ui:Label text="B:" style="color: rgb(102, 102, 255);" />
                        <ui:Slider name="BlueSlider" low-value="0" high-value="255" value="255" />
                        <ui:IntegerField name="BlueField" value="255" />
                    </ui:VisualElement>
                    <ui:VisualElement class="color-input-row" style="flex-direction: row;  ">
                        <ui:Label text="A:" style="color: rgb(204, 204, 204);" />
                        <ui:Slider name="AlphaSlider" low-value="0" high-value="255" value="255" />
                        <ui:IntegerField name="AlphaField" value="255" />
                    </ui:VisualElement>
                </ui:VisualElement>
                <ui:VisualElement class="color-input-row hex-input" style="flex-direction: row; align-items: stretch;">
                    <ui:Label text="Hex:" />
                    <ui:TextField name="HexField" value="#FFFFFF" style="flex-grow: 1;" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement class="message-buttons">
            <ui:Button name="ConfirmButton" text="确认" class="message-button primary-button" />
            <ui:Button name="CancelButton" text="取消" class="message-button secondary-button" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
