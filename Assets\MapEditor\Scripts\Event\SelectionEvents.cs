using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Event
{
    /// <summary>
    /// 选择变更事件
    /// </summary>
    public class SelectionChangedEvent
    {
        /// <summary>
        /// 被选中的GameObject列表
        /// </summary>
        public List<GameObject> SelectedObjects { get; set; }
        
        /// <summary>
        /// 被选中的对象实例列表（用于ObjectTool）
        /// </summary>
        public List<Data.Chunks.ObjectInstance> SelectedObjectInstances { get; set; }
        
        /// <summary>
        /// 发起选择的工具类型
        /// </summary>
        public string SourceToolId { get; set; }
        
        /// <summary>
        /// 是否是多选
        /// </summary>
        public bool IsMultiSelect => 
            (SelectedObjects != null && SelectedObjects.Count > 1) ||
            (SelectedObjectInstances != null && SelectedObjectInstances.Count > 1);
        
        /// <summary>
        /// 总选中数量
        /// </summary>
        public int SelectionCount => 
            (SelectedObjects?.Count ?? 0) + (SelectedObjectInstances?.Count ?? 0);
    }

    /// <summary>
    /// 请求选择对象事件
    /// </summary>
    public class RequestSelectObjectEvent
    {
        /// <summary>
        /// 要选择的对象ID
        /// </summary>
        public string ObjectId { get; set; }
        
        /// <summary>
        /// 是否多选
        /// </summary>
        public bool MultiSelect { get; set; }
    }
} 