using System.Linq;
using MapEditor.Core;
using MapEditor.Event;
using MapEditor.Tools;
using UnityEngine;

namespace MapEditor.Services
{
    /// <summary>
    /// 工具服务，负责工具的注册和管理
    /// </summary>
    public class ToolService : ServiceBase
    {
        private  IToolManager toolManager;

        public override void Initialize()
        {
            // 在服务初始化阶段就获取 ToolManager，保证对外接口立即可用
            toolManager = GetService<ToolManager>();

            // 注册事件
            RegisterEvent<ActiveLayerChangedEvent>(OnActiveLayerChanged);

            // 注册默认工具，确保 UI 在第一帧即可获取到完整工具列表
            RegisterDefaultTools();
        }

        public override void Start()
        {
            // Start 阶段暂不需要额外逻辑，保留空实现以备后续扩展
        }

        /// <summary>
        /// 注册默认工具
        /// </summary>
        private void RegisterDefaultTools()
        {
            // 注册选择工具
            var selectionTool = new SelectionTool(core);
            toolManager.RegisterTool(selectionTool);
            
            // 为选择工具注册对象层的行为和UI
            Debug.Log("开始注册对象层选择行为和UI");
            var objectSelectionBehavior = new Tools.Selection.ObjectSelectionBehavior(core);
            selectionTool.RegisterSelectionBehavior(objectSelectionBehavior);
            Debug.Log($"已注册对象层选择行为，支持的图层类型: {objectSelectionBehavior.SupportedLayerType}");
            
            // 注册地表层选择行为和 UI
            var tilemapSelectionBehavior = new Tools.Selection.TilemapSelectionBehavior(core);
            selectionTool.RegisterSelectionBehavior(tilemapSelectionBehavior);

            var tilemapSelectionUI = new Tools.Selection.TilemapSelectionUI();
            selectionTool.RegisterSelectionUI(tilemapSelectionUI);

            var objectSelectionUI = new Tools.Selection.ObjectSelectionUI();
            selectionTool.RegisterSelectionUI(objectSelectionUI);
            Debug.Log($"已注册对象层选择UI，支持的图层类型: {objectSelectionUI.SupportedLayerType}");

            // 注册画笔工具
            var brushTool = new BrushTool(core);
            toolManager.RegisterTool(brushTool);

            // 注册对象绘制工具
            var objectToolPaint = new ObjectToolPaint(core);
            toolManager.RegisterTool(objectToolPaint);

            // 注册曲线工具
            var curveTool = new CurveTool(core);
            toolManager.RegisterTool(curveTool);

            Debug.Log("默认工具注册完成");
            
            // 默认激活选择工具
            toolManager.ActivateTool("SelectionTool");
        }
        
        /// <summary>
        /// 处理图层切换事件
        /// </summary>
        private void OnActiveLayerChanged(ActiveLayerChangedEvent evt)
        {
            if (evt.NewLayer == null)
            {
                return;
            }
            
            // 检查当前工具是否支持新图层
            var currentTool = toolManager.ActiveTool;
            if (currentTool != null && !currentTool.SupportsLayerType(evt.NewLayer.Type))
            {
                // 当前工具不支持新图层，切换到选择工具
                Debug.Log($"当前工具 {currentTool.DisplayName} 不支持 {evt.NewLayer.Type} 图层，切换到选择工具");
                toolManager.ActivateTool("SelectionTool");
            }
        }

        /// <summary>
        /// 获取工具管理器
        /// </summary>
        public IToolManager GetToolManager()
        {
            return toolManager;
        }

        /// <summary>
        /// 获取指定工具
        /// </summary>
        public IMapTool GetTool(string toolId)
        {
            return toolManager.GetTool(toolId);
        }

        /// <summary>
        /// 激活工具
        /// </summary>
        public void ActivateTool(string toolId)
        {
            // 检查工具是否支持当前活动图层
            var tool = toolManager.GetTool(toolId);
            var activeLayer = core.LayerStore?.ActiveLayer;
            
            if (tool != null && activeLayer != null && !tool.SupportsLayerType(activeLayer.Type))
            {
                Debug.LogWarning($"工具 {tool.DisplayName} 不支持当前图层类型 {activeLayer.Type}");
                return;
            }
            
            toolManager.ActivateTool(toolId);
        }

        /// <summary>
        /// 获取所有工具的ID
        /// </summary>
        /// <returns>工具ID数组</returns>
        public string[] GetAllToolIds()
        {
            // 增加空检查，避免在极端情况下抛出空引用
            return toolManager != null ? toolManager.GetAllToolIds().ToArray() : System.Array.Empty<string>();
        }
        
        /// <summary>
        /// 获取当前图层可用的工具ID列表
        /// </summary>
        /// <returns>可用工具ID数组</returns>
        public string[] GetAvailableToolIds()
        {
            var activeLayer = core.LayerStore?.ActiveLayer;
            if (activeLayer == null || toolManager == null)
            {
                // 没有活动图层或 ToolManager 尚未就绪时，只有选择工具可用
                return new string[] { "SelectionTool" };
            }
            
            var availableTools = toolManager.GetToolsForLayerType(activeLayer.Type);
            return availableTools.Select(t => t.ToolId).ToArray();
        }
    }
}