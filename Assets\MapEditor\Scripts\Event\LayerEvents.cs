using MapEditor.Core;

namespace MapEditor.Event
{
    /// <summary>
    /// 活动图层变更事件
    /// </summary>
    public class ActiveLayerChangedEvent
    {
        /// <summary>
        /// 旧图层
        /// </summary>
        public IMapLayer OldLayer { get; set; }
        
        /// <summary>
        /// 新图层
        /// </summary>
        public IMapLayer NewLayer { get; set; }
    }

    /// <summary>
    /// 图层渲染动作枚举
    /// </summary>
    public enum LayerRenderAction
    {
        Create,
        Remove,
        ClearAll,
        UpdateVisibility,
        UpdateAll
    }

    /// <summary>
    /// 图层渲染请求事件，由 LayerRenderService 发送，SceneRenderManager 响应
    /// </summary>
    public struct LayerRenderRequestEvent
    {
        public LayerRenderAction Action;
        public IMapLayer Layer;
        public bool Visibility;
    }

    /// <summary>
    /// 渲染器已创建/注册事件，由 SceneRenderManager 发送，LayerRenderService 接收
    /// </summary>
    public struct LayerRendererRegisteredEvent
    {
        public IMapLayer Layer;
        public LayerRenderer Renderer;
    }
} 