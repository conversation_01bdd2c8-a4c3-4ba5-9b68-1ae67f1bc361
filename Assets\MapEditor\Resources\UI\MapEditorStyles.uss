/* Map Editor 主样式表 */

:root {
    --color-background: #2D2D2D;
    --color-background-light: #3C3C3C;
    --color-text: #E0E0E0;
    --color-text-dark: #BBBBBB;
    --color-accent: #4990E2;
    --color-accent-dark: #3A73B4;
    --color-border: #555555;
    --color-success: #4CAF50;
    --color-warning: #FFC107;
    --color-error: #F44336;
    --font-size-small: 12px;
    --font-size-normal: 14px;
    --font-size-large: 16px;
    --spacing-small: 4px;
    --spacing-normal: 8px;
    --spacing-large: 16px;
    -unity-font: resource("Fonts & Materials/NotoSansSC-Regular");
 
}

/* 全局样式 */
.map-editor-ui {
    color: var(--color-text);
    font-size: var(--font-size-normal);
    width: 100%;
    height: 100%;
}

/* 布局容器 */
.panel {
    background-color: var(--color-background-light);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
    padding: var(--spacing-normal);
    height: 100%;
}

.panel-title {
    -unity-font-style: bold;
    font-size: var(--font-size-large);
    margin-bottom: var(--spacing-normal);
    color: var(--color-accent);
    border-bottom-width: 1px;
    border-bottom-color: var(--color-border);
    padding-bottom: var(--spacing-small);
}

/* 工具栏样式 */
.toolbar {
    flex-direction: row;
    background-color: var(--color-background);
    border-bottom-width: 1px;
    border-bottom-color: var(--color-border);
    padding: var(--spacing-small);
    align-items: center;
}

#ToolButtonContainer {
    flex-direction: row;
    align-items: center;
    margin-right: 20px;
    margin-left: 80px;
}

.tool-button {
    margin-right: var(--spacing-small);
}

/* UI Toolkit doesn't support :last-child, handle margin in code if needed */

.tool-button-text {
    min-width: 80px;
    height: 30px;
    padding: 0 16px;
    margin: 0;
    border-radius: 4px;
    background-color: var(--color-background-light);
    color: var(--color-text);
    border-width: 1px;
    border-color: transparent;
    -unity-text-align: middle-center;
    font-size: var(--font-size-normal);
    transition-duration: 0.2s;
}

.tool-button-text:hover:enabled {
    background-color: rgba(73, 144, 226, 0.2);
    border-color: var(--color-accent);
    margin-top: -1px;
}

.tool-button-text:active:enabled {
    margin-top: 0;
}

.tool-button-text.active {
    background-color: var(--color-accent);
    color: white;
    border-color: var(--color-accent);
    -unity-font-style: bold;
}

.tool-button-text.active:hover {
    background-color: var(--color-accent-dark);
    border-color: var(--color-accent-dark);
}

.tool-button.disabled .tool-button-text {
    opacity: 0.4;
    color: var(--color-text-dark);
    background-color: rgba(60, 60, 60, 0.5);
}

.tool-button.disabled .tool-button-text:hover {
    background-color: rgba(60, 60, 60, 0.5);
    border-color: transparent;
    margin-top: 0;
}

/* 左侧面板 */
.left-panel {
    background-color: var(--color-background);
    border-right-width: 1px;
    border-right-color: var(--color-border);
}

/* 右侧面板 */
.right-panel {
    background-color: var(--color-background);
    border-left-width: 1px;
    border-left-color: var(--color-border);
}

/* 图层面板 */
.layer-list {
    flex-grow: 1;
    overflow: hidden;
}

.layer-item {
    height: auto;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-small);
    border-bottom-width: 1px;
    border-bottom-color: var(--color-border);
    background-color: var(--color-background-light);
}

.layer-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.layer-item.selected {
    background-color: var(--color-accent-dark);
}

.layer-type-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-small);
}

.grid-layer-icon {
    background-color: #888888;
    border-radius: 2px;
}

.layer-name {
    flex-grow: 1;
    -unity-text-align: middle-left;
}

.layer-controls {
    flex-direction: row;
    align-items: center;
}

.visibility-toggle {
    margin-right: var(--spacing-small);
}

.lock-toggle {
    margin-right: var(--spacing-small);
}

/* 属性检查器 */
.property-field {
    margin-bottom: var(--spacing-normal);
}

.property-label {
    margin-bottom: var(--spacing-small);
    color: var(--color-text-dark);
}

.property-input {
    width: 100%;
}

.vector3-container {
    flex-direction: row;
    justify-content: space-between;
}

.vector-component {
    flex: 1 1 auto;
    min-width: 0;
    max-width: 100px;
    margin-right: var(--spacing-small);
}

.vector-component {
    margin-right: 0;
}

/* FloatField内的Label和Input布局修正 */
.vector-component .unity-base-field__label {
    flex: 0 0 auto;
    width: 15px;
    min-width: 15px;
    max-width: 15px;
    margin-right: 4px;
    -unity-text-align: middle-center;
}

.vector-component .unity-base-field__input {
    flex: 1 1 auto;
    min-width: 0;
}

/* 按钮样式 */
.button {
    background-color: var(--color-accent);
    color: white;
    border-radius: 3px;
    padding: var(--spacing-small) var(--spacing-normal);
    margin: var(--spacing-small);
    border-width: 0;
}

.button:hover {
    background-color: var(--color-accent-dark);
}

.button-small {
    font-size: var(--font-size-small);
    padding: 2px var(--spacing-small);
}

/* 消息框按钮样式 */
.message-button {
    background-color: var(--color-accent);
    color: var(--color-text);
    border-radius: 3px;
    padding: var(--spacing-small) var(--spacing-large);
    margin: var(--spacing-small);
    border-width: 0;
    min-width: 80px;
}

.message-button:hover {
    background-color: var(--color-accent-dark);
}

.primary-button {
    background-color: var(--color-accent);
}

.primary-button:hover {
    background-color: var(--color-accent-dark);
}

.secondary-button {
    background-color: var(--color-background);
    border-width: 1px;
    border-color: var(--color-border);
}

/* 消息框 */
.message-box {
    position: absolute;
    width: 400px;
    background-color: var(--color-background-light);
    border-radius: 5px;
    border-width: 1px;
    border-color: var(--color-border);
    padding: var(--spacing-large);
    top: 50%;
    left: 50%;
    translate: -50% 50%;
    color: var(--color-text);
}

.message-title {
    font-size: var(--font-size-large);
    -unity-font-style: bold;
    margin-bottom: var(--spacing-normal);
    color: var(--color-text);
}

.message-content {
    margin-bottom: var(--spacing-large);
    color: var(--color-text);
}

.message-buttons {
    flex-direction: row;
    justify-content: flex-end;
}

.info-message {
    border-color: var(--color-accent);
}

.success-message {
    border-color: var(--color-success);
}

.warning-message {
    border-color: var(--color-warning);
}

.error-message {
    border-color: var(--color-error);
}

/* 确认对话框 */
.dialog-buttons {
    flex-direction: row;
    justify-content: flex-end;
}

/* 底部状态栏 */
.status-bar {
    background-color: var(--color-background);
    border-top-width: 1px;
    border-top-color: var(--color-border);
    flex-direction: row;
    align-items: center;
    padding: 0 var(--spacing-normal);
}

.status-text {
    font-size: var(--font-size-small);
    color: var(--color-text-dark);
}

/* 添加针对特定对话框内元素的颜色设置 */
.input-label, .form-label {
    color: var(--color-text);
}

.input-field, .form-input {
    color: var(--color-text);
    background-color: var(--color-background);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
    padding: var(--spacing-small);
}

.input-field:focus, .form-input:focus {
    border-color: var(--color-accent);
}

.input-group {
    margin-bottom: var(--spacing-normal);
}

/* 输入框样式 */
TextField, DropdownField, IntegerField {
    color: var(--color-text);
}

TextField > TextInput, 
DropdownField > TextElement, 
IntegerField > TextInput {
    color: var(--color-text);
    background-color: var(--color-background);
    border-color: var(--color-border);
}

/* 确保下拉框和整数输入框有正确的背景色 */
.unity-base-dropdown__container-inner,
.unity-base-dropdown__container-outer,
.unity-popup-field__input,
.unity-base-popup-field__input,
.unity-base-popup-field__text,
.unity-integer-field__input,
.unity-text-field__input,
.unity-base-field__input {
    background-color: var(--color-background) !important;
    color: var(--color-text) !important;
}

.unity-popup-field {
    background-color: var(--color-background) !important;
}

/* 下拉菜单项样式 */
.unity-base-dropdown__item {
    background-color: var(--color-background) !important;
    color: var(--color-text) !important;
}

.unity-base-dropdown__item:hover {
    background-color: var(--color-accent-dark) !important;
}

/* 自定义尺寸输入框样式 */
.size-input {
    width: 70px;
    color: var(--color-text);
    background-color: var(--color-background);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
}

.size-input > TextInput,
.size-input .unity-base-field__input {
    color: var(--color-text) !important;
    background-color: var(--color-background) !important;
}

/* Checkbox 样式 */
Toggle {
    margin: var(--spacing-small);
}

Toggle > .unity-toggle__input {
    background-color: var(--color-background);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 2px;
}

Toggle:checked > .unity-toggle__input {
    background-color: var(--color-accent);
    border-color: var(--color-accent);
}

Toggle > Label {
    color: var(--color-text);
    margin-left: var(--spacing-small);
}

/* 确保对话框内所有文本元素都使用正确的颜色 */
.message-box Label, 
.message-box TextField, 
.message-box DropdownField,
.message-box IntegerField,
.message-box Toggle {
    color: var(--color-text);
}

.message-box TextField > TextInput, 
.message-box DropdownField > TextElement, 
.message-box IntegerField > TextInput,
.message-box .unity-base-field__input,
.message-box .unity-popup-field__input,
.message-box .unity-integer-field__input {
    color: var(--color-text) !important;
    background-color: var(--color-background) !important;
}

.message-box DropdownField,
.message-box IntegerField {
    background-color: var(--color-background);
}

/* 图标网格，用于笔刷/纹理选择 */
.icon-grid {
    flex-direction: row;
    flex-wrap: wrap;
}

.icon-button {
    width: 32px;
    height: 32px;
    border-width: 1px;
    border-color: var(--color-border);
    border-radius: 2px;
    padding: 0;
    margin: var(--spacing-small);
}

.icon-button.selected {
    border-color: var(--color-accent);
}

/* 右侧属性面板滚动条优化 */
#RightPanel .unity-scroll-view__vertical-scroller {
    width: 6px;
}

/* 隐藏滚动条箭头按钮，保持简洁 */
#RightPanel .unity-scroller__low-button,
#RightPanel .unity-scroller__high-button {
    display: none;
}

/* 网格设置面板样式 */
.panel-container {
    background-color: var(--color-background-light);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
    padding: var(--spacing-normal);
    margin: var(--spacing-small);
}

.settings-content {
    flex-direction: column;
}

.setting-row {
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--spacing-normal);
    justify-content: space-between;
}

.setting-label {
    min-width: 100px;
    color: var(--color-text);
    font-size: var(--font-size-normal);
    margin-right: var(--spacing-normal);
}

.vector2-field {
    flex-grow: 1;
    max-width: 120px;
}

.integer-field, .float-field {
    flex-grow: 1;
    max-width: 80px;
}

.color-field {
    flex-grow: 1;
    max-width: 100px;
}

/* 对象工具面板样式 */
.tool-panel {
    background-color: var(--color-background-light);
    padding: var(--spacing-normal);
    border-width: 1px;
    border-color: var(--color-border);
}

.button-group {
    flex-direction: row;
    margin-bottom: var(--spacing-normal);
}

.mode-button {
    flex-grow: 1;
    margin-right: var(--spacing-small);
    background-color: var(--color-background);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
    padding: var(--spacing-small);
}

.mode-button.selected {
    background-color: var(--color-accent);
    color: white;
}

.mode-panel {
    margin-top: var(--spacing-normal);
}

.mode-panel.hidden {
    display: none;
}

.object-list {
    max-height: 200px;
    background-color: var(--color-background);
    border-width: 1px;
    border-color: var(--color-border);
    border-radius: 3px;
}

.prefab-list {
    max-height: 150px;
    background-color: var(--color-background);
    border-width: 1px;
    border-color: var(--color-border);
    border-radius: 3px;
}

.object-list-item, .prefab-list-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-small);
    border-bottom-width: 1px;
    border-bottom-color: var(--color-border);
}

.object-list-item.selected, .prefab-list-item.selected {
    background-color: rgba(73, 144, 226, 0.3);
    border-color: var(--color-accent);
    border-width: 1px;
}

.property-row {
    flex-direction: row;
    align-items: center;
    margin-bottom: var(--spacing-small);
}

.property-label {
    min-width: 60px;
    margin-right: var(--spacing-normal);
    color: var(--color-text-dark);
}

.property-value {
    flex-direction: row;
    flex-grow: 1;
    min-width: 0;
}

.apply-button {
    background-color: var(--color-success);
    color: white;
    border-radius: 3px;
    border-width: 0;
    margin-top: var(--spacing-normal);
    padding: var(--spacing-small) var(--spacing-normal);
}

.delete-button {
    background-color: var(--color-error);
    color: white;
    border-radius: 3px;
    border-width: 0;
    margin-top: var(--spacing-small);
    padding: var(--spacing-small) var(--spacing-normal);
}

.section {
    margin-bottom: var(--spacing-large);
}

.section-title {
    font-size: var(--font-size-large);
    -unity-font-style: bold;
    margin-bottom: var(--spacing-normal);
    color: var(--color-accent);
}

.subsection-title {
    font-size: var(--font-size-normal);
    -unity-font-style: bold;
    margin-bottom: var(--spacing-small);
    color: var(--color-text);
}

/* 图层面板样式 - 仿PS设计 */
.expand-button {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    font-size: 10px;
    padding: 0;
    background-color: transparent;
    border-width: 0;
    color: #cccccc;
}

.layer-item {
    background-color: rgba(55, 55, 55, 0.9);
    border-bottom-width: 1px;
    border-bottom-color: rgba(255, 255, 255, 0.08);
}

.layer-item:hover {
    background-color: rgba(75, 75, 75, 0.9);
}

.layer-item.selected {
    background-color: rgba(0, 119, 204, 0.4) !important;
}

.layer-main {
    padding: 6px var(--spacing-small);
    min-height: 24px;
}

.layer-name {
    font-size: 12px;
    color: #e8e8e8;
    -unity-font-style: normal;
}

.layer-item.selected .layer-name {
    color: white;
    -unity-font-style: bold;
}

.layer-type-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    border-radius: 2px;
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.2);
}

.object-layer-icon {
    background-color: #d4822a; /* 橙色代表对象图层 */
}

.ground-layer-icon {
    background-color: #228B22; /* 绿色代表地形图层 */
}

.decoration-layer-icon {
    background-color: #9370DB; /* 紫色代表装饰图层 */
}

.logic-layer-icon {
    background-color: #DC143C; /* 红色代表逻辑图层 */
}

.layer-object-list {
    background-color: rgba(35, 35, 35, 0.95);
    border-left-width: 2px;
    border-left-color: rgba(0, 119, 204, 0.3);
    width: 100%;
}

/* PS风格的对象项 */
.ps-object-item {
    background-color: rgba(45, 45, 45, 0.9);
    border-bottom-width: 1px;
    border-bottom-color: rgba(255, 255, 255, 0.03);
    min-height: 22px;
}

.ps-object-item:hover {
    background-color: rgba(65, 65, 65, 0.9);
}

.ps-object-item.selected {
    background-color: rgba(0, 119, 204, 0.7) !important;
}

.ps-object-icon {
    flex-shrink: 0;
}

.ps-object-name {
    font-size: 11px;
    color: #e8e8e8;
    -unity-font-style: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ps-object-item.selected .ps-object-name {
    color: white;
    -unity-font-style: bold;
}

.ps-visibility-icon {
    flex-shrink: 0;
    opacity: 0.6;
}

.ps-visibility-icon:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
}

.empty-object-list {
    -unity-font-style: italic;
    color: #777777;
    font-size: 10px;
    padding: 8px 24px;
    background-color: rgba(35, 35, 35, 0.8);
    border-bottom-width: 1px;
    border-bottom-color: rgba(255, 255, 255, 0.02);
    -unity-text-align: upper-left;
}

/* 图层和对象名称编辑样式 */
.layer-name-container {
    flex-direction: column;
    flex-grow: 1;
}

.layer-name-edit {
    font-size: var(--font-size-normal);
    background-color: var(--color-background);
    color: var(--color-text);
    border-color: var(--color-accent);
    border-width: 1px;
    border-radius: 2px;
    padding: 2px 4px;
    margin: 0;
}

.layer-name-edit:focus {
    border-color: var(--color-accent);
    background-color: var(--color-background);
}

.ps-object-name-edit {
    font-size: 11px;
    background-color: var(--color-background);
    color: var(--color-text);
    border-color: var(--color-accent);
    border-width: 1px;
    border-radius: 2px;
    padding: 1px 3px;
    margin: 0;
    flex-grow: 1;
}

.ps-object-name-edit:focus {
    border-color: var(--color-accent);
    background-color: var(--color-background);
}

/* 确保编辑框内的文本输入样式正确 */
.layer-name-edit > .unity-text-field__input,
.ps-object-name-edit > .unity-text-field__input {
    background-color: transparent;
    color: var(--color-text);
    border-width: 0;
    padding: 0;
    margin: 0;
}

/* 对象预览容器样式 */
.object-preview-container {
    background-color: rgba(40, 40, 40, 0.8);
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    margin-top: 8px;
}

.object-preview-icon {
    flex-shrink: 0;
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.2);
    /* 黑色背景便于预览 */
    background-color: #000000;
    /* 放大对象预览尺寸 */
    min-width: 96px;
    min-height: 96px;
}

.object-name-label {
    font-size: 16px;
    -unity-font-style: bold;
    color: #ffffff;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.object-type-label {
    font-size: 12px;
    color: #aaaaaa;
    -unity-font-style: italic;
}

/* 确保属性行的名称字段正确显示 */
.property-row TextField {
    flex-grow: 1;
    margin: 0;
}

.property-row TextField .unity-text-field__input {
    background-color: var(--color-background);
    color: var(--color-text);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
    padding: 4px;
}

/* 预制体搜索和树形视图样式 */
.search-container {
    margin-bottom: var(--spacing-normal);
    padding: var(--spacing-small);
    background-color: var(--color-background);
    border-radius: 3px;
}

.search-field {
    width: 100%;
    height: 24px;
    font-size: var(--font-size-normal);
}

.search-field > .unity-text-field__input {
    background-color: var(--color-background-light);
    border-color: var(--color-border);
    border-width: 1px;
    border-radius: 3px;
    padding-left: var(--spacing-small);
    color: var(--color-text);
}

.search-field > .unity-text-field__input:focus {
    border-color: var(--color-accent);
}

/* 树形视图样式 */
.prefab-tree-view {
    flex-grow: 1;
    background-color: var(--color-background);
    border-width: 1px;
    border-color: var(--color-border);
    border-radius: 3px;
    min-height: 300px;
    max-height: 400px;
}

.prefab-tree-view > .unity-scroll-view {
    flex-grow: 1;
}

.prefab-tree-view .unity-tree-view__item {
    height: 24px;
    flex-direction: row;
    align-items: center;
    padding-left: var(--spacing-small);
}

.prefab-tree-view .unity-tree-view__item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.prefab-tree-view .unity-tree-view__item--selected {
    background-color: var(--color-accent-dark);
}

.prefab-tree-view .unity-tree-view__item-toggle {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-small);
}

.prefab-tree-view .unity-tree-view__item-content {
    flex-direction: row;
    align-items: center;
    flex-grow: 1;
}

/* 预制体项图标和文本 */
.prefab-item-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-small);
    background-color: var(--color-accent);
    border-radius: 2px;
}

.prefab-item-label {
    flex-grow: 1;
    color: var(--color-text);
    font-size: var(--font-size-normal);
    -unity-text-align: middle-left;
}

.prefab-folder-icon {
    background-color: var(--color-warning);
}

/* 滚动条样式优化 */
.prefab-tree-view .unity-scroll-view__vertical-scroller {
    width: 8px;
}

.prefab-tree-view .unity-scroller__slider {
    background-color: var(--color-border);
    border-radius: 4px;
}

.prefab-tree-view .unity-scroller__slider:hover {
    background-color: var(--color-accent-dark);
}

/* 选中的预制体显示 */
.selected-prefab-container {
    flex-direction: row;
    align-items: center;
    padding: var(--spacing-normal);
    background-color: var(--color-background);
    border-radius: 4px;
    margin-bottom: var(--spacing-normal);
}

.selected-prefab-icon {
    /* 放大预览图标尺寸以获得更清晰的预览 */
    width: 96px;
    height: 96px;
    margin-right: var(--spacing-normal);
    /* 黑色背景便于突显图片 */
    background-color: #000000;
    border-radius: 4px;
}

.selected-prefab-name {
    flex-grow: 1;
    font-size: var(--font-size-large);
    color: var(--color-text);
}

/* 颜色选择器样式 */
.color-picker-dialog {
    position: absolute;
    background-color: var(--color-background-light);
    border-color: var(--color-border);
    border-width: 2px;
    border-radius: 6px;
    padding: var(--spacing-large);
    min-width: 400px;
    min-height: 350px;
    top: 50%;
    left: 50%;
    translate: -50% 30%;
}

.color-input-row {
    margin-bottom: var(--spacing-small);
    align-items: stretch;
}

.color-input-row Slider {
    margin-right: var(--spacing-normal);
    flex-grow: 1;
}

.color-input-row IntegerField {
    width: var(--numeric-input-width, 50px);

}

.color-input-row Label {
    width: var(--color-input-label-width, 20px);
}

.color-preview {
    border-width: 2px;
    border-color: var(--color-border);
    border-radius: 3px;
    min-width: var(--preview-min-width, 100px);
    min-height: var(--preview-min-height, 40px);
    flex-grow: 1;
}

.preset-colors-grid {
    background-color: var(--color-background);
    border-width: 1px;
    border-color: var(--color-border);
    border-radius: 3px;
    padding: var(--spacing-small);
    min-height: 100px;
}

.color-preset-button {
    width: 25px;
    height: 25px;
    border-width: 1px;
    border-color: var(--color-border);
    border-radius: 2px;
    margin: 2px;
}

.color-preset-button:hover {
    border-color: var(--color-accent);
    border-width: 2px;
}

.hex-input TextField {
    /* UI Toolkit 2022 LTS 不支持 font-family，可在必要时通过 -unity-font 引用具体字体资产 */
}

.hex-input Label {
    width: var(--hex-label-width, 40px);
}

/* Layer Settings Dialog specific */
.layer-settings-dialog .unity-integer-field__input,
.layer-settings-dialog .unity-text-field__input {
    height: 100%;
    min-height: 24px;
} 