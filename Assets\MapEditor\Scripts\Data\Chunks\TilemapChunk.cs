using System;
using MapEditor.Core;
using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Data.Chunks
{
    [Serializable]
    public partial class TilemapChunk : ChunkBase
    {
        // Unity 无法序列化 Guid 类型，用 string 保存
        [SerializeField] private string tilemapId;
        [SerializeField] private int chunkSize;

        /// <summary>
        /// 对外只读的 Tilemap 唯一标识符。
        /// </summary>
        public string TilemapId { get => tilemapId; set => tilemapId = value; }

        /// <summary>
        /// Chunk 边长(像素)。
        /// </summary>
        public int ChunkSize => chunkSize;

        public TilemapChunk(ChunkCoord coord, int chunkSize) : base(coord)
        {
            tilemapId = Guid.NewGuid().ToString();
            this.chunkSize = chunkSize;
        }
    }
} 