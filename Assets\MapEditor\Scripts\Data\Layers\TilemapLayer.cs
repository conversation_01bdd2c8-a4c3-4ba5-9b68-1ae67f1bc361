using MapEditor.Core;
using UnityEngine;

namespace MapEditor.Data.Layers
{
    /// <summary>
    /// 基于网格像素贴图的地表图层。
    /// </summary>
    [System.Serializable]
    public class TilemapLayer : MapLayer
    {
        public override LayerType Type => LayerType.Ground;

        // 旧的材质通道逻辑已被 Chunk 级索引表取代，此处保持空实现仅用于兼容序列化

        public TilemapLayer()
        {
            Order = -10;
        }
    }
}