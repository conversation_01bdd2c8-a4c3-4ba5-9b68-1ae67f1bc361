using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 所有图层渲染器的抽象基类，实现通用的 Chunk 刷新逻辑骨架。
    /// Template-Method：派生类只需负责创建 / 更新 / 释放 ChunkRenderProxy。
    /// </summary>
    public abstract class LayerRenderer : MonoBehaviour, ILayerRenderer
    {
        public string ID => LinkedLayer.LayerId;
        public IMapLayer LinkedLayer { get; protected set; }
        protected readonly Dictionary<ChunkCoord, ChunkRenderProxy> proxies = new();

        // 通用调度成员
        protected IChunkScheduler Scheduler = new SimpleChunkScheduler();
        private readonly HashSet<ChunkCoord> lastVisible = new();
        



        [SerializeField] protected int unloadRing = 2;
        [SerializeField] protected int preloadRing = 1;

        /// <summary>
        /// 供外部读取图层的排序权重（IMapLayer.Order）。
        /// </summary>
        public int LayerOrder => LinkedLayer != null ? LinkedLayer.Order : 0;

        public virtual void Initialize(IMapLayer layer)
        {
            LinkedLayer = layer;
        }
        

        /// <summary>
        /// 刷新视口内的 Chunk，可直接被 RenderManager 调用。
        /// 子类无需再实现差分逻辑，仅实现 Create/Update 钩子即可。
        /// </summary>
        public virtual void RefreshChunks(Bounds viewBounds)
        {
            // 已取消视锥剔除：保持所有已有代理可见，并更新其内容（如有必要）。
            foreach (var kv in proxies)
            {
                var proxy = kv.Value;
                if (proxy == null) continue;

                // 始终保持可见
                proxy.SetVisibility(true);

                // 可选：执行 UpdateProxy，以便响应数据变更
                var chunk = (LinkedLayer as MapLayer)?.GetOrCreateChunk<ChunkBase>(kv.Key);
                UpdateProxy(proxy, chunk);
            }
        }

        /// <summary>
        /// 创建指定 Chunk 的渲染代理（进入视野时调用）。
        /// </summary>
        protected abstract ChunkRenderProxy CreateProxy(ChunkCoord coord, ChunkBase chunk);




        /// <summary>
        /// 每帧更新已有渲染代理（如版本重建）。默认空实现。
        /// </summary>
        protected virtual void UpdateProxy(ChunkRenderProxy proxy, ChunkBase chunk) { }

        /// <summary>
        /// Chunk 离开视野时调用。默认 Dispose 资源。
        /// </summary>
        protected virtual void OnProxyRemoved(ChunkRenderProxy proxy)
        {
            proxy.Dispose();
        }

        /// <summary>
        /// 当图层被移除(而非整体系统销毁)时调用，用于执行该图层自定义的清理逻辑，例如删除未保存的临时资源。
        /// 基类默认空实现，派生类可根据需要覆盖。
        /// </summary>
        public virtual void OnLayerRemoved() { }

        /// <summary>
        /// 设置渲染器可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        public virtual void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }

        // ========= 通用工具 =========
        /// <summary>
        /// 判断给定像素坐标是否落在当前图层的 RealLayerSize 范围内。
        /// 当 LinkedLayer 不是 MapLayer 时默认返回 true。
        /// </summary>
        protected bool IsPixelInsideLayer(int pixelX, int pixelY)
        {
            if (LinkedLayer is MapLayer mapLayer)
            {
                Vector2Int size = mapLayer.RealLayerSize;
                return pixelX >= 0 && pixelX < size.x && pixelY >= 0 && pixelY < size.y;
            }
            return true;
        }

        /// <summary>
        /// 将世界坐标转换为像素坐标（Vector2Int）。
        /// 1 world unit = PixelsPerUnit 像素。
        /// </summary>
        protected static Vector2Int WorldToPixel(Vector2 worldPosition)
        {
            int px = Mathf.RoundToInt(worldPosition.x * MapEditorConfig.PixelsPerUnit);
            int py = Mathf.RoundToInt(worldPosition.y * MapEditorConfig.PixelsPerUnit);
            return new Vector2Int(px, py);
        }

        /// <summary>
        /// 将像素坐标转换为世界坐标（Vector2）。
        /// 1 world unit = PixelsPerUnit 像素。
        /// </summary>
        protected static Vector2 PixelToWorld(int pixelX, int pixelY)
        {
            float wx = pixelX / (float)MapEditorConfig.PixelsPerUnit;
            float wy = pixelY / (float)MapEditorConfig.PixelsPerUnit;
            return new Vector2(wx, wy);
        }

        /// <summary>
        /// 判断给定世界坐标是否落在当前图层范围内。
        /// 会自动转换为像素坐标再进行判断。
        /// </summary>
        protected bool IsWorldPositionInsideLayer(Vector2 worldPosition)
        {
            Vector2Int pix = WorldToPixel(worldPosition);
            return IsPixelInsideLayer(pix.x, pix.y);
        }
    }
} 