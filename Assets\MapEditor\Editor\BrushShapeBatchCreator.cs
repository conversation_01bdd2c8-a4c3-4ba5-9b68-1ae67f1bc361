using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using MapEditor.Data;

namespace MapEditor.EditorTools
{
    public class BrushShapeBatchCreator : EditorWindow
    {
        [MenuItem("Tools/MapEditor/Batch Create Brush Shapes", priority = 201)]
        private static void ShowWindow()
        {
            var win = GetWindow<BrushShapeBatchCreator>(true, "Batch Brush Shape Creator");
            win.minSize = new Vector2(400, 300);
        }

        private List<Texture2D> masks = new();
        private string saveFolder = "Assets/MapEditor/Resources/BrushShapes";

        private void OnGUI()
        {
            GUILayout.Label("拖入灰度蒙版纹理，一键生成 BrushShapeSO", EditorStyles.boldLabel);
            GUILayout.Space(4);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Selected Textures"))
            {
                foreach (var obj in Selection.objects)
                {
                    if (obj is Texture2D tex && !masks.Contains(tex)) masks.Add(tex);
                }
            }
            if (GUILayout.Button("Clear")) masks.Clear();
            EditorGUILayout.EndHorizontal();

            Rect area = GUILayoutUtility.GetRect(0, 60, GUILayout.ExpandWidth(true));
            GUI.Box(area, "Drag Masks Here", EditorStyles.helpBox);
            HandleDragDrop(area);

            GUILayout.Space(4);
            saveFolder = EditorGUILayout.TextField("Save Folder", saveFolder);

            GUILayout.Space(6);
            if (GUILayout.Button("Create/Update Assets")) CreateAssets();

            GUILayout.Label($"Pending Masks: {masks.Count}");
        }

        private void HandleDragDrop(Rect rect)
        {
            UnityEngine.Event e = UnityEngine.Event.current;
            switch (e.type)
            {
                case EventType.DragUpdated:
                case EventType.DragPerform:
                    if (!rect.Contains(e.mousePosition)) break;
                    DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                    if (e.type == EventType.DragPerform)
                    {
                        DragAndDrop.AcceptDrag();
                        foreach (var o in DragAndDrop.objectReferences)
                        {
                            if (o is Texture2D t && !masks.Contains(t)) masks.Add(t);
                        }
                    }
                    e.Use();
                    break;
            }
        }

        private void CreateAssets()
        {
            if (masks.Count == 0) { EditorUtility.DisplayDialog("No Masks", "请先拖入纹理", "OK"); return; }
            if (!AssetDatabase.IsValidFolder(saveFolder))
            {
                if (!EditorUtility.DisplayDialog("Folder not exist", $"{saveFolder} 不存在，是否创建？", "Yes", "No")) return;
                AssetDatabase.CreateFolder(System.IO.Path.GetDirectoryName(saveFolder), System.IO.Path.GetFileName(saveFolder));
            }

            foreach (var tex in masks)
            {
                string assetName = tex.name + "_Shape.asset";
                string path = System.IO.Path.Combine(saveFolder, assetName);

                BrushShapeSO so = AssetDatabase.LoadAssetAtPath<BrushShapeSO>(path);
                if (so == null)
                {
                    so = ScriptableObject.CreateInstance<BrushShapeSO>();
                    AssetDatabase.CreateAsset(so, path);
                }

                so.alphaMask = tex;
                EditorUtility.SetDirty(so);
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("Done", "BrushShapeSO assets created/updated.", "OK");
        }
    }
} 