<UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
      xmlns="UnityEngine.UIElements" 
      xsi:schemaLocation="UnityEngine.UIElements ../UIElementsSchema/UnityEngine.UIElements.xsd">
    
    <Style src="../MapEditorStyles.uss" />
    
    <VisualElement name="AddLayerDialog" class="message-box">
        <Label name="Title" text="添加图层" class="message-title" />
        
        <VisualElement class="form-container">
            <!-- 图层名称输入 -->
            <VisualElement class="form-row">
                <Label text="图层名称:" class="form-label" />
                <TextField name="LayerNameField" value="新图层" class="form-input" />
            </VisualElement>
            
            <!-- 图层类型选择 -->
            <VisualElement class="form-row">
                <Label text="图层类型:" class="form-label" />
                <DropdownField name="LayerTypeDropdown" class="form-input" />
            </VisualElement>
            
            <!-- 图层属性 -->
            <VisualElement class="form-row">
                <Label text="图层属性:" class="form-label" />
                <VisualElement class="form-toggles">
                    <Toggle name="VisibleToggle" text="可见" value="true" class="form-toggle" />
                    <Toggle name="LockedToggle" text="锁定" value="false" class="form-toggle" />
                </VisualElement>
            </VisualElement>
        </VisualElement>
        
        <!-- 按钮区域 -->
        <VisualElement class="dialog-buttons">
            <Button name="CancelButton" text="取消" class="button" />
            <Button name="ConfirmButton" text="确认" class="button button-primary" />
        </VisualElement>
    </VisualElement>
</UXML>