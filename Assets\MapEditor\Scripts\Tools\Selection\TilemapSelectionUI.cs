using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Data.Chunks;
using MapEditor.Services;

namespace MapEditor.Tools.Selection
{
    /// <summary>
    /// 地表层选择面板：材质网格 + Chunk 通道查看器。
    /// </summary>
    public class TilemapSelectionUI : ISelectionUI
    {
        private IUIManager uiManager;
        public LayerType SupportedLayerType => LayerType.Ground;

        private VisualElement root;
        private VisualElement materialGrid;
        private VisualElement channelViewer;
        private VisualElement containerRef;

        public void CreateSelectionPanel(VisualElement container, List<ISelectable> selectedObjects)
        {
            // 如果之前已经创建过面板，先清理，避免重复添加
            ClearSelectionPanel();

            containerRef = container;
            root = new VisualElement();
            root.style.flexDirection = FlexDirection.Column;
            container.Add(root);

            // 材质网格
            materialGrid = new VisualElement();
            materialGrid.AddToClassList("icon-grid");
            materialGrid.style.flexDirection = FlexDirection.Row;
            materialGrid.style.flexWrap = Wrap.Wrap;
            materialGrid.style.flexGrow = 1;
            materialGrid.style.maxHeight = 200;
            root.Add(new Label("所有地表材质:"));
            root.Add(materialGrid);

            // 通道查看
            root.Add(new Label("选中 Chunk 通道映射:"));
            channelViewer = new VisualElement();
            root.Add(channelViewer);

            BuildMaterialGrid();
            UpdateSelectionPanel(selectedObjects);
        }

        public void UpdateSelectionPanel(List<ISelectable> selectedObjects)
        {
            channelViewer.Clear();
            if (selectedObjects.Count == 0) { channelViewer.Add(new Label("未选中 Chunk")); return; }
            if (selectedObjects[0] is Tools.Selection.SelectableTilemapChunk stc)
            {
                BuildChannelViewer(stc.Chunk);
            }
        }

        public void ClearSelectionPanel()
        {
            if (root != null)
            {
                root.RemoveFromHierarchy();
            }
            root = null;
            containerRef = null;
        }

        public void CreateToolOptions(VisualElement container)
        {
            // 暂无
        }

        public void SetUIManager(IUIManager uiManager)
        {
            this.uiManager = uiManager;
        }

        private void BuildMaterialGrid()
        {
            materialGrid.Clear();
            var all = GroundTextureProvider.GetAllTextures();
            for (int i = 0; i < all.Length; i++)
            {
                var so = all[i];
                var btn = new Button();
                btn.AddToClassList("icon-button");
                btn.style.width = 32;
                btn.style.height = 32;
                btn.style.marginRight = 4;
                btn.style.marginBottom = 4;
                btn.style.backgroundImage = new StyleBackground(so.texture);
                btn.tooltip = $"global {so.textureIndex}, layer {GroundTextureArrayProvider.GetLayer(so.textureIndex)}";
                materialGrid.Add(btn);
            }
        }

        private void BuildChannelViewer(TilemapChunk chunk)
        {
            channelViewer.Clear();

            var layer = MapEditorCore.Instance.LayerStore.ActiveLayer as MapLayer;
            var renderer = MapEditorCore.Instance.GetService<LayerRenderService>()?.GetRenderer(layer) as Rendering.Layers.TilemapLayerRenderer;

            for (int ch = 0; ch < 7; ch++)
            {
                int layerIdx = chunk.MaterialIndexTable[ch];
                int globalIdx = -1;
                Texture2D iconTex = Texture2D.grayTexture;
                if (layerIdx >= 0)
                {
                    GlobalToTexture(layerIdx, out globalIdx, out iconTex);
                }

                var row = new VisualElement();
                row.style.flexDirection = FlexDirection.Row;
                row.style.alignItems = Align.Center;
                row.style.marginBottom = 2;

                var icon = new VisualElement();
                icon.style.width = 24;
                icon.style.height = 24;
                icon.style.backgroundImage = new StyleBackground(iconTex);
                icon.style.marginRight = 4;
                row.Add(icon);

                var label = new Label($"通道 {ch}: " + (layerIdx >= 0 ? $"layer {layerIdx} / global {globalIdx}" : "(空)"));
                label.style.flexGrow = 1;
                row.Add(label);

                var btn = new Button();
                btn.text = "×";
                btn.style.width = 20;
                btn.style.height = 20;
                btn.style.unityTextAlign = TextAnchor.MiddleCenter;
                if (layerIdx < 0) btn.SetEnabled(false);

                int capturedCh = ch;
                btn.clicked += () =>
                {
                    // 清空通道（通过反射访问私有数组）
                    chunk.ClearChannel(capturedCh);

                    // 清零权重像素
                    if (renderer != null)
                    {
                        var rts = renderer.GetOrCreateChunkRTsPublic(chunk.Coord);
                        if (capturedCh < 4)
                        {
                            Utility.TextureFillJobs.ClearChannel(rts[0], capturedCh);
                        }
                        else
                        {
                            Utility.TextureFillJobs.ClearChannel(rts[1], capturedCh - 4);
                        }
                        renderer.MarkChunkWeightsDirty(chunk.Coord);
                        renderer.RefreshAllProxyMaterialMappings();
                    }

                    // 刷新面板
                    UpdateSelectionPanel(new List<ISelectable> { new SelectableTilemapChunk(chunk, layer.LayerId) });
                };
                row.Add(btn);

                channelViewer.Add(row);
            }
        }

        private void GlobalToTexture(int layerIdx, out int globalIdx, out Texture2D tex)
        {
            globalIdx = -1;
            tex = Texture2D.grayTexture;
            // Reverse lookup: iterate all SO once
            var sos = GroundTextureProvider.GetAllTextures();
            foreach (var so in sos)
            {
                if (GroundTextureArrayProvider.GetLayer(so.textureIndex) == layerIdx)
                {
                    globalIdx = so.textureIndex;
                    tex = so.texture;
                    break;
                }
            }
        }
    }
} 