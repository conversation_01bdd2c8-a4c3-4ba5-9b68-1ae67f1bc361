// CurveStroke.compute
// 使用 S2+ 带硬度插值混合算法，实现高质量的曲线描边效果
// 支持 7 个材质通道(0-6) + 1个coverage通道，分布在两张 RGBA Texture (Splat0/1)

#pragma kernel CSCurveStroke
#pragma target 5.0

// 输出纹理 (现在是 Splat 贴图)
RWTexture2D<float4> _Splat0;
RWTexture2D<float4> _Splat1;

// 曲线段数据结构
struct CurveSegment
{
    float2 position;    // 曲线点位置
    float2 tangent;     // 切线方向 (归一化)
    float2 normal;      // 法向量 (归一化)
    float distance;     // 沿曲线距离
    float width;        // 局部宽度
};

// 曲线段缓冲区
StructuredBuffer<CurveSegment> _CurveSegments;

// 描边参数
float2 _ChunkOffset;        // Chunk在世界坐标的偏移
float _StrokeWidth;         // 描边宽度 (世界单位)
float _EdgeSoftness;        // 边缘柔和度 (0-1)
float _Strength;            // 绘制强度 (0-1)
int _SegmentCount;          // 曲线段数量
float _PixelsPerUnit;       // 像素密度

// 新增：从 BrushTool 借鉴的参数
uint    _Channel;           // 0-6 选中材质通道
float   _Hardness;          // 笔刷硬度 (0.1-5.0)


// 计算点到曲线段的距离（考虑变宽度）
float DistanceToSegment(float2 pos, CurveSegment segment1, CurveSegment segment2, float strokeWidth)
{
    float2 segmentStart = segment1.position;
    float2 segmentEnd = segment2.position;
    float2 segmentDir = normalize(segmentEnd - segmentStart);
    
    // 计算点在线段上的投影
    float2 toPoint = pos - segmentStart;
    float projection = dot(toPoint, segmentDir);
    float segmentLength = distance(segmentStart, segmentEnd);
    
    // 限制投影在线段范围内
    projection = clamp(projection, 0.0, segmentLength);
    
    // 计算最近点
    float2 closestPoint = segmentStart + segmentDir * projection;
    
    // 计算该点的描边宽度（支持变宽度）
    float t = segmentLength > 0 ? projection / segmentLength : 0.0;
    float localWidth = lerp(segment1.width, segment2.width, t) * strokeWidth;
    float halfWidth = localWidth * 0.5;
    
    // 计算到中心线的距离
    float distanceToCenter = distance(pos, closestPoint);
    
    // 返回到描边边缘的有符号距离
    return distanceToCenter - halfWidth;
}

// 计算点到整个曲线描边的距离
float CalculateDistanceToStroke(float2 worldPosition)
{
    if (_SegmentCount <= 1)
    {
        return 1000000.0; // 很大的值表示无限远
    }
    
    float minDistance = 1000000.0;
    
    // 遍历所有曲线段，找到最近距离
    for (int i = 0; i < _SegmentCount - 1; i++)
    {
        CurveSegment segment1 = _CurveSegments[i];
        CurveSegment segment2 = _CurveSegments[i + 1];
        
        float distance = DistanceToSegment(worldPosition, segment1, segment2, _StrokeWidth);
        
        if (abs(distance) < abs(minDistance))
        {
            minDistance = distance;
        }
    }
    
    return minDistance;
}

// 将距离转换为权重/蒙版值
float DistanceToMask(float distance, float strokeWidth, float edgeSoftness)
{
    if (distance <= 0.0)
    {
        // 在描边内部
        return 1.0;
    }
    
    // 计算柔和边缘的宽度
    float softEdgeWidth = strokeWidth * edgeSoftness * 0.5;
    
    if (softEdgeWidth <= 0.0)
    {
        // 硬边缘
        return 0.0;
    }
    
    // 使用平滑步函数创建柔和过渡
    float normalizedDistance = distance / softEdgeWidth;
    return 1.0 - smoothstep(0.0, 1.0, normalizedDistance);
}


[numthreads(8,8,1)]
void CSCurveStroke(uint3 id : SV_DispatchThreadID)
{
    uint width, height;
    _Splat0.GetDimensions(width, height);
    
    if (id.x >= width || id.y >= height)
        return;
    
    // 计算世界坐标位置
    float2 pixelCenter = float2(id.x + 0.5, id.y + 0.5);
    float2 worldPosition = _ChunkOffset + pixelCenter / _PixelsPerUnit;
    
    // 1. 基于距离计算 "虚拟画笔蒙版"
    float distance = CalculateDistanceToStroke(worldPosition);
    float mask = DistanceToMask(distance, _StrokeWidth, _EdgeSoftness);
    mask *= _Strength; // 应用强度

    if(mask <= 0) return;

    // ---------------- 读取数据 ----------------
    uint2 dst = id.xy;
    float4 w0 = _Splat0[dst];               // 材质通道0-3
    float4 w1 = _Splat1[dst];               // 材质通道4-6 + coverage

    // 提取7个材质权重与 coverage
    float weights[7];
    weights[0]=w0.r; weights[1]=w0.g; weights[2]=w0.b; weights[3]=w0.a;
    weights[4]=w1.r; weights[5]=w1.g; weights[6]=w1.b;

    float coverage = w1.a;                 // coverage通道 (0-1)

    uint tgt = _Channel;
    if(tgt >= 7) return; // 材质通道范围0-6，第7通道保留给coverage

    // -------------------------------------------------------------
    // S2+ 带硬度插值混合算法 (从 SplatBrush.compute 借鉴)
    // -------------------------------------------------------------
    
    // 2. 应用硬度并进行线性插值
    // 使用 saturate 确保 mask 不为负，避免 pow 函数产生 a NaN 警告
    float effectiveMask = pow(saturate(mask), _Hardness);
    float sum = 0;
    [unroll]
    for(uint j = 0; j < 7; ++j)
    {
        float target = (j == tgt) ? 1.0 : 0.0;
        weights[j] = lerp(weights[j], target, effectiveMask);
        sum += weights[j];
    }

    // 3. 归一化：W_new[i] = W_tmp[i] / sum
    float invSum = (sum > 1e-6) ? rcp(sum) : 0.0;
    [unroll]
    for(uint k = 0; k < 7; ++k)
    {
        weights[k] *= invSum;
    }

    // 4. coverage 保持原值
    // coverage = 1.0; // 可选：强制设为 1

    // ---------------- 写回 ----------------
    w0 = float4(weights[0], weights[1], weights[2], weights[3]);
    w1 = float4(weights[4], weights[5], weights[6], coverage);

    _Splat0[dst] = w0;
    _Splat1[dst] = w1;
} 