using System.Collections.Generic;
using UnityEngine.UIElements;

namespace MapEditor.Core
{
    /// <summary>
    /// 选择UI接口，定义图层特定的选择UI
    /// </summary>
    public interface ISelectionUI
    {
        /// <summary>
        /// 获取支持的图层类型
        /// </summary>
        LayerType SupportedLayerType { get; }
        
        /// <summary>
        /// 创建选择面板UI
        /// </summary>
        /// <param name="container">UI容器</param>
        /// <param name="selectedObjects">选中的对象</param>
        void CreateSelectionPanel(VisualElement container, List<ISelectable> selectedObjects);
        
        /// <summary>
        /// 更新选择面板
        /// </summary>
        /// <param name="selectedObjects">选中的对象</param>
        void UpdateSelectionPanel(List<ISelectable> selectedObjects);
        
        /// <summary>
        /// 清理选择面板
        /// </summary>
        void ClearSelectionPanel();
        
        /// <summary>
        /// 获取工具选项UI
        /// </summary>
        /// <param name="container">UI容器</param>
        void CreateToolOptions(VisualElement container);
        
        /// <summary>
        /// 设置UI管理器引用
        /// </summary>
        void SetUIManager(IUIManager uiManager);
        
    }
} 