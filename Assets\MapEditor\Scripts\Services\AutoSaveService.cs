using UnityEngine;
using MapEditor.Core;
using MapEditor.Event;

namespace MapEditor.Services
{
    /// <summary>
    /// 自动保存服务：每隔一定时间调用 MapDataStore.SaveAutoBackup()。
    /// </summary>
    public class AutoSaveService : ServiceBase,IUpdate
    {
        [SerializeField] private float intervalSeconds = 180f; // 3 分钟

        private float time = 0;

 

        public override void Initialize()
        {

            Debug.Log($"[AutoSave] Initialized. Interval: {intervalSeconds}s");
        }

        public override void Start()
        {

        }

        public void Update()
        {
            // time += Time.deltaTime;
            // if (time >= intervalSeconds)
            // {
            //     time = 0;
            //     Debug.Log("[AutoSave] Triggering auto backup");
            //     PublishEvent(new SaveRequestEvent(true));
            // }
        }

    }
} 