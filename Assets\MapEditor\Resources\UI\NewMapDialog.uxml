<UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
      xmlns="UnityEngine.UIElements" 
      xsi:schemaLocation="UnityEngine.UIElements ../UIElementsSchema/UnityEngine.UIElements.xsd">
    
    <Style src="MapEditorStyles.uss" />
    
    <VisualElement name="NewMapDialog" class="message-box" >
        <Label name="Title" text="新建地图" class="message-title" />
        
        <VisualElement class="message-content">
            <!-- 地图名称输入 -->
            <VisualElement class="input-group">
                <Label text="地图名称:" class="input-label" />
                <TextField name="MapNameField" value="新地图" class="input-field" />
            </VisualElement>
            
            <!-- 地图尺寸选择 -->
            <VisualElement class="input-group">
                <Label text="地图尺寸:" class="input-label" />
                <DropdownField name="MapSizeDropdown" class="input-field" />
            </VisualElement>
            
            <!-- 自定义尺寸输入 -->
            <VisualElement name="CustomSizeContainer" class="input-group" style="display: none;">
                <Label text="自定义尺寸:" class="input-label" />
                <VisualElement class="size-input-container" style="flex-direction: row;">
                    <IntegerField name="WidthField" value="1024" class="size-input" />
                    <Label text="×" style="align-self: center; margin: 0 5px;" />
                    <IntegerField name="HeightField" value="1024" class="size-input" />
                </VisualElement>
            </VisualElement>
            
            <!-- Chunk 大小输入 -->
            <VisualElement class="input-group">
                <Label text="Chunk 大小:" class="input-label" />
                <IntegerField name="ChunkSizeField" value="256" class="input-field" />
            </VisualElement>
            
            <!-- 基础纹理选择 -->
            <VisualElement class="input-group">
                <Label text="基础纹理:" class="input-label" />
                <ScrollView name="TexturePreviewContainer" class="texture-preview-container" 
                           style="height: 120px; border-width: 1px; border-color: #555; background-color: #2a2a2a;">
                    <VisualElement name="TextureGrid" class="texture-grid" 
                                  style="flex-direction: row; flex-wrap: wrap; padding: 5px;" />
                </ScrollView>
            </VisualElement>
        </VisualElement>
        
        <!-- 按钮区域 -->
        <VisualElement class="message-buttons">
            <Button name="CreateButton" text="创建" class="message-button primary-button" />
            <Button name="CancelButton" text="取消" class="message-button secondary-button" />
        </VisualElement>
    </VisualElement>
</UXML>