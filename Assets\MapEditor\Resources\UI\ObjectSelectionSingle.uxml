<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns="UnityEngine.UIElements">
    <ui:VisualElement name="SinglePropertiesContainer" class="object-properties-single" style="flex-direction: column;">
        <ui:TextField name="NameField" label="名称" style="width: 100%;" />
        <ui:VisualElement class="property-group" style="flex-direction: row;">
            <ui:Label text="位置" style="width: 60px;" />
            <ui:FloatField name="PositionX" style="flex-grow: 1;" />
            <ui:FloatField name="PositionY" style="flex-grow: 1;" />
        </ui:VisualElement>
        <ui:VisualElement class="property-group" style="flex-direction: row;">
            <ui:Label text="旋转" style="width: 60px;" />
            <ui:FloatField name="RotationField" style="flex-grow: 1;" />
        </ui:VisualElement>
        <ui:VisualElement class="property-group" style="flex-direction: row;">
            <ui:Label text="缩放" style="width: 60px;" />
            <ui:FloatField name="ScaleX" style="flex-grow: 1;" />
            <ui:FloatField name="ScaleY" style="flex-grow: 1;" />
        </ui:VisualElement>
        <ui:VisualElement class="button-group" style="flex-direction: row; margin-top: 10px;">
            <ui:Button name="ApplyButton" text="应用" style="flex-grow: 1;" />
            <ui:Button name="DeleteButton" text="删除" class="danger-button" style="flex-grow: 1;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>