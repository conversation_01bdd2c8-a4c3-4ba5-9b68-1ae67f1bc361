using System.Collections.Generic;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Data.Chunks;

namespace MapEditor.Tools.Selection
{
    /// <summary>
    /// 地表(TilemapLayer) 的选择行为：以 Chunk 为粒度。
    /// </summary>
    public class TilemapSelectionBehavior : ISelectionBehavior
    {
        private readonly IMapEditorCore editorCore;

        public TilemapSelectionBehavior(IMapEditorCore core)
        {
            editorCore = core;
        }

        public LayerType SupportedLayerType => LayerType.Ground;

        public bool SupportsDragging => false;
        public bool SupportsScaling => false;
        public bool SupportsRotation => false;
        public bool SupportsDeletion => false;

        // === 选择逻辑 ===
        public List<ISelectable> HandlePointSelection(Vector2 worldPosition, bool isMultiSelect, IMapLayer layer)
        {
            var list = new List<ISelectable>();
            if (layer is not MapLayer mapLayer) return list;

            var chunk = GetChunkAtWorldPosition(mapLayer, worldPosition);
            if (chunk != null)
            {
                list.Add(new SelectableTilemapChunk(chunk, mapLayer.LayerId));
            }
            return list;
        }

        public List<ISelectable> HandleRectSelection(Rect selectionRect, bool isMultiSelect, IMapLayer layer)
        {
            var list = new List<ISelectable>();
            if (layer is not MapLayer mapLayer) return list;
            int chunkSize = mapLayer.ChunkSize;
            float unitsPerPixel = 1f / MapEditorConfig.PixelsPerUnit;

            // Convert rect min/max to chunk coords
            int minChunkX = Mathf.FloorToInt(selectionRect.xMin / (chunkSize * unitsPerPixel));
            int maxChunkX = Mathf.FloorToInt(selectionRect.xMax / (chunkSize * unitsPerPixel));
            int minChunkY = Mathf.FloorToInt(selectionRect.yMin / (chunkSize * unitsPerPixel));
            int maxChunkY = Mathf.FloorToInt(selectionRect.yMax / (chunkSize * unitsPerPixel));

            for (int cx = minChunkX; cx <= maxChunkX; cx++)
            {
                for (int cy = minChunkY; cy <= maxChunkY; cy++)
                {
                    var coord = new ChunkCoord(cx, cy);
                    var chunk = mapLayer.GetOrCreateChunk<TilemapChunk>(coord);
                    if (chunk != null)
                    {
                        list.Add(new SelectableTilemapChunk(chunk, mapLayer.LayerId));
                    }
                }
            }
            return list;
        }

        // 以下操作不支持，留空实现
        public void StartDrag(List<ISelectable> selectedObjects, Vector2 startPosition) { }
        public void UpdateDrag(Vector2 currentPosition) { }
        public void EndDrag() { }

        public void StartScale(List<ISelectable> selectedObjects, int handleIndex, Vector2 startPosition) { }
        public void UpdateScale(Vector2 currentPosition) { }
        public void EndScale() { }

        public void StartRotate(List<ISelectable> selectedObjects, Vector2 startPosition) { }
        public void UpdateRotate(Vector2 currentPosition) { }
        public void EndRotate() { }

        public void DeleteSelected(List<ISelectable> selectedObjects) { }

        // === helpers ===
        private TilemapChunk GetChunkAtWorldPosition(MapLayer mapLayer, Vector2 worldPos)
        {
            int pixelX = Mathf.FloorToInt(worldPos.x * MapEditorConfig.PixelsPerUnit);
            int pixelY = Mathf.FloorToInt(worldPos.y * MapEditorConfig.PixelsPerUnit);
            int cx = Mathf.FloorToInt((float)pixelX / mapLayer.ChunkSize);
            int cy = Mathf.FloorToInt((float)pixelY / mapLayer.ChunkSize);
            return mapLayer.GetOrCreateChunk<TilemapChunk>(new ChunkCoord(cx, cy));
        }
    }

    /// <summary>
    /// 可选择 TilemapChunk 封装。
    /// </summary>
    internal class SelectableTilemapChunk : ISelectable
    {
        private readonly TilemapChunk chunk;
        private bool isSelected;
        private readonly string layerId;

        public SelectableTilemapChunk(TilemapChunk chunk, string layerId)
        {
            this.chunk = chunk;
            this.layerId = layerId;
        }

        public string Id => chunk.TilemapId;
        public string DisplayName => $"Chunk ({chunk.Coord.X},{chunk.Coord.Y})";
        public Vector2 Position { get; set; } // not used
        public float Rotation { get; set; }
        public Vector2 Scale { get; set; }
        public bool IsVisible => true;
        public bool IsLocked => false;
        public string LayerId => layerId;
        public string ObjectType => "Chunk";
        public bool IsSelected => isSelected;

        public TilemapChunk Chunk => chunk;

        public void SetSelected(bool selected) => isSelected = selected;

        public Rect GetBounds()
        {
            int cs = chunk.ChunkSize;
            float unitsPerPixel = 1f / MapEditorConfig.PixelsPerUnit;
            Vector2 origin = new Vector2(chunk.Coord.X * cs * unitsPerPixel, chunk.Coord.Y * cs * unitsPerPixel);
            return new Rect(origin, new Vector2(cs * unitsPerPixel, cs * unitsPerPixel));
        }
    }
} 