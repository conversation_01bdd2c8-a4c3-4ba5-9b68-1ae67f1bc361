Shader "MapEditor/SplatMixArrayLit"
{
    Properties
    {
        [HideInInspector] _MainTex ("Sprite Texture (unused)", 2D) = "white" {}
        _SurfaceArray ("Surface Texture Array", 2DArray) = "white" {}
        _Splat0 ("Splat0", 2D) = "black" {}
        _Splat1 ("Splat1", 2D) = "black" {}
        [HideInInspector] _IndexTable0 ("IndexTable0", Vector) = (0,0,0,0)
        [HideInInspector] _IndexTable1 ("IndexTable1", Vector) = (0,0,0,0)
    }
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" "RenderPipeline"="UniversalPipeline" }
        Cull Off ZWrite Off ZTest LEqual
        Blend One OneMinusSrcAlpha
        Pass
        {
            Name "SplatMixArrayLit"

            HLSLPROGRAM
            #pragma vertex Vert
            #pragma fragment Frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            // =====================
            // Texture declarations
            // =====================
            TEXTURE2D_ARRAY(_SurfaceArray);
            SAMPLER(sampler_SurfaceArray);
            TEXTURE2D(_Splat0); SAMPLER(sampler_Splat0);
            TEXTURE2D(_Splat1); SAMPLER(sampler_Splat1);

            CBUFFER_START(UnityPerMaterial)
                int4 _IndexTable0; // 通道 0-3
                int4 _IndexTable1; // 通道 4-6 + dummy
                float4 _UVScales[7];
            CBUFFER_END

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv         : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv         : TEXCOORD0;
            };

            Varyings Vert (Attributes IN)
            {
                Varyings OUT;
                OUT.positionCS = TransformObjectToHClip(IN.positionOS.xyz);
                OUT.uv = IN.uv;
                return OUT;
            }

            half4 Frag (Varyings IN) : SV_Target
            {
                float2 uv = IN.uv;

                float4 w0 = SAMPLE_TEXTURE2D(_Splat0, sampler_Splat0, uv);
                float4 w1 = SAMPLE_TEXTURE2D(_Splat1, sampler_Splat1, uv);

                float coverage = saturate(w1.a);
                float total = w0.r + w0.g + w0.b + w0.a + w1.r + w1.g + w1.b;
                float invTotal = total > 1e-5 ? 1.0 / total : 0.0;

                w0 *= invTotal;
                w1.rgb *= invTotal;

                // 读取索引
                int indices[7];
                indices[0] = (int)_IndexTable0.x;
                indices[1] = (int)_IndexTable0.y;
                indices[2] = (int)_IndexTable0.z;
                indices[3] = (int)_IndexTable0.w;
                indices[4] = (int)_IndexTable1.x;
                indices[5] = (int)_IndexTable1.y;
                indices[6] = (int)_IndexTable1.z;

                float3 col = 0;
                if(indices[0] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[0].xy, indices[0]).rgb * w0.r;
                if(indices[1] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[1].xy, indices[1]).rgb * w0.g;
                if(indices[2] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[2].xy, indices[2]).rgb * w0.b;
                if(indices[3] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[3].xy, indices[3]).rgb * w0.a;
                if(indices[4] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[4].xy, indices[4]).rgb * w1.r;
                if(indices[5] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[5].xy, indices[5]).rgb * w1.g;
                if(indices[6] >= 0) col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv * _UVScales[6].xy, indices[6]).rgb * w1.b;

                return half4(col, coverage);
            }
            ENDHLSL
        }
    }

    Fallback Off
} 