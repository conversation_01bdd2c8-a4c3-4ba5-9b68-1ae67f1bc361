using System;
using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 所有地图图层的抽象基类，负责管理 Chunk 字典与基本元数据。
    /// </summary>
    [Serializable]
    public abstract class MapLayer : IMapLayer, UnityEngine.ISerializationCallbackReceiver
    {
        [SerializeField] private string layerId;
        [SerializeField] private string layerName;
        [SerializeField] private bool isVisible = true;
        [SerializeField] private bool isLocked;
        [SerializeField] private int order;
        [SerializeField] private Vector2Int layerSize;

        // 新增：Chunk 尺寸，默认 256，可在创建地图时由用户指定。
        [SerializeField] protected int chunkSize = 256;

        // Chunk 工厂委托字典，避免反射
        private static readonly Dictionary<Type, Func<ChunkCoord, int, ChunkBase>> _chunkFactories = new();

        // 使用 SerializeReference 以支持不同具体 Chunk 类型
        [SerializeReference] protected Dictionary<ChunkCoord, ChunkBase> chunks = new();

        // 为了解决 JsonUtility 无法序列化 Dictionary 的限制，使用列表做桥接
        [SerializeReference] [SerializeField] private List<ChunkBase> serializedChunks = new();

        /// <summary>
        /// 注册 Chunk 类型的工厂方法
        /// </summary>
        /// <typeparam name="TChunk">Chunk 类型</typeparam>
        /// <param name="factory">创建 Chunk 的工厂方法</param>
        public static void RegisterChunkFactory<TChunk>(Func<ChunkCoord, int, TChunk> factory) where TChunk : ChunkBase
        {
            _chunkFactories[typeof(TChunk)] = (coord, size) => factory(coord, size);
        }

        #region IMapLayer
        public string LayerId { get => layerId; set => layerId = value; }
        public string LayerName { get => layerName; set => layerName = value; }
        public bool IsVisible { get => isVisible; set => isVisible = value; }
        public bool IsLocked { get => isLocked; set => isLocked = value; }
        public int Order { get => order; set => order = value; }
        public abstract LayerType Type { get; }
        /// <summary>
        /// Chunk 边长（可由地图创建流程在运行时修改）。
        /// </summary>
        public virtual int ChunkSize => chunkSize;
        public Vector2Int LayerSize { get => layerSize; set => layerSize = value; }

        /// <summary>
        /// 真实图层尺寸：按 ChunkSize 向上取整后得到的宽高，确保能被 ChunkSize 整除。
        /// 例如 LayerSize 720 且 ChunkSize 520 时，真实高度为 1040。
        /// </summary>
        public Vector2Int RealLayerSize
        {
            get
            {
                int realWidth = Mathf.CeilToInt(layerSize.x / (float)ChunkSize) * ChunkSize;
                int realHeight = Mathf.CeilToInt(layerSize.y / (float)ChunkSize) * ChunkSize;
                return new Vector2Int(realWidth, realHeight);
            }
        }
        
        /// <summary>
        /// 设置 ChunkSize，供地图创建或加载后统一调整。
        /// </summary>
        /// <param name="size">新的 Chunk 边长(像素)。</param>
        public void SetChunkSize(int size)
        {
            chunkSize = Mathf.Max(1, size);
        }
        #endregion

        /// <summary>
        /// 获取可见 Chunk。
        /// 默认实现采用 AABB 裁剪，派生类可覆盖。
        /// </summary>
        public virtual IEnumerable<ChunkBase> GetVisibleChunks(Bounds viewBounds)
        {
            List<ChunkBase> result = new();

            // 将视口包围盒转换为 ChunkCoord 范围
            int minX = Mathf.FloorToInt(viewBounds.min.x / ChunkSize);
            int minY = Mathf.FloorToInt(viewBounds.min.y / ChunkSize);
            int maxX = Mathf.FloorToInt(viewBounds.max.x / ChunkSize);
            int maxY = Mathf.FloorToInt(viewBounds.max.y / ChunkSize);

            for (int x = minX; x <= maxX; x++)
            {
                for (int y = minY; y <= maxY; y++)
                {
                    var coord = new ChunkCoord(x, y);
                    if (chunks.TryGetValue(coord, out var chunk))
                    {
                        result.Add(chunk);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取或创建指定坐标的 Chunk。
        /// </summary>
        public TChunk GetOrCreateChunk<TChunk>(ChunkCoord coord) where TChunk : ChunkBase
        {
            if (chunks.TryGetValue(coord, out var existing))
            {
                return (TChunk)existing;
            }

            // 使用工厂创建，避免反射
            if (!_chunkFactories.TryGetValue(typeof(TChunk), out var factory))
            {
                Debug.LogError($"未注册 {typeof(TChunk)} 的工厂方法。请在静态构造函数中调用 MapLayer.RegisterChunkFactory");
                return null;
            }

            var newChunk = (TChunk)factory(coord, ChunkSize);
            chunks[coord] = newChunk;
            return newChunk;
        }

        internal void MarkChunkDirty(ChunkCoord coord)
        {
            if (chunks.TryGetValue(coord, out var chunk))
            {
                chunk.MarkDirty();
            }
        }

        /// <summary>
        /// 获取该图层当前持有的所有 Chunk 数据。
        /// </summary>
        public IEnumerable<ChunkBase> GetAllChunks()
        {
            return chunks.Values;
        }

        #region ISerializationCallbackReceiver 实现

        // 在序列化之前，把字典内容同步到列表
        public void OnBeforeSerialize()
        {
            serializedChunks.Clear();
            foreach (var kv in chunks)
            {
                serializedChunks.Add(kv.Value);
            }
        }

        // 反序列化后，把列表内容重新填充到字典
        public virtual void OnAfterDeserialize()
        {
            chunks.Clear();
            foreach (var chunk in serializedChunks)
            {
                if (chunk == null) continue;
                chunks[chunk.Coord] = chunk;
            }
        }

        #endregion
    }
} 