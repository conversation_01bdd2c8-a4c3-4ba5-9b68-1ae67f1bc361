using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Event;
using MapEditor.UI;

namespace MapEditor.UI
{
    /// <summary>
    /// 添加图层对话框
    /// </summary>
    public class AddLayerDialog : UIPanel
    {
        public override string PanelId => "addLayerDialog";
        public override string DisplayName => "添加图层";
        
        // UI控件引用
        private TextField layerNameField;
        private DropdownField layerTypeDropdown;
        private Toggle visibleToggle;
        private Toggle lockedToggle;
        private Button confirmButton;
        private Button cancelButton;
        
        // 图层类型选项
        private readonly List<string> layerTypeOptions = new ()
        {
            "地表层",
            "对象层",
            "装饰层",
            "逻辑层"
        };
        
        private readonly Dictionary<string, LayerType> layerTypeMap = new ()
        {
            { "地表层", LayerType.Ground },
            { "对象层", LayerType.Object },
            { "装饰层", LayerType.Decoration },
            { "逻辑层", LayerType.Logic }
        };
        
        // 事件
        public event Action<AddLayerDialogData> OnLayerCreated;
        public event Action OnCancelled;
        
        public AddLayerDialog(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
            // 默认隐藏对话框
            IsVisible = false;
        }
        
        public override void Initialize()
        {
            // 查找UI控件
            layerNameField = FindElement<TextField>("LayerNameField");
            layerTypeDropdown = FindElement<DropdownField>("LayerTypeDropdown");
            visibleToggle = FindElement<Toggle>("VisibleToggle");
            lockedToggle = FindElement<Toggle>("LockedToggle");
            confirmButton = FindElement<Button>("ConfirmButton");
            cancelButton = FindElement<Button>("CancelButton");
            
            if (layerNameField == null || layerTypeDropdown == null || 
                confirmButton == null || cancelButton == null)
            {
                Debug.LogError("AddLayerDialog: 缺少必要的UI控件");
                return;
            }
            
            // 设置图层类型下拉选项
            layerTypeDropdown.choices = layerTypeOptions;
            layerTypeDropdown.value = layerTypeOptions[1]; // 默认选择"对象层"
            
            // 注册事件
            confirmButton.clicked += OnConfirmButtonClicked;
            cancelButton.clicked += OnCancelButtonClicked;
            
            // 订阅图层创建失败事件
            MapEditorCore.Instance?.EventSystem?.Subscribe<LayerCreateFailedEvent>(OnLayerCreateFailed);
            
            // 设置默认值
            ResetToDefaults();
            
            Debug.Log("AddLayerDialog 初始化完成");
        }
        
        /// <summary>
        /// 显示对话框
        /// </summary>
        public void ShowDialog()
        {
            ResetToDefaults();
            IsVisible = true;
            
            // 聚焦到名称输入框
            layerNameField?.Focus();
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        private void ResetToDefaults()
        {
            if (layerNameField != null)
                layerNameField.value = "新图层";
                
            if (layerTypeDropdown != null)
                layerTypeDropdown.value = layerTypeOptions[1]; // 对象层
                
            if (visibleToggle != null)
                visibleToggle.value = true;
                
            if (lockedToggle != null)
                lockedToggle.value = false;
        }
        
        private void OnConfirmButtonClicked()
        {
            // 验证输入
            string layerName = layerNameField.value?.Trim();
            if (string.IsNullOrEmpty(layerName))
            {
                RequestShowMessage("错误", "图层名称不能为空", MessageType.Error);
                return;
            }
            
            // 创建图层数据
            var layerData = new AddLayerDialogData
            {
                layerName = layerName,
                layerType = layerTypeMap[layerTypeDropdown.value],
                isVisible = visibleToggle?.value ?? true,
                isLocked = lockedToggle?.value ?? false
            };
            
            // 触发创建事件
            OnLayerCreated?.Invoke(layerData);
            
            // 注意：不在这里隐藏对话框，等待创建结果
            Debug.Log($"请求创建图层: {layerData.layerName} ({layerData.layerType})");
        }
        
        private void OnCancelButtonClicked()
        {
            // 触发取消事件
            OnCancelled?.Invoke();
            
            // 隐藏对话框
            IsVisible = false;
            
            Debug.Log("取消添加图层");
        }

        private void OnLayerCreateFailed(LayerCreateFailedEvent evt)
        {
            RequestShowMessage("错误", evt.Reason, MessageType.Error);
        }
    }
}