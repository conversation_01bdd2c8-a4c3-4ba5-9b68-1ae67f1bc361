using System;
using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 地图数据接口，定义地图数据结构
    /// </summary>
    public interface IMapData
    {
        /// <summary>
        /// 地图唯一标识符
        /// </summary>
        string MapId { get; }
        
        /// <summary>
        /// 地图名称
        /// </summary>
        string MapName { get; set; }
        
        /// <summary>
        /// 地图大小
        /// </summary>
        Vector2Int MapSize { get; }
        
        /// <summary>
        /// 地图创建时间
        /// </summary>
        DateTime CreationTime { get; }
        
        /// <summary>
        /// 地图最后修改时间
        /// </summary>
        DateTime LastModifiedTime { get; set; }
        
        /// <summary>
        /// 获取所有图层
        /// </summary>
        IEnumerable<IMapLayer> GetAllLayers();
        
        /// <summary>
        /// 获取指定ID的图层
        /// </summary>
        IMapLayer GetLayer(string layerId);
        
        /// <summary>
        /// 添加图层
        /// </summary>
        void AddLayer(IMapLayer layer);
        
        /// <summary>
        /// 移除图层
        /// </summary>
        void RemoveLayer(string layerId);
    }
} 