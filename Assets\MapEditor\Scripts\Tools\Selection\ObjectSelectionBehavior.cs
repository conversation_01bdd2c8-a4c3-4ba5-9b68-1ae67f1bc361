using System.Collections.Generic;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using MapEditor.Rendering.Layers;
using MapEditor.Services;

namespace MapEditor.Tools.Selection
{
    /// <summary>
    /// 对象层的选择行为实现
    /// </summary>
    public class ObjectSelectionBehavior : ISelectionBehavior
    {
        private readonly IMapEditorCore editorCore;
        private readonly Dictionary<string, Vector2> dragStartPositions = new();
        private readonly Dictionary<string, Vector2> originalPositions = new();
        private readonly Dictionary<string, Vector2> originalScales = new();
        private readonly Dictionary<string, float> originalRotations = new();
        
        private Vector2 dragStartPosition;
        private Rect originalBounds;
        private int scalingHandleIndex;
        private Vector2 rotationCenter;
        private float rotationStartAngle;
        
        public LayerType SupportedLayerType => LayerType.Object;
        public bool SupportsDragging => true;
        public bool SupportsScaling => true;
        public bool SupportsRotation => true;
        public bool SupportsDeletion => true;
        
        public ObjectSelectionBehavior(IMapEditorCore editorCore)
        {
            this.editorCore = editorCore;
        }
        
        public List<ISelectable> HandlePointSelection(Vector2 worldPosition, bool isMultiSelect, IMapLayer layer)
        {
            var result = new List<ISelectable>();
            
            Debug.Log($"ObjectSelectionBehavior.HandlePointSelection: 世界坐标 {worldPosition}");
            
            var objectRenderer = GetObjectRenderer(layer);
            if (objectRenderer == null)
            {
                Debug.LogWarning("ObjectSelectionBehavior: 无法获取对象渲染器");
                return result;
            }
            
            // 使用射线检测选择对象
            var hitObject = objectRenderer.SelectObjectAtPosition(worldPosition);
            if (hitObject != null)
            {
                Debug.Log($"选中对象: {hitObject.GetDisplayName()} at {hitObject.Position}");
                var selectable = new SelectableObjectInstance(hitObject, layer.LayerId, editorCore);
                result.Add(selectable);
            }
            else
            {
                Debug.Log("未选中任何对象");
            }
            
            return result;
        }
        
        public List<ISelectable> HandleRectSelection(Rect selectionRect, bool isMultiSelect, IMapLayer layer)
        {
            var result = new List<ISelectable>();
            
            var objectRenderer = GetObjectRenderer(layer);
            if (objectRenderer == null) return result;
            
            // 获取所有对象
            var allObjects = objectRenderer.GetAllObjects();
            
            // 检查哪些对象在选择框内
            foreach (var obj in allObjects)
            {
                var selectable = new SelectableObjectInstance(obj, layer.LayerId, editorCore);
                var objBounds = selectable.GetBounds();
                
                // 检查对象的包围框是否与选择框相交
                if (selectionRect.Overlaps(objBounds))
                {
                    result.Add(selectable);
                }
            }
            
            return result;
        }
        
        public void StartDrag(List<ISelectable> selectedObjects, Vector2 startPosition)
        {
            dragStartPosition = startPosition;
            dragStartPositions.Clear();
            
            foreach (var obj in selectedObjects)
            {
                dragStartPositions[obj.Id] = obj.Position;
            }
        }
        
        public void UpdateDrag(Vector2 currentPosition)
        {
            Vector2 delta = currentPosition - dragStartPosition;
            
            foreach (var kvp in dragStartPositions)
            {
                var obj = GetObjectById(kvp.Key);
                if (obj != null)
                {
                    obj.Position = kvp.Value + delta;
                    UpdateObjectInRenderer(obj);
                }
            }
        }
        
        public void EndDrag()
        {
            // 更新所有被拖拽对象的占用格子
            foreach (var kvp in dragStartPositions)
            {
                var obj = GetObjectById(kvp.Key);
                if (obj is SelectableObjectInstance selectableObj)
                {
                    var objectRenderer = GetActiveObjectRenderer();
                    objectRenderer?.UpdateObjectInstance(selectableObj.ObjectInstance);
                }
            }
            
            dragStartPositions.Clear();
        }
        
        public void StartScale(List<ISelectable> selectedObjects, int handleIndex, Vector2 startPosition)
        {
            scalingHandleIndex = handleIndex;
            originalPositions.Clear();
            originalScales.Clear();
            
            // 计算原始包围框
            originalBounds = CalculateBounds(selectedObjects);
            
            foreach (var obj in selectedObjects)
            {
                originalPositions[obj.Id] = obj.Position;
                originalScales[obj.Id] = obj.Scale;
            }
        }
        
        public void UpdateScale(Vector2 currentPosition)
        {
            // 根据手柄索引计算缩放
            Vector2 scaleFactors = CalculateScaleFactors(scalingHandleIndex, currentPosition, originalBounds);
            
            // 如果按住 Shift 键则进行等比缩放，使用较大的缩放系数
            if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift))
            {
                float uniform = Mathf.Max(Mathf.Abs(scaleFactors.x), Mathf.Abs(scaleFactors.y));
                uniform = Mathf.Max(0.1f, uniform);
                scaleFactors = new Vector2(uniform, uniform);
            }
            
            foreach (var kvp in originalScales)
            {
                var obj = GetObjectById(kvp.Key);
                if (obj != null)
                {
                    // 更新缩放
                    obj.Scale = kvp.Value * scaleFactors;
                    
                    // 更新位置以保持相对位置
                    if (originalPositions.TryGetValue(kvp.Key, out Vector2 originalPos))
                    {
                        Vector2 relativePos = originalPos - originalBounds.center;
                        obj.Position = originalBounds.center + relativePos * scaleFactors;
                    }
                    
                    UpdateObjectInRenderer(obj);
                }
            }
        }
        
        public void EndScale()
        {
            // 更新所有被缩放对象的占用格子
            foreach (var kvp in originalScales)
            {
                var obj = GetObjectById(kvp.Key);
                if (obj is SelectableObjectInstance selectableObj)
                {
                    var objectRenderer = GetActiveObjectRenderer();
                    objectRenderer?.UpdateObjectInstance(selectableObj.ObjectInstance);
                }
            }
            
            originalPositions.Clear();
            originalScales.Clear();
        }
        
        public void StartRotate(List<ISelectable> selectedObjects, Vector2 startPosition)
        {
            originalRotations.Clear();
            originalPositions.Clear();
            
            // 计算旋转中心
            rotationCenter = CalculateBounds(selectedObjects).center;
            
            // 计算起始角度
            Vector2 startVector = startPosition - rotationCenter;
            rotationStartAngle = Mathf.Atan2(startVector.y, startVector.x) * Mathf.Rad2Deg;
            
            foreach (var obj in selectedObjects)
            {
                originalRotations[obj.Id] = obj.Rotation;
                originalPositions[obj.Id] = obj.Position;
            }
        }
        
        public void UpdateRotate(Vector2 currentPosition)
        {
            // 计算当前角度
            Vector2 currentVector = currentPosition - rotationCenter;
            float currentAngle = Mathf.Atan2(currentVector.y, currentVector.x) * Mathf.Rad2Deg;
            
            // 计算旋转增量
            float deltaAngle = currentAngle - rotationStartAngle;
            
            foreach (var kvp in originalRotations)
            {
                var obj = GetObjectById(kvp.Key);
                if (obj != null)
                {
                    // 更新旋转
                    obj.Rotation = kvp.Value + deltaAngle;
                    
                    // 更新位置
                    if (originalPositions.TryGetValue(kvp.Key, out Vector2 originalPos))
                    {
                        Vector2 relativePos = originalPos - rotationCenter;
                        float angle = deltaAngle * Mathf.Deg2Rad;
                        float cos = Mathf.Cos(angle);
                        float sin = Mathf.Sin(angle);
                        
                        Vector2 rotatedPos = new Vector2(
                            relativePos.x * cos - relativePos.y * sin,
                            relativePos.x * sin + relativePos.y * cos
                        );
                        
                        obj.Position = rotationCenter + rotatedPos;
                    }
                    
                    UpdateObjectInRenderer(obj);
                }
            }
        }
        
        public void EndRotate()
        {
            // 更新所有被旋转对象的占用格子
            foreach (var kvp in originalRotations)
            {
                var obj = GetObjectById(kvp.Key);
                if (obj is SelectableObjectInstance selectableObj)
                {
                    var objectRenderer = GetActiveObjectRenderer();
                    objectRenderer?.UpdateObjectInstance(selectableObj.ObjectInstance);
                }
            }
            
            originalRotations.Clear();
            originalPositions.Clear();
        }
        
        public void DeleteSelected(List<ISelectable> selectedObjects)
        {
            var objectRenderer = GetActiveObjectRenderer();
            if (objectRenderer == null) return;
            
            foreach (var obj in selectedObjects)
            {
                if (obj is SelectableObjectInstance selectableObj)
                {
                    objectRenderer.RemoveObject(selectableObj.ObjectInstance);
                }
            }
        }
        
        private ObjectLayerRenderer GetObjectRenderer(IMapLayer layer)
        {
            var layerRenderService = editorCore?.GetService<LayerRenderService>();
            var renderer = layerRenderService?.GetRenderer(layer);
            return renderer as ObjectLayerRenderer;
        }
        
        private ObjectLayerRenderer GetActiveObjectRenderer()
        {
            var activeLayer = editorCore.LayerStore?.ActiveLayer;
            if (activeLayer == null) return null;
            
            return GetObjectRenderer(activeLayer);
        }
        
        private ISelectable GetObjectById(string id)
        {
            var objectRenderer = GetActiveObjectRenderer();
            if (objectRenderer == null) return null;
            
            var obj = objectRenderer.GetObjectById(id);
            if (obj != null)
            {
                return new SelectableObjectInstance(obj, editorCore.LayerStore.ActiveLayer.LayerId, editorCore);
            }
            
            return null;
        }
        
        private void UpdateObjectInRenderer(ISelectable selectable)
        {
            if (selectable is SelectableObjectInstance selectableObj)
            {
                var objectRenderer = GetActiveObjectRenderer();
                objectRenderer?.UpdateObjectInstance(selectableObj.ObjectInstance);
            }
        }
        
        private Rect CalculateBounds(List<ISelectable> objects)
        {
            if (objects.Count == 0) return new Rect();
            
            Rect bounds = objects[0].GetBounds();
            
            for (int i = 1; i < objects.Count; i++)
            {
                Rect objBounds = objects[i].GetBounds();
                bounds.xMin = Mathf.Min(bounds.xMin, objBounds.xMin);
                bounds.yMin = Mathf.Min(bounds.yMin, objBounds.yMin);
                bounds.xMax = Mathf.Max(bounds.xMax, objBounds.xMax);
                bounds.yMax = Mathf.Max(bounds.yMax, objBounds.yMax);
            }
            
            return bounds;
        }
        
        private Vector2 CalculateScaleFactors(int handleIndex, Vector2 currentPosition, Rect originalBounds)
        {
            Vector2 center = originalBounds.center;
            Vector2 scaleFactors = Vector2.one;
            
            switch (handleIndex)
            {
                case 0: // 左下
                    scaleFactors.x = (center.x - currentPosition.x) / (center.x - originalBounds.xMin);
                    scaleFactors.y = (center.y - currentPosition.y) / (center.y - originalBounds.yMin);
                    break;
                case 1: // 右下
                    scaleFactors.x = (currentPosition.x - center.x) / (originalBounds.xMax - center.x);
                    scaleFactors.y = (center.y - currentPosition.y) / (center.y - originalBounds.yMin);
                    break;
                case 2: // 右上
                    scaleFactors.x = (currentPosition.x - center.x) / (originalBounds.xMax - center.x);
                    scaleFactors.y = (currentPosition.y - center.y) / (originalBounds.yMax - center.y);
                    break;
                case 3: // 左上
                    scaleFactors.x = (center.x - currentPosition.x) / (center.x - originalBounds.xMin);
                    scaleFactors.y = (currentPosition.y - center.y) / (originalBounds.yMax - center.y);
                    break;
            }
            
            // 限制最小缩放
            scaleFactors.x = Mathf.Max(0.1f, scaleFactors.x);
            scaleFactors.y = Mathf.Max(0.1f, scaleFactors.y);
            
            return scaleFactors;
        }
    }
    
    /// <summary>
    /// 可选择的对象实例包装器
    /// </summary>
    public class SelectableObjectInstance : ISelectable
    {
        private readonly ObjectInstance objectInstance;
        private readonly IMapEditorCore editorCore;
        private bool isSelected;
        
        public SelectableObjectInstance(ObjectInstance objectInstance, string layerId, IMapEditorCore editorCore = null)
        {
            this.objectInstance = objectInstance;
            this.LayerId = layerId;
            this.editorCore = editorCore ?? MapEditorCore.Instance;
        }
        
        public ObjectInstance ObjectInstance => objectInstance;
        
        public string Id => objectInstance.InstanceId;
        public string DisplayName => objectInstance.GetDisplayName();
        public string LayerId { get; }
        public string ObjectType => "Object";
        
        public Vector2 Position
        {
            get => objectInstance.Position;
            set => objectInstance.Position = value;
        }
        
        public float Rotation
        {
            get => objectInstance.Rotation;
            set => objectInstance.Rotation = value;
        }
        
        public Vector2 Scale
        {
            get => objectInstance.Scale;
            set => objectInstance.Scale = value;
        }
        
        public bool IsVisible => true;
        public bool IsLocked => false;
        public bool IsSelected => isSelected;
        
        public Rect GetBounds()
        {
            // 获取Prefab的实际尺寸
            Vector2 actualSize = GetActualPrefabSize();
            
            // 应用缩放
            float width = actualSize.x * Scale.x;
            float height = actualSize.y * Scale.y;
            
            return new Rect(
                Position.x - width * 0.5f,
                Position.y - height * 0.5f,
                width,
                height
            );
        }
        
        /// <summary>
        /// 获取Prefab的实际尺寸
        /// </summary>
        /// <returns>Prefab的实际尺寸</returns>
        private Vector2 GetActualPrefabSize()
        {
            // 尝试从PrefabSizeCache获取尺寸
            if (editorCore != null)
            {
                var prefabSizeCache = editorCore.GetService<PrefabSizeCache>();
                if (prefabSizeCache != null)
                {
                    return prefabSizeCache.GetPrefabSize(objectInstance.PrefabGuid);
                }
            }
            
            // 降级处理：使用默认尺寸
            Debug.LogWarning($"SelectableObjectInstance: 无法获取PrefabSizeCache服务，使用默认尺寸 1x1");
            return new Vector2(1f, 1f);
        }
        
        public void SetSelected(bool selected)
        {
            isSelected = selected;
        }
    }
} 