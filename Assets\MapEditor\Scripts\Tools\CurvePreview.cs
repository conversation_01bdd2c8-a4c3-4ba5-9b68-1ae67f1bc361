using UnityEngine;
using System.Collections.Generic;
using MapEditor.Core;
using MapEditor.Config;

namespace MapEditor.Tools
{
    /// <summary>
    /// 曲线预览渲染器，负责实时显示曲线和描边效果
    /// </summary>
    public class CurvePreview
    {
        private GameObject previewContainer;
        private LineRenderer curveLineRenderer;
        private LineRenderer strokeBorderRenderer;
        private Vector2 mousePosition;
        private bool isVisible = false;
        private ISceneRenderer sceneRenderer;
        
        // 预览参数
        private const float CURVE_LINE_WIDTH = 0.05f;
        private const float STROKE_BORDER_WIDTH = 0.02f;
        private static readonly Color CurveColor = new Color(1f, 1f, 0f, 0.8f); // 黄色
        private static readonly Color StrokeColor = new Color(0f, 1f, 1f, 0.5f); // 青色半透明
        
        /// <summary>
        /// 初始化预览系统
        /// </summary>
        /// <param name="renderer">场景渲染器</param>
        public void Initialize(ISceneRenderer renderer)
        {
            sceneRenderer = renderer;
            CreatePreviewObjects();
        }
        
        /// <summary>
        /// 创建预览对象
        /// </summary>
        private void CreatePreviewObjects()
        {
            // 创建容器
            previewContainer = new GameObject("CurvePreview");
            previewContainer.transform.position = Vector3.zero;
            
            // 创建曲线中心线渲染器
            CreateCurveLineRenderer();
            
            // 创建描边边界渲染器
            CreateStrokeBorderRenderer();
            
            // 初始状态为隐藏
            SetVisible(false);
        }
        
        /// <summary>
        /// 创建曲线中心线渲染器
        /// </summary>
        private void CreateCurveLineRenderer()
        {
            GameObject curveLineObj = new GameObject("CurveLine");
            curveLineObj.transform.SetParent(previewContainer.transform);
            
            curveLineRenderer = curveLineObj.AddComponent<LineRenderer>();
            
            // 设置材质和属性
            Material lineMaterial = CreateLineMaterial();
            curveLineRenderer.material = lineMaterial;
            curveLineRenderer.startColor = CurveColor;
            curveLineRenderer.endColor = CurveColor;
            curveLineRenderer.startWidth = CURVE_LINE_WIDTH;
            curveLineRenderer.endWidth = CURVE_LINE_WIDTH;
            curveLineRenderer.positionCount = 0;
            curveLineRenderer.useWorldSpace = true;
            curveLineRenderer.sortingOrder = SortingOrderConfig.PreviewRange.max;
            
            // 禁用阴影
            curveLineRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            curveLineRenderer.receiveShadows = false;
        }
        
        /// <summary>
        /// 创建描边边界渲染器
        /// </summary>
        private void CreateStrokeBorderRenderer()
        {
            GameObject strokeBorderObj = new GameObject("StrokeBorder");
            strokeBorderObj.transform.SetParent(previewContainer.transform);
            
            strokeBorderRenderer = strokeBorderObj.AddComponent<LineRenderer>();
            
            // 设置材质和属性
            Material borderMaterial = CreateLineMaterial();
            strokeBorderRenderer.material = borderMaterial;
            strokeBorderRenderer.startColor = StrokeColor;
            strokeBorderRenderer.endColor = StrokeColor;
            strokeBorderRenderer.startWidth = STROKE_BORDER_WIDTH;
            strokeBorderRenderer.endWidth = STROKE_BORDER_WIDTH;
            strokeBorderRenderer.positionCount = 0;
            strokeBorderRenderer.useWorldSpace = true;
            strokeBorderRenderer.sortingOrder = SortingOrderConfig.PreviewRange.max - 1;
            
            // 禁用阴影
            strokeBorderRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            strokeBorderRenderer.receiveShadows = false;
        }
        
        /// <summary>
        /// 创建线条材质
        /// </summary>
        private Material CreateLineMaterial()
        {
            // 使用Unity内置的Unlit/Color着色器
            Shader shader = Shader.Find("Unlit/Color");
            if (shader == null)
            {
                shader = Shader.Find("Sprites/Default");
            }
            
            return new Material(shader);
        }
        
        /// <summary>
        /// 更新曲线预览
        /// </summary>
        /// <param name="curveSegments">曲线段列表</param>
        /// <param name="settings">曲线设置</param>
        public void UpdateCurvePreview(IReadOnlyList<CurveSegment> curveSegments, CurveStrokeSettings settings)
        {
            if (curveSegments == null || curveSegments.Count == 0)
            {
                ClearPreview();
                return;
            }
            
            // 更新曲线中心线
            UpdateCurveLine(curveSegments);
            
            // 更新描边边界
            UpdateStrokeBorder(curveSegments, settings.width);
        }
        
        /// <summary>
        /// 更新曲线中心线
        /// </summary>
        private void UpdateCurveLine(IReadOnlyList<CurveSegment> curveSegments)
        {
            if (curveLineRenderer == null || curveSegments.Count == 0) return;
            
            Vector3[] positions = new Vector3[curveSegments.Count];
            for (int i = 0; i < curveSegments.Count; i++)
            {
                positions[i] = new Vector3(curveSegments[i].position.x, curveSegments[i].position.y, 0);
            }
            
            curveLineRenderer.positionCount = positions.Length;
            curveLineRenderer.SetPositions(positions);
        }
        
        /// <summary>
        /// 更新描边边界
        /// </summary>
        private void UpdateStrokeBorder(IReadOnlyList<CurveSegment> curveSegments, float strokeWidth)
        {
            if (strokeBorderRenderer == null || curveSegments.Count == 0) return;
            
            var borderPoints = GenerateStrokeBorder(curveSegments, strokeWidth * 0.5f);
            
            if (borderPoints.Count > 0)
            {
                Vector3[] positions = new Vector3[borderPoints.Count];
                for (int i = 0; i < borderPoints.Count; i++)
                {
                    positions[i] = new Vector3(borderPoints[i].x, borderPoints[i].y, 0);
                }
                
                strokeBorderRenderer.positionCount = positions.Length;
                strokeBorderRenderer.SetPositions(positions);
            }
            else
            {
                strokeBorderRenderer.positionCount = 0;
            }
        }
        
        /// <summary>
        /// 生成描边边界点
        /// </summary>
        private List<Vector2> GenerateStrokeBorder(IReadOnlyList<CurveSegment> curveSegments, float radius)
        {
            var borderPoints = new List<Vector2>();
            
            if (curveSegments.Count < 2) return borderPoints;
            
            // 生成左边界
            for (int i = 0; i < curveSegments.Count; i++)
            {
                Vector2 offset = curveSegments[i].normal * radius;
                borderPoints.Add(curveSegments[i].position + offset);
            }
            
            // 生成右边界（逆序）
            for (int i = curveSegments.Count - 1; i >= 0; i--)
            {
                Vector2 offset = curveSegments[i].normal * radius;
                borderPoints.Add(curveSegments[i].position - offset);
            }
            
            // 闭合边界
            if (borderPoints.Count > 0)
            {
                borderPoints.Add(borderPoints[0]);
            }
            
            return borderPoints;
        }
        
        /// <summary>
        /// 更新鼠标位置
        /// </summary>
        /// <param name="worldPosition">鼠标世界坐标</param>
        public void UpdateMousePosition(Vector2 worldPosition)
        {
            mousePosition = worldPosition;
        }
        
        /// <summary>
        /// 设置可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        public void SetVisible(bool visible)
        {
            isVisible = visible;
            if (previewContainer != null)
            {
                previewContainer.SetActive(visible);
            }
        }
        
        /// <summary>
        /// 清除预览
        /// </summary>
        public void ClearPreview()
        {
            if (curveLineRenderer != null)
            {
                curveLineRenderer.positionCount = 0;
            }
            
            if (strokeBorderRenderer != null)
            {
                strokeBorderRenderer.positionCount = 0;
            }
        }
        
        /// <summary>
        /// 更新渲染
        /// </summary>
        public void UpdateRender()
        {
            // 确保预览对象与可见性设置一致
            if (previewContainer != null)
            {
                previewContainer.SetActive(isVisible);
            }
        }
        
        /// <summary>
        /// 销毁预览对象
        /// </summary>
        public void Destroy()
        {
            if (previewContainer != null)
            {
                Object.Destroy(previewContainer);
                previewContainer = null;
            }
            
            curveLineRenderer = null;
            strokeBorderRenderer = null;
        }
    }
    
    /// <summary>
    /// 简化的描边展开器，用于预览
    /// </summary>
    public static class SimpleStrokeExpander
    {
        /// <summary>
        /// 将曲线扩展为描边轮廓
        /// </summary>
        /// <param name="curveSegments">曲线段</param>
        /// <param name="width">描边宽度</param>
        /// <returns>描边轮廓点</returns>
        public static List<Vector2> ExpandToStroke(IReadOnlyList<CurveSegment> curveSegments, float width)
        {
            var strokePoints = new List<Vector2>();
            
            if (curveSegments.Count < 2)
            {
                return strokePoints;
            }
            
            float halfWidth = width * 0.5f;
            
            // 生成左侧边界
            for (int i = 0; i < curveSegments.Count; i++)
            {
                Vector2 offset = curveSegments[i].normal * halfWidth;
                strokePoints.Add(curveSegments[i].position + offset);
            }
            
            // 生成右侧边界（逆序）
            for (int i = curveSegments.Count - 1; i >= 0; i--)
            {
                Vector2 offset = curveSegments[i].normal * halfWidth;
                strokePoints.Add(curveSegments[i].position - offset);
            }
            
            return strokePoints;
        }
        
        /// <summary>
        /// 计算点到曲线的距离
        /// </summary>
        /// <param name="point">测试点</param>
        /// <param name="curveSegments">曲线段</param>
        /// <returns>最短距离</returns>
        public static float DistanceToStroke(Vector2 point, IReadOnlyList<CurveSegment> curveSegments)
        {
            if (curveSegments.Count == 0)
            {
                return float.MaxValue;
            }
            
            float minDistance = float.MaxValue;
            
            for (int i = 0; i < curveSegments.Count - 1; i++)
            {
                Vector2 segmentStart = curveSegments[i].position;
                Vector2 segmentEnd = curveSegments[i + 1].position;
                
                float distance = DistancePointToLineSegment(point, segmentStart, segmentEnd);
                minDistance = Mathf.Min(minDistance, distance);
            }
            
            return minDistance;
        }
        
        /// <summary>
        /// 计算点到线段的距离
        /// </summary>
        private static float DistancePointToLineSegment(Vector2 point, Vector2 lineStart, Vector2 lineEnd)
        {
            Vector2 line = lineEnd - lineStart;
            float lineLength = line.magnitude;
            
            if (lineLength < 0.001f)
            {
                return Vector2.Distance(point, lineStart);
            }
            
            Vector2 lineDirection = line / lineLength;
            Vector2 toPoint = point - lineStart;
            
            float projectionLength = Vector2.Dot(toPoint, lineDirection);
            projectionLength = Mathf.Clamp(projectionLength, 0f, lineLength);
            
            Vector2 closestPoint = lineStart + lineDirection * projectionLength;
            return Vector2.Distance(point, closestPoint);
        }
    }
} 