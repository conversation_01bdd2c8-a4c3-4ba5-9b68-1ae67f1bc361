using UnityEngine;
using System.Collections.Generic;
using MapEditor.Core;
using MapEditor.Config;

namespace MapEditor.Tools
{
    /// <summary>
    /// 控制点管理器，负责控制点的可视化和交互
    /// </summary>
    public class ControlPointManager
    {
        private CurveTool parentTool;
        private List<ControlPointGizmo> controlPointGizmos = new List<ControlPointGizmo>();
        private GameObject controlPointContainer;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="tool">父工具引用</param>
        public ControlPointManager(CurveTool tool)
        {
            parentTool = tool;
            CreateContainer();
        }
        
        /// <summary>
        /// 创建控制点容器
        /// </summary>
        private void CreateContainer()
        {
            controlPointContainer = new GameObject("CurveControlPoints");
            controlPointContainer.transform.position = Vector3.zero;
        }
        
        /// <summary>
        /// 更新控制点可视化
        /// </summary>
        /// <param name="controlPoints">控制点位置列表</param>
        public void UpdateControlPointVisuals(IReadOnlyList<Vector2> controlPoints, int selectedIndex)
        {
            // 确保有足够的Gizmo对象
            while (controlPointGizmos.Count < controlPoints.Count)
            {
                CreateControlPointGizmo(controlPointGizmos.Count);
            }
            
            // 更新Gizmo位置和可见性
            for (int i = 0; i < controlPointGizmos.Count; i++)
            {
                if (i < controlPoints.Count)
                {
                    controlPointGizmos[i].SetPosition(controlPoints[i]);
                    controlPointGizmos[i].SetVisible(true);
                    // 在这里设置状态，而不是在别处
                    controlPointGizmos[i].SetState(i == selectedIndex ? ControlPointState.Selected : ControlPointState.Normal);
                }
                else
                {
                    controlPointGizmos[i].SetVisible(false);
                }
            }
        }
        
        /// <summary>
        /// 获取指定位置的控制点索引
        /// </summary>
        /// <param name="worldPosition">世界坐标位置</param>
        /// <param name="threshold">检测阈值</param>
        /// <returns>控制点索引，-1表示没有找到</returns>
        public int GetControlPointAt(Vector2 worldPosition, float threshold)
        {
            for (int i = 0; i < controlPointGizmos.Count; i++)
            {
                if (controlPointGizmos[i].IsVisible && 
                    Vector2.Distance(controlPointGizmos[i].Position, worldPosition) <= threshold)
                {
                    return i;
                }
            }
            return -1;
        }
        
        /// <summary>
        /// 设置指定控制点的状态
        /// </summary>
        /// <param name="index">控制点索引</param>
        /// <param name="state">新状态</param>
        public void SetControlPointState(int index, ControlPointState state)
        {
            if (index >= 0 && index < controlPointGizmos.Count)
            {
                controlPointGizmos[index].SetState(state);
            }
        }

        public void SetSelectedControlPoint(int selectedIndex)
        {
            // 这个方法现在可以被简化，因为状态更新被移到了UpdateControlPointVisuals中
            // 我们只需要触发一次更新即可
            parentTool.UpdateControlPointVisuals();
        }
        
        /// <summary>
        /// 清除所有控制点可视化
        /// </summary>
        public void ClearControlPointVisuals()
        {
            foreach (var gizmo in controlPointGizmos)
            {
                gizmo.SetVisible(false);
            }
        }
        
        /// <summary>
        /// 创建控制点Gizmo
        /// </summary>
        /// <param name="index">控制点索引</param>
        private void CreateControlPointGizmo(int index)
        {
            GameObject gizmoObj = new GameObject($"ControlPoint_{index}");
            gizmoObj.transform.SetParent(controlPointContainer.transform);
            
            var gizmo = gizmoObj.AddComponent<ControlPointGizmo>();
            gizmo.Initialize(index);
            
            controlPointGizmos.Add(gizmo);
        }
        
        /// <summary>
        /// 销毁控制点管理器
        /// </summary>
        public void Destroy()
        {
            if (controlPointContainer != null)
            {
                Object.Destroy(controlPointContainer);
                controlPointContainer = null;
            }
            controlPointGizmos.Clear();
        }
    }
    
    /// <summary>
    /// 控制点可视化组件
    /// </summary>
    public class ControlPointGizmo : MonoBehaviour, IRenderable
    {
        private SpriteRenderer spriteRenderer;
        private int pointIndex;
        private Vector2 position;
        private ControlPointState currentState = ControlPointState.Normal;
        private bool isVisible = false;
        
        // 可视化参数
        private const float GIZMO_SIZE = 0.3f;
        private const float SELECTED_SIZE = 0.4f;
        
        // 颜色配置
        private static readonly Color NormalColor = Color.green;
        private static readonly Color SelectedColor = Color.yellow;
        private static readonly Color HoveredColor = Color.cyan;
        
        /// <summary>
        /// 渲染优先级
        /// </summary>
        public int RenderOrder => 100;
        
        /// <summary>
        /// 渲染层级
        /// </summary>
        public RenderLayer Layer => RenderLayer.UI;
        
        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible { get => isVisible; set => isVisible = value; }
        
        /// <summary>
        /// 控制点位置
        /// </summary>
        public Vector2 Position => position;

       // bool IRenderable.IsVisible { get => IsVisible; set => throw new System.NotImplementedException(); }

        /// <summary>
        /// 初始化控制点Gizmo
        /// </summary>
        /// <param name="index">控制点索引</param>
        public void Initialize(int index)
        {
            pointIndex = index;
            CreateVisual();
        }
        
        /// <summary>
        /// 创建可视化组件
        /// </summary>
        private void CreateVisual()
        {
            // 创建圆形纹理
            Texture2D circleTexture = CreateCircleTexture(32);
            
            // 创建精灵
            Sprite circleSprite = Sprite.Create(
                circleTexture,
                new Rect(0, 0, circleTexture.width, circleTexture.height),
                new Vector2(0.5f, 0.5f),
                circleTexture.width
            );
            
            // 创建SpriteRenderer
            spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
            spriteRenderer.sprite = circleSprite;
            spriteRenderer.sortingOrder = SortingOrderConfig.UIRange.max;
            
            // 初始设置
            SetVisible(false);
            UpdateVisual();
        }
        
        /// <summary>
        /// 设置控制点位置
        /// </summary>
        /// <param name="worldPosition">世界坐标位置</param>
        public void SetPosition(Vector2 worldPosition)
        {
            position = worldPosition;
            transform.position = new Vector3(worldPosition.x, worldPosition.y, 0);
        }
        
        /// <summary>
        /// 设置可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        public void SetVisible(bool visible)
        {
            isVisible = visible;
            gameObject.SetActive(visible);
        }
        
        /// <summary>
        /// 设置控制点状态
        /// </summary>
        /// <param name="state">新状态</param>
        public void SetState(ControlPointState state)
        {
            if (currentState != state)
            {
                currentState = state;
                UpdateVisual();
            }
        }
        
        /// <summary>
        /// 更新可视化
        /// </summary>
        public void UpdateRender()
        {
            // 空实现，控制点是静态的
        }
        
        /// <summary>
        /// 更新控制点的可视化表现
        /// </summary>
        private void UpdateVisual()
        {
            if (spriteRenderer == null) return;
            
            // 根据状态设置颜色和大小
            switch (currentState)
            {
                case ControlPointState.Normal:
                    spriteRenderer.color = NormalColor;
                    transform.localScale = Vector3.one * GIZMO_SIZE;
                    break;
                    
                case ControlPointState.Selected:
                    spriteRenderer.color = SelectedColor;
                    transform.localScale = Vector3.one * SELECTED_SIZE;
                    break;
                    
                case ControlPointState.Hovered:
                    spriteRenderer.color = HoveredColor;
                    transform.localScale = Vector3.one * SELECTED_SIZE;
                    break;
            }
        }
        
        /// <summary>
        /// 创建圆形纹理
        /// </summary>
        /// <param name="size">纹理大小</param>
        /// <returns>圆形纹理</returns>
        private Texture2D CreateCircleTexture(int size)
        {
            Texture2D texture = new Texture2D(size, size, TextureFormat.RGBA32, false);
            texture.filterMode = FilterMode.Bilinear;
            
            Color[] colors = new Color[size * size];
            float center = size / 2f;
            float radius = center - 1f;
            
            for (int y = 0; y < size; y++)
            {
                for (int x = 0; x < size; x++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), new Vector2(center, center));
                    float alpha = distance <= radius ? 1f : 0f;
                    
                    // 边缘抗锯齿
                    if (distance > radius - 1f && distance <= radius)
                    {
                        alpha = radius - distance;
                    }
                    
                    colors[y * size + x] = new Color(1, 1, 1, alpha);
                }
            }
            
            texture.SetPixels(colors);
            texture.Apply();
            
            return texture;
        }
        
        private void OnDestroy()
        {
            if (spriteRenderer?.sprite?.texture != null)
            {
                DestroyImmediate(spriteRenderer.sprite.texture);
            }
        }
    }
    
    /// <summary>
    /// 控制点状态枚举
    /// </summary>
    public enum ControlPointState
    {
        Normal,     // 普通状态
        Selected,   // 选中状态
        Hovered     // 悬停状态
    }
} 