using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using MapEditor.Tools;
using MapEditor.Services;
using MapEditor.Event;


namespace MapEditor.UI
{
    /// <summary>
    /// 对象工具面板UI
    /// </summary>
    public class ObjectToolPanel : UIPanel
    {
        public override string PanelId => "ObjectTool";
        public override string DisplayName => "对象工具";

        // UI元素引用
        private VisualElement paintModePanel;
        
        // 绘制模式UI
        private VisualElement prefabListContent;
        private FloatField paintRotationField;
        private FloatField paintScaleX, paintScaleY;
        private VisualElement selectedPrefabIcon;
        private Label selectedPrefabName;

        // 数据
        private ObjectToolPaint objectToolPaint;
        private List<ObjectPrefabData> availablePrefabs = new List<ObjectPrefabData>();
        private string selectedPrefabGuid;

        // 新增：树形视图和搜索相关
        private TreeView prefabTreeView;
        private TextField prefabSearchField;
        private List<TreeViewItemData<PrefabTreeItemData>> treeRootItems = new();
        private Dictionary<string, List<ObjectPrefabData>> prefabsByFolder = new();
        private string currentSearchQuery = "";

        public ObjectToolPanel(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
        }

        public override void Initialize()
        {
            // 获取对象工具引用
            var toolService = MapEditorCore.Instance.GetService<ToolService>();
            objectToolPaint = toolService?.GetTool("ObjectToolPaint") as ObjectToolPaint;

            // 查找UI元素
            paintModePanel = FindElement<VisualElement>("PaintModePanel");

            // 绘制模式UI
            prefabListContent = FindElement<VisualElement>("PrefabListContent");
            paintRotationField = FindElement<FloatField>("PaintRotationField");
            paintScaleX = FindElement<FloatField>("PaintScaleX");
            paintScaleY = FindElement<FloatField>("PaintScaleY");

            // 绘制模式UI - 新的树形视图和搜索
            prefabTreeView = FindElement<TreeView>("PrefabTreeView");
            prefabSearchField = FindElement<TextField>("PrefabSearchField");
            selectedPrefabIcon = FindElement<VisualElement>("SelectedPrefabIcon");
            selectedPrefabName = FindElement<Label>("SelectedPrefabName");

            // 设置树形视图
            if (prefabTreeView != null)
            {
                prefabTreeView.makeItem = MakeTreeItem;
                prefabTreeView.bindItem = BindTreeItem;
                prefabTreeView.selectionType = SelectionType.Single;
                prefabTreeView.selectionChanged += OnTreeSelectionChanged;
                
                // 确保树形视图可以接收事件
                prefabTreeView.focusable = true;
                prefabTreeView.pickingMode = PickingMode.Position;
            }

            // 设置搜索框
            if (prefabSearchField != null)
            {
                prefabSearchField.RegisterValueChangedCallback(evt => OnSearchQueryChanged(evt.newValue));
            }

            // 注册绘制参数即时修改事件
            RegisterPaintParameterEvents();

            // 设置默认值
            if (paintScaleX != null) paintScaleX.value = 1f;
            if (paintScaleY != null) paintScaleY.value = 1f;

            // 加载可用预制体
            LoadAvailablePrefabs();
            RefreshPrefabList();

            // 初始化选中预制体显示
            if (selectedPrefabName != null)
            {
                selectedPrefabName.text = "未选择预制体";
            }
        }

        public override void UpdatePanel()
        {
            // 面板更新逻辑（如果需要）
        }

        /// <summary>
        /// 注册绘制参数的即时修改事件
        /// </summary>
        private void RegisterPaintParameterEvents()
        {
            if (paintRotationField != null)
                paintRotationField.RegisterValueChangedCallback(evt => UpdatePaintParameters());
            if (paintScaleX != null)
                paintScaleX.RegisterValueChangedCallback(evt => UpdatePaintParameters());
            if (paintScaleY != null)
                paintScaleY.RegisterValueChangedCallback(evt => UpdatePaintParameters());
        }

        /// <summary>
        /// 更新绘制参数到工具
        /// </summary>
        private void UpdatePaintParameters()
        {
            float rotation = paintRotationField?.value ?? 0f;
            Vector2 scale = new Vector2(paintScaleX?.value ?? 1f, paintScaleY?.value ?? 1f);
            
            // 更新ObjectToolPaint的参数
            if (objectToolPaint != null)
            {
                objectToolPaint.PaintRotation = rotation;
                objectToolPaint.PaintScale = scale;
            }
        }

        /// <summary>
        /// 加载可用预制体（支持文件夹分类）
        /// </summary>
        private void LoadAvailablePrefabs()
        {
            availablePrefabs.Clear();
            prefabsByFolder.Clear();
            
            // 记录文件夹层级关系
            folderHierarchy = new Dictionary<string, string>();
            folderPaths = new Dictionary<string, string>();
            
            // 读取文件夹结构配置
            var folderStructure = LoadFolderStructure();
            
            // 构建层级关系
            BuildFolderHierarchy(folderStructure);
            
            // 按照从深到浅的顺序扫描文件夹，确保子文件夹先被处理
            var sortedFolders = folderStructure.OrderByDescending(f => f.Count(c => c == '/')).ToList();
            
            // 记录所有已处理的预制体
            var allProcessedPrefabs = new HashSet<string>();
            
            foreach (var folderPath in sortedFolders)
            {
                ScanFolderForPrefabs(folderPath, allProcessedPrefabs);
            }
            
            // 如果没有找到预制体，添加提示
            if (availablePrefabs.Count == 0)
            {
                var emptyData = new ObjectPrefabData 
                { 
                    Guid = "", 
                    Name = "未找到预制体", 
                    IconPath = "",
                    Prefab = null,
                    FolderPath = "ObjectPrefabs"
                };
                availablePrefabs.Add(emptyData);
                prefabsByFolder["ObjectPrefabs"] = new List<ObjectPrefabData> { emptyData };
            }
            

            
            // 构建树形数据
            BuildTreeData();
        }
        
        // 文件夹路径映射
        private Dictionary<string, string> folderPaths;
        
        /// <summary>
        /// 加载文件夹结构配置
        /// </summary>
        private List<string> LoadFolderStructure()
        {
            var folders = new List<string>();
            
            // 尝试加载配置文件
            var configText = Resources.Load<TextAsset>("ObjectPrefabs/folder_structure");
            if (configText != null)
            {
                var lines = configText.text.Split('\n');
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (!string.IsNullOrEmpty(trimmedLine) && !trimmedLine.StartsWith("#"))
                    {
                        folders.Add(trimmedLine);
                    }
                }
            }
            else
            {
                // 如果没有配置文件，使用默认结构

                folders.Add("ObjectPrefabs");
            }
            
            return folders;
        }
        
        /// <summary>
        /// 构建文件夹层级关系
        /// </summary>
        private void BuildFolderHierarchy(List<string> folders)
        {
            foreach (var folder in folders)
            {
                var folderName = GetFolderName(folder);
                
                // 存储文件夹的完整路径
                folderPaths[folderName] = folder;
                
                // 查找父文件夹
                var lastSlash = folder.LastIndexOf('/');
                if (lastSlash > 0)
                {
                    var parentPath = folder.Substring(0, lastSlash);
                    var parentName = GetFolderName(parentPath);
                    folderHierarchy[folderName] = parentName;
                }
                
                // 确保文件夹在字典中存在
                if (!prefabsByFolder.ContainsKey(folderName))
                {
                    prefabsByFolder[folderName] = new List<ObjectPrefabData>();
                }
            }
        }
        
        /// <summary>
        /// 扫描文件夹中的预制体
        /// </summary>
        private void ScanFolderForPrefabs(string folderPath, HashSet<string> allProcessedPrefabs)
        {
            var folderName = GetFolderName(folderPath);
            
            // 加载当前文件夹的所有预制体
            var prefabs = Resources.LoadAll<GameObject>(folderPath);
            
            foreach (var prefab in prefabs)
            {
                if (prefab != null && !allProcessedPrefabs.Contains(prefab.name))
                {
                    // 添加到已处理集合
                    allProcessedPrefabs.Add(prefab.name);
                    
                    var prefabData = new ObjectPrefabData
                    {
                        Guid = $"{folderPath}/{prefab.name}", // 使用完整路径作为GUID
                        Name = prefab.name,
                        IconPath = $"ObjectIcons/{prefab.name}",
                        Prefab = prefab,
                        FolderPath = folderName,
                        ResourcePath = $"{folderPath}/{prefab.name}"
                    };
                    
                    prefabsByFolder[folderName].Add(prefabData);
                    availablePrefabs.Add(prefabData);
                }
            }
        }

        
        // 文件夹层级关系
        private Dictionary<string, string> folderHierarchy;
        
        /// <summary>
        /// 从路径获取文件夹名称
        /// </summary>
        private string GetFolderName(string path)
        {
            int lastSlash = path.LastIndexOf('/');
            return lastSlash >= 0 ? path.Substring(lastSlash + 1) : path;
        }

        /// <summary>
        /// 构建树形数据结构
        /// </summary>
        private void BuildTreeData()
        {
            treeRootItems.Clear();
            
            // 使用计数器确保唯一ID
            int idCounter = 0;
            
            // 如果有搜索查询，显示平铺列表
            if (!string.IsNullOrEmpty(currentSearchQuery))
            {
                var filteredPrefabs = availablePrefabs.Where(p => 
                    p.Name.ToLower().Contains(currentSearchQuery.ToLower())).ToList();
                
                foreach (var prefab in filteredPrefabs)
                {
                    var itemData = new PrefabTreeItemData
                    {
                        IsFolder = false,
                        Name = prefab.Name,
                        PrefabData = prefab
                    };
                    treeRootItems.Add(new TreeViewItemData<PrefabTreeItemData>(idCounter++, itemData));
                }
            }
            else
            {
                // 根据扫描的层级关系构建树形结构
                
                // 找出所有根文件夹（没有父文件夹的）
                var rootFolders = new List<string>();
                foreach (var folder in prefabsByFolder.Keys)
                {
                    if (!folderHierarchy.ContainsKey(folder))
                    {
                        rootFolders.Add(folder);
                    }
                }
                
                // 如果只有一个根文件夹且是ObjectPrefabs，直接显示其子文件夹
                if (rootFolders.Count == 1 && rootFolders[0] == "ObjectPrefabs")
                {
                    // 直接添加ObjectPrefabs的子文件夹作为根
                    foreach (var kvp in folderHierarchy)
                    {
                        if (kvp.Value == "ObjectPrefabs")
                        {
                            var childItem = BuildFolderTree(kvp.Key, ref idCounter);
                            if (childItem.HasValue)
                            {
                                treeRootItems.Add(childItem.Value);
                            }
                        }
                    }
                }
                else
                {
                    // 正常构建每个根文件夹的树
                    foreach (var rootFolder in rootFolders.OrderBy(f => f))
                    {
                        var rootItem = BuildFolderTree(rootFolder, ref idCounter);
                        if (rootItem.HasValue)
                        {
                            treeRootItems.Add(rootItem.Value);
                        }
                    }
                }
            }
            
            // 更新树形视图
            if (prefabTreeView != null)
            {
                prefabTreeView.SetRootItems(treeRootItems);
                prefabTreeView.Rebuild();
            }
        }

        /// <summary>
        /// 递归构建文件夹树
        /// </summary>
        private TreeViewItemData<PrefabTreeItemData>? BuildFolderTree(string folderName, ref int idCounter)
        {
            if (!prefabsByFolder.ContainsKey(folderName))
                return null;
                
            var folderData = new PrefabTreeItemData
            {
                IsFolder = true,
                Name = folderName,
                FolderPath = folderName
            };
            
            var children = new List<TreeViewItemData<PrefabTreeItemData>>();
            
            // 添加子文件夹
            foreach (var kvp in folderHierarchy)
            {
                if (kvp.Value == folderName)
                {
                    var childFolder = kvp.Key;
                    var childItem = BuildFolderTree(childFolder, ref idCounter);
                    if (childItem.HasValue)
                    {
                        children.Add(childItem.Value);
                    }
                }
            }
            
            // 添加当前文件夹中的预制体
            var prefabs = prefabsByFolder[folderName].Where(p => p.Prefab != null).OrderBy(p => p.Name).ToList();
            foreach (var prefab in prefabs)
            {
                var prefabItemData = new PrefabTreeItemData
                {
                    IsFolder = false,
                    Name = prefab.Name,
                    PrefabData = prefab
                };
                
                children.Add(new TreeViewItemData<PrefabTreeItemData>(
                    idCounter++,
                    prefabItemData
                ));
            }
            
            // 如果文件夹为空（没有子文件夹也没有预制体），不显示它
            if (children.Count == 0)
                return null;
                
            return new TreeViewItemData<PrefabTreeItemData>(
                idCounter++,
                folderData,
                children
            );
        }

        /// <summary>
        /// 获取文件夹显示名称
        /// </summary>
        private string GetFolderDisplayName(string folderPath)
        {
            // 直接返回文件夹路径作为显示名称，因为我们已经使用了友好的中文名称
            return folderPath;
        }

        /// <summary>
        /// 创建树形项UI
        /// </summary>
        private VisualElement MakeTreeItem()
        {
            var container = new VisualElement();
            container.AddToClassList("prefab-tree-item");
            container.style.flexDirection = FlexDirection.Row;
            container.style.alignItems = Align.Center;
            
            // 确保容器可以接收鼠标事件
            container.pickingMode = PickingMode.Position;
            
            var icon = new VisualElement();
            icon.name = "ItemIcon";
            icon.AddToClassList("prefab-item-icon");
            icon.pickingMode = PickingMode.Ignore; // 图标不拦截事件
            
            var label = new Label();
            label.name = "ItemLabel";
            label.AddToClassList("prefab-item-label");
            label.pickingMode = PickingMode.Ignore; // 标签不拦截事件
            
            container.Add(icon);
            container.Add(label);
            
            return container;
        }

        /// <summary>
        /// 绑定树形项数据
        /// </summary>
        private void BindTreeItem(VisualElement element, int index)
        {
            var itemData = prefabTreeView.GetItemDataForIndex<PrefabTreeItemData>(index);
            
            var icon = element.Q<VisualElement>("ItemIcon");
            var label = element.Q<Label>("ItemLabel");
            
            if (itemData.IsFolder)
            {
                icon.RemoveFromClassList("prefab-item-icon");
                icon.AddToClassList("prefab-folder-icon");
                label.text = itemData.Name;
            }
            else
            {
                icon.RemoveFromClassList("prefab-folder-icon");
                icon.AddToClassList("prefab-item-icon");
                label.text = itemData.Name;
                
                // 设置预制体图标
                if (itemData.PrefabData != null)
                {
                    SetPrefabIcon(icon, itemData.PrefabData);
                }
            }
        }

        /// <summary>
        /// 树形视图选择变更事件
        /// </summary>
        private void OnTreeSelectionChanged(IEnumerable<object> selectedItems)
        {
            var selected = selectedItems.FirstOrDefault();
            
            // TreeView在新版本中直接返回数据对象而不是索引
            if (selected is PrefabTreeItemData itemData)
            {
                if (!itemData.IsFolder && itemData.PrefabData != null)
                {
                    SelectPrefab(itemData.PrefabData.Guid);
                }
            }
            else if (selected is int index)
            {
                // 兼容旧版本行为
                var data = prefabTreeView.GetItemDataForIndex<PrefabTreeItemData>(index);
                
                if (!data.IsFolder && data.PrefabData != null)
                {
                    SelectPrefab(data.PrefabData.Guid);
                }
            }
        }

        /// <summary>
        /// 搜索查询变更事件
        /// </summary>
        private void OnSearchQueryChanged(string newQuery)
        {
            currentSearchQuery = newQuery;
            BuildTreeData();
        }

        /// <summary>
        /// 设置预制体图标
        /// </summary>
        private void SetPrefabIcon(VisualElement iconElement, ObjectPrefabData prefabData)
        {
            if (iconElement == null || prefabData == null) return;
            
            // 首先尝试从预制体的SpriteRenderer获取Sprite
            if (prefabData.Prefab != null)
            {
                var spriteRenderer = prefabData.Prefab.GetComponent<SpriteRenderer>();
                if (spriteRenderer != null && spriteRenderer.sprite != null)
                {
                    iconElement.style.backgroundImage = new StyleBackground(spriteRenderer.sprite);
                    return;
                }
            }
            
            // 尝试加载图标
            if (!string.IsNullOrEmpty(prefabData.IconPath))
            {
                var icon = Resources.Load<Texture2D>(prefabData.IconPath);
                if (icon != null)
                {
                    iconElement.style.backgroundImage = new StyleBackground(icon);
                    return;
                }
            }
            
            // 使用默认颜色
            var hash = prefabData.Guid.GetHashCode();
            var hue = (hash & 0xFFFF) / 65535f;
            var color = Color.HSVToRGB(hue, 0.6f, 0.8f);
            iconElement.style.backgroundColor = new StyleColor(color);
        }

        /// <summary>
        /// 刷新预制体列表
        /// </summary>
        private void RefreshPrefabList()
        {
            // 重新构建树形数据
            BuildTreeData();
        }

        /// <summary>
        /// 创建预制体列表项
        /// </summary>
        private VisualElement CreatePrefabListItem(ObjectPrefabData prefab)
        {
            var item = new VisualElement();
            item.AddToClassList("prefab-list-item");

            var label = new Label(prefab.Name);
            label.AddToClassList("prefab-label");

            var selectButton = new Button(() => SelectPrefab(prefab.Guid)) { text = "选择" };
            selectButton.AddToClassList("select-prefab-button");

            item.Add(label);
            item.Add(selectButton);

            // 如果当前选中，添加选中样式
            if (selectedPrefabGuid == prefab.Guid)
            {
                item.AddToClassList("selected");
            }

            return item;
        }

        /// <summary>
        /// 选择预制体
        /// </summary>
        private void SelectPrefab(string guid)
        {
            // 检查是否是无效的预制体
            if (string.IsNullOrEmpty(guid))
            {
                RequestShowMessage("警告", "无法选择此预制体", MessageType.Warning);
                return;
            }
            
            selectedPrefabGuid = guid;
            
            // 查找预制体数据
            var prefabData = availablePrefabs.FirstOrDefault(p => p.Guid == guid);
            if (prefabData != null)
            {
                // 更新选中的预制体显示
                UpdateSelectedPrefabDisplay(prefabData);
                
                // 更新ObjectToolPaint的选中预制体
                if (objectToolPaint != null)
                {
                    objectToolPaint.SelectedPrefabGuid = guid;
                }
            }

            BuildTreeData(); // 刷新树形视图
        }
        
        /// <summary>
        /// 更新选中的预制体显示
        /// </summary>
        private void UpdateSelectedPrefabDisplay(ObjectPrefabData prefabData)
        {
            if (selectedPrefabName != null)
            {
                selectedPrefabName.text = prefabData.Name;
            }
            
            if (selectedPrefabIcon != null && prefabData.Prefab != null)
            {
                // 尝试从预制体获取Sprite
                var spriteRenderer = prefabData.Prefab.GetComponent<SpriteRenderer>();
                if (spriteRenderer != null && spriteRenderer.sprite != null)
                {
                    selectedPrefabIcon.style.backgroundImage = new StyleBackground(spriteRenderer.sprite);
                }
                else
                {
                    // 如果没有SpriteRenderer，使用SetPrefabIcon的逻辑
                    SetPrefabIcon(selectedPrefabIcon, prefabData);
                }
            }
        }

        /// <summary>
        /// 创建对象预制体数据类
        /// </summary>
        private class ObjectPrefabData
        {
            public string Guid { get; set; }
            public string Name { get; set; }
            public string IconPath { get; set; }
            public GameObject Prefab { get; set; }
            public string FolderPath { get; set; }
            public string ResourcePath { get; set; }
        }
        
        /// <summary>
        /// 树形视图项数据
        /// </summary>
        private class PrefabTreeItemData
        {
            public bool IsFolder { get; set; }
            public string Name { get; set; }
            public string FolderPath { get; set; }
            public ObjectPrefabData PrefabData { get; set; }
        }

        /// <summary>
        /// 添加图标文本
        /// </summary>
        private void AddIconText(VisualElement iconElement, string text, Color color)
        {
            iconElement.Clear();
            var label = new Label(text);
            label.style.unityTextAlign = TextAnchor.MiddleCenter;
            label.style.color = new StyleColor(color);
            label.style.fontSize = 18;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.width = Length.Percent(100);
            label.style.height = Length.Percent(100);
            iconElement.Add(label);
        }
    }
} 