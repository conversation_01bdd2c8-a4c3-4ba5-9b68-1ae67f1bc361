using MapEditor.Core;
using UnityEngine;
using MapEditor.Config;


namespace MapEditor.Tools
{
    public class BrushPreviewRenderer : MonoBehaviour, IRenderable
    {
        // 预览对象
        private GameObject previewObj;
        private SpriteRenderer spriteRenderer;

        // 画笔属性
        private Texture2D brushTexture;
        private Vector2 position;
        private float brushSize = 1.0f;
        private Color brushColor = Color.white;

        // 场景渲染器引用
        private ISceneRenderer sceneRenderer;

        /// <summary>
        /// 渲染优先级
        /// </summary>
        public int RenderOrder => 0;

        /// <summary>
        /// 渲染层级
        /// </summary>
        public RenderLayer Layer => RenderLayer.Preview;

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// 初始化
        /// </summary>
        public void Initialize(ISceneRenderer renderer, Texture2D texture)
        {
            sceneRenderer = renderer;
            brushTexture = texture;

            // 创建预览精灵
            if (previewObj == null)
            {
                // 创建精灵对象
                previewObj = new GameObject("BrushPreview");
                previewObj.transform.SetParent(transform);

                spriteRenderer = previewObj.AddComponent<SpriteRenderer>();
                spriteRenderer.sortingOrder = SortingOrderConfig.PreviewRange.min; // 使用配置的 Preview 区间基准

                // 从画笔纹理创建精灵(像素单位设为纹理宽度, 保证 1:1 世界单位)
                Sprite brushSprite = Sprite.Create(
                    brushTexture,
                    new Rect(0, 0, brushTexture.width, brushTexture.height),
                    new Vector2(0.5f, 0.5f),
                    brushTexture.width // PPU = texture width, 让缩放与世界单位对应
                );

                spriteRenderer.sprite = brushSprite;
                spriteRenderer.color = new Color(brushColor.r, brushColor.g, brushColor.b, 0.5f); // 半透明

                // 初次创建时立即同步大小
                UpdateScale();
            }

            // 注册到场景渲染器
            sceneRenderer.RegisterRenderable(this);
        }

        /// <summary>
        /// 组件销毁时的处理
        /// </summary>
        private void OnDestroy()
        {
            // 从场景渲染器注销
            sceneRenderer?.UnregisterRenderable(this);
        }

        /// <summary>
        /// 设置画笔纹理
        /// </summary>
        public void SetBrushTexture(Texture2D texture)
        {
            if (texture != null && spriteRenderer != null)
            {
                brushTexture = texture;

                // 更新精灵
                Sprite brushSprite = Sprite.Create(
                    brushTexture,
                    new Rect(0, 0, brushTexture.width, brushTexture.height),
                    new Vector2(0.5f, 0.5f),
                    brushTexture.width // PPU = texture width
                );

                spriteRenderer.sprite = brushSprite;

                // 纹理变化后重新计算缩放
                UpdateScale();
            }
        }

        /// <summary>
        /// 设置画笔位置
        /// </summary>
        public void SetPosition(Vector2 worldPosition)
        {
            position = worldPosition;

            if (previewObj != null)
            {
                previewObj.transform.position = new Vector3(position.x, position.y, 0);
            }
        }

        /// <summary>
        /// 设置画笔属性
        /// </summary>
        public void SetBrushProperties(float size, Color color)
        {
            brushSize = size;
            brushColor = color;

            if (previewObj != null)
            {
                // 更新大小
                UpdateScale();

                // 更新颜色（保持半透明）
                if (spriteRenderer != null)
                {
                    spriteRenderer.color = new Color(brushColor.r, brushColor.g, brushColor.b, 0.5f);
                }
            }
        }

        /// <summary>
        /// 更新渲染
        /// </summary>
        public void UpdateRender()
        {
            // 确保预览对象与可见性设置一致
            previewObj?.SetActive(IsVisible);
        }

        /// <summary>
        /// 根据当前 brushSize 与 sprite 像素密度重新计算缩放，使预览尺寸与实际绘制保持一致。
        /// </summary>
        private void UpdateScale()
        {
            if (previewObj == null || spriteRenderer == null || spriteRenderer.sprite == null) return;

            // sprite 世界单位宽度 = textureWidth / pixelsPerUnit
            float baseWorldSize = brushTexture.width / spriteRenderer.sprite.pixelsPerUnit;
            if (baseWorldSize <= 0f) baseWorldSize = 1f;

            float scaleFactor = brushSize / baseWorldSize;
            previewObj.transform.localScale = Vector3.one * scaleFactor;
        }
    }

}