using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Core
{
    public interface IChunkScheduler
    {
        void GetVisibleChunks(IMapLayer layer, Bounds viewBounds, List<ChunkCoord> result);
    }

    /// <summary>
    /// 简单同步实现。
    /// </summary>
    public class SimpleChunkScheduler : IChunkScheduler
    {
        public void GetVisibleChunks(IMapLayer layer, Bounds viewBounds, List<ChunkCoord> result)
        {
            if (layer == null) return;
            result.Clear();

            // 以世界坐标计算 Chunk 索引（1 world unit = 1/PPU pixel）。
            float csWorld = layer.ChunkSize / (float)MapEditorConfig.PixelsPerUnit;

            int minX = Mathf.FloorToInt(viewBounds.min.x / csWorld);
            int minY = Mathf.FloorToInt(viewBounds.min.y / csWorld);
            int maxX = Mathf.FloorToInt(viewBounds.max.x / csWorld);
            int maxY = Mathf.FloorToInt(viewBounds.max.y / csWorld);

            // 限制在图层实际范围内（避免生成无效 Chunk 坐标）。
            if (layer is MapLayer mapLayer)
            {
                Vector2Int realSize = mapLayer.RealLayerSize;
                int maxChunkX = Mathf.Max(0, Mathf.CeilToInt(realSize.x / (float)layer.ChunkSize) - 1);
                int maxChunkY = Mathf.Max(0, Mathf.CeilToInt(realSize.y / (float)layer.ChunkSize) - 1);

                minX = Mathf.Clamp(minX, 0, maxChunkX);
                minY = Mathf.Clamp(minY, 0, maxChunkY);
                maxX = Mathf.Clamp(maxX, 0, maxChunkX);
                maxY = Mathf.Clamp(maxY, 0, maxChunkY);
            }

            for (int x = minX; x <= maxX; x++)
            {
                for (int y = minY; y <= maxY; y++)
                {
                    result.Add(new ChunkCoord(x, y));
                }
            }
        }
    }
} 