using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// 网格设置面板，当选中网格层时显示
    /// </summary>
    public class GridSettingsPanel : UIPanel
    {
        public override string PanelId => "GridSettings";
        public override string DisplayName => "网格设置";

        private GridService gridService;
        private IGridRenderer gridRenderer;
        
        // UI控件引用
        private IntegerField gridIntervalField;
        private FloatField lineWidthField;
        private FloatField majorLineWidthField;
        private VisualElement gridColorField;
        private VisualElement majorGridColorField;
        
        // 简化的颜色字段（因为UI Toolkit没有内置ColorField）
        private Button gridColorButton;
        private Button majorGridColorButton;
        
        private Color currentGridColor = new Color(0.6f, 0.6f, 0.6f, 0.6f);
        private Color currentMajorGridColor = new Color(0.9f, 0.9f, 0.9f, 0.8f);

        public GridSettingsPanel(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
        }

        public override void Initialize()
        {
            gridService = MapEditorCore.Instance.GetService<GridService>();
            var eventService = MapEditorCore.Instance.EventSystem;

            // 查找UI控件
            gridIntervalField = FindElement<IntegerField>("GridIntervalField");
            lineWidthField = FindElement<FloatField>("LineWidthField");
            majorLineWidthField = FindElement<FloatField>("MajorLineWidthField");
            gridColorField = FindElement<VisualElement>("GridColorField");
            majorGridColorField = FindElement<VisualElement>("MajorGridColorField");

            // 创建颜色按钮（简化实现）
            CreateColorButtons();
            
            // 注册事件
            RegisterControlEvents();
            
            // 监听活动图层变更
            eventService.Subscribe<ActiveLayerChangedEvent>(OnActiveLayerChanged);
            
            // 将面板添加到右侧属性面板的容器中
            var propertiesContainer = UIManager.Instance.Root?.Q<ScrollView>("PropertiesContainer");
            if (propertiesContainer != null)
            {
                propertiesContainer.Add(root);
                Debug.Log("GridSettingsPanel added to PropertiesContainer");
            }
            else
            {
                Debug.LogWarning("PropertiesContainer not found, GridSettingsPanel may not display correctly");
            }
            
            // 初始隐藏面板
            IsVisible = false;
        }

        private void CreateColorButtons()
        {
            // 网格颜色按钮
            gridColorButton = new Button();
            gridColorButton.text = "网格颜色";
            gridColorButton.style.backgroundColor = currentGridColor;
            gridColorButton.clicked += () => ShowColorPicker(currentGridColor, OnGridColorChanged);
            gridColorField.Add(gridColorButton);
            
            // 主网格颜色按钮
            majorGridColorButton = new Button();
            majorGridColorButton.text = "主网格颜色";
            majorGridColorButton.style.backgroundColor = currentMajorGridColor;
            majorGridColorButton.clicked += () => ShowColorPicker(currentMajorGridColor, OnMajorGridColorChanged);
            majorGridColorField.Add(majorGridColorButton);
        }

        private void RegisterControlEvents()
        {
            if (gridIntervalField != null)
            {
                gridIntervalField.RegisterValueChangedCallback(evt => OnGridIntervalChanged(evt.newValue));
            }
            
            if (lineWidthField != null)
            {
                lineWidthField.RegisterValueChangedCallback(evt => OnLineWidthChanged(evt.newValue));
            }
            
            if (majorLineWidthField != null)
            {
                majorLineWidthField.RegisterValueChangedCallback(evt => OnMajorLineWidthChanged(evt.newValue));
            }
        }

        private void OnActiveLayerChanged(ActiveLayerChangedEvent evt)
        {
            // 只有当选中网格层时才显示面板
            bool shouldShow = evt.NewLayer != null && evt.NewLayer.Type == LayerType.Grid;
            IsVisible = shouldShow;
            
            if (shouldShow)
            {
                // 刷新当前网格设置
                RefreshGridSettings();
            }
        }

        private void RefreshGridSettings()
        {
            // 从GridService获取当前网格设置
            if (gridService != null)
            {
                var settings = gridService.GetCurrentGridSettings();
                
                if (gridIntervalField != null)
                {
                    gridIntervalField.value = settings.gridInterval;
                }
                
                if (lineWidthField != null)
                {
                    lineWidthField.value = settings.lineWidth;
                }
                
                if (majorLineWidthField != null)
                {
                    majorLineWidthField.value = settings.majorLineWidth;
                }
                
                // 更新颜色
                currentGridColor = settings.gridColor;
                currentMajorGridColor = settings.majorGridColor;
                
                if (gridColorButton != null)
                {
                    gridColorButton.style.backgroundColor = currentGridColor;
                }
                
                if (majorGridColorButton != null)
                {
                    majorGridColorButton.style.backgroundColor = currentMajorGridColor;
                }
            }
            else
            {
                // GridService未就绪时使用默认值
                if (gridIntervalField != null)
                {
                    gridIntervalField.value = 16;
                }
                
                if (lineWidthField != null)
                {
                    lineWidthField.value = 0.05f;
                }
                
                if (majorLineWidthField != null)
                {
                    majorLineWidthField.value = 0.1f;
                }
            }
        }

        private void OnGridIntervalChanged(int newInterval)
        {
            if (newInterval <= 0) return;
            
            gridService?.UpdateGridSettings(gridInterval: newInterval);
        }

        private void OnLineWidthChanged(float newWidth)
        {
            if (newWidth <= 0) return;
            
            gridService?.UpdateGridSettings(lineWidth: newWidth);
        }

        private void OnMajorLineWidthChanged(float newWidth)
        {
            if (newWidth <= 0) return;
            
            gridService?.UpdateGridSettings(majorLineWidth: newWidth);
        }

        private void OnGridColorChanged(Color newColor)
        {
            currentGridColor = newColor;
            gridColorButton.style.backgroundColor = newColor;
            gridService?.UpdateGridSettings(gridColor: newColor);
        }

        private void OnMajorGridColorChanged(Color newColor)
        {
            currentMajorGridColor = newColor;
            majorGridColorButton.style.backgroundColor = newColor;
            gridService?.UpdateGridSettings(majorGridColor: newColor);
        }

        private void ShowColorPicker(Color currentColor, System.Action<Color> onColorChanged)
        {
            // 获取颜色选择器对话框
            var colorPickerDialog = UIManager.Instance.GetPanel("ColorPickerDialog") as ColorPickerDialog;
            if (colorPickerDialog == null)
            {
                Debug.LogError("ColorPickerDialog not found in UIManager");
                return;
            }

            // 显示颜色选择器并设置回调
            colorPickerDialog.ShowDialog(
                currentColor,
                onConfirmed: (selectedColor) =>
                {
                    onColorChanged?.Invoke(selectedColor);
                    Debug.Log($"颜色已选择: {selectedColor}");
                },
                onCancelled: () =>
                {
                    Debug.Log("颜色选择已取消");
                }
            );
        }

        public override void UpdatePanel()
        {
            if (IsVisible)
            {
                RefreshGridSettings();
            }
        }
    }
} 