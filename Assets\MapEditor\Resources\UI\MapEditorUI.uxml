<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="UnityEngine.UIElements" schemaLocation="UnityEngine.UIElements ../UIElementsSchema/UnityEngine.UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/MapEditor/UI/MapEditorStyles.uss?fileID=7433441132597879392&amp;guid=dacb3703fd47af342abe56ffb6eea127&amp;type=3#MapEditorStyles" />
    <ui:VisualElement name="RootContainer" picking-mode="Ignore" class="map-editor-ui" style="position: absolute; left: 0; top: 0; right: 0; bottom: 0; width: 100%; height: 100%;">
        <ui:VisualElement name="Toolbar" style="position: absolute; left: 0; right: 0; top: 0; height: 40px; background-color: rgba(50, 50, 50, 0.9); flex-direction: row; align-items: center; padding: 0 10px;">
            <ui:Label text="地图编辑器" style="font-size: 16px; -unity-font-style: bold; color: rgb(73, 144, 226); margin-right: 20px;" />
            <ui:VisualElement name="ToolButtonContainer" style="flex-direction: row; align-items: center;">
                <!-- 工具按钮将在运行时动态生成 -->
            </ui:VisualElement>
            <ui:VisualElement style="flex-grow: 1;" />
            <ui:Button name="NewMapButton" text="新建" style="width: 60px; height: 30px; margin: 0 5px; background-color: rgb(255, 152, 0); color: white;" />
            <ui:Button name="SaveButton" text="保存" style="width: 60px; height: 30px; margin: 0 5px; background-color: rgb(76, 175, 80); color: white;" />
            <ui:Button name="LoadButton" text="加载" style="width: 60px; height: 30px; margin: 0 5px; background-color: rgb(33, 150, 243); color: white;" />
            <ui:Button name="SettingsButton" text="设置" style="width: 60px; height: 30px; margin: 0 5px; background-color: rgb(96, 125, 139); color: white;" />
        </ui:VisualElement>
        <ui:VisualElement name="LeftPanel" style="position: absolute; left: 0; top: 40px; bottom: 24px; width: 250px; background-color: rgba(45, 45, 45, 0.9); overflow: hidden;">
            <ui:VisualElement style="padding: 10px; height: 100%;">
                <ui:Label text="图层" style="font-size: 16px; -unity-font-style: bold; color: rgb(73, 144, 226); border-bottom-width: 1px; border-bottom-color: rgb(85, 85, 85); padding-bottom: 5px; margin-bottom: 10px;" />
                <ui:ScrollView name="LayerListContainer" style="flex-grow: 1;">
                    <!-- 图层项将在运行时动态生成 -->
                </ui:ScrollView>
                <ui:VisualElement style="flex-direction: row; justify-content: flex-end; margin-top: 8px;">
                    <ui:Button text="添加图层" style="width: 80px; height: 30px; background-color: rgb(73, 144, 226); color: white;" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="RightPanel" style="position: absolute; right: 0; top: 40px; bottom: 24px; width: 300px; background-color: rgba(45, 45, 45, 0.9); overflow: hidden;">
            <ui:VisualElement name="VisualElement" style="padding: 10px; height: 100%;">
                <ui:ScrollView name="PropertiesContainer" mouse-wheel-scroll-size="100" vertical-page-size="2" style="flex-grow: 1;" />
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="EditorArea" picking-mode="Ignore" style="position: absolute; left: 250px; right: 300px; top: 40px; bottom: 24px; background-color: rgba(0, 0, 0, 0); display: none;" />
        <ui:VisualElement name="StatusBar" style="position: absolute; left: 0; right: 0; bottom: 0; height: 24px; background-color: rgba(50, 50, 50, 0.9); flex-direction: row; align-items: center; padding: 0 10px;">
            <ui:Label text="就绪" style="font-size: 12px; color: rgb(187, 187, 187);" />
            <ui:VisualElement style="flex-grow: 1;" />
            <ui:Label text="X: 0, Y: 0" style="font-size: 12px; color: rgb(187, 187, 187);" />
            <ui:Label text="缩放: 100%" style="font-size: 12px; color: rgb(187, 187, 187); margin-left: 16px;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
