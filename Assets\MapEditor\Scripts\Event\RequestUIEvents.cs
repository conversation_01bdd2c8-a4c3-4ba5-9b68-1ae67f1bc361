using MapEditor.Core;

namespace MapEditor.Event
{
    /// <summary>
    /// UI 请求事件：显示消息框
    /// </summary>
    public readonly struct RequestShowMessageEvent
    {
        public readonly string Title;
        public readonly string Message;
        public readonly MessageType Type;
        public RequestShowMessageEvent(string title, string message, MessageType type)
        {
            Title = title;
            Message = message;
            Type = type;
        }
    }

    /// <summary>
    /// UI 请求事件：显示指定面板
    /// </summary>
    public readonly struct RequestShowPanelEvent
    {
        public readonly string PanelId;
        public RequestShowPanelEvent(string panelId)
        {
            PanelId = panelId;
        }
    }

    /// <summary>
    /// UI 请求事件：隐藏指定面板
    /// </summary>
    public readonly struct RequestHidePanelEvent
    {
        public readonly string PanelId;
        public RequestHidePanelEvent(string panelId)
        {
            PanelId = panelId;
        }
    }

    /// <summary>
    /// UI 请求事件：注册面板
    /// </summary>
    public readonly struct RequestRegisterPanelEvent
    {
        public readonly IUIPanel Panel;
        public RequestRegisterPanelEvent(IUIPanel panel)
        {
            Panel = panel;
        }
    }

    /// <summary>
    /// UI 请求事件：注销面板
    /// </summary>
    public readonly struct RequestUnregisterPanelEvent
    {
        public readonly string PanelId;
        public RequestUnregisterPanelEvent(string panelId)
        {
            PanelId = panelId;
        }
    }
    
    /// <summary>
    /// 图层面板可见性变更事件
    /// </summary>
    public readonly struct LayerPanelVisibilityChangedEvent
    {
        public readonly bool IsHidden;
        public LayerPanelVisibilityChangedEvent(bool isHidden)
        {
            IsHidden = isHidden;
        }
    }
} 