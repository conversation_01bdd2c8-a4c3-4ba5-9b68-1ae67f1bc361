Shader "MapEditor/GridOverlay"
{
    Properties
    {
        _GridColor       ("Grid Color", Color) = (0.6,0.6,0.6,0.4)
        _MajorGridColor  ("Major Grid Color", Color) = (0.9,0.9,0.9,0.6)
        _CellSize        ("Cell Size (WU)", Vector) = (1,1,0,0)
        _MajorInterval   ("Major Interval", Float ) = 5
        _LineWidth       ("Line Width (WU)", Float ) = 0.05
        _MajorLineWidth  ("Major Line Width (WU)", Float) = 0.1
        _GridOrigin      ("Grid Origin", Vector) = (0,0,0,0)
    }
    SubShader
    {
        Tags 
        { 
            "RenderType"="Transparent" 
            "Queue"="Transparent"
            "RenderPipeline"="UniversalPipeline"
        }
        LOD 100
        
        Pass
        {
            Name "GridOverlay"
            Tags { "LightMode"="SRPDefaultUnlit" }
            
            ZWrite Off
            Blend SrcAlpha OneMinusSrcAlpha
            Cull Off

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            CBUFFER_START(UnityPerMaterial)
                float4 _GridColor;
                float4 _MajorGridColor;
                float2 _CellSize;
                float2 _GridOrigin;
                float  _MajorInterval;
                float  _LineWidth;
                float  _MajorLineWidth;
            CBUFFER_END

            struct Attributes
            {
                float3 positionOS : POSITION;
            };
            
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float3 worldPos   : TEXCOORD0;
            };

            Varyings vert(Attributes input)
            {
                Varyings o;
                o.positionCS = TransformObjectToHClip(input.positionOS);
                o.worldPos   = TransformObjectToWorld(input.positionOS).xyz;
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                float2 local = (i.worldPos.xy - _GridOrigin) / _CellSize;

                // 计算到最近单元边界的世界距离
                float2 fracPart   = frac(local);           // 0~1
                float2 distFrac   = min(fracPart, 1.0 - fracPart); // 距离最近格线的归一化距离(0 在边界)
                float2 distWorld  = distFrac * _CellSize;  // 转为世界单位 (distX, distY)

                // Minor line alpha (同方向)
                float halfMinor = _LineWidth * 0.5;
                float axMinor = saturate((halfMinor - distWorld.x) / halfMinor);
                float ayMinor = saturate((halfMinor - distWorld.y) / halfMinor);

                // Major line判断
                bool isMajorX = (fmod(round(local.x), _MajorInterval) == 0);
                bool isMajorY = (fmod(round(local.y), _MajorInterval) == 0);

                float halfMajor = _MajorLineWidth * 0.5;
                float axMajor = isMajorX ? saturate((halfMajor - distWorld.x) / halfMajor) : 0.0;
                float ayMajor = isMajorY ? saturate((halfMajor - distWorld.y) / halfMajor) : 0.0;

                // Combine alpha
                float alpha = max(max(axMinor, ayMinor), max(axMajor, ayMajor));

                // Determine color: 使用majorIntensity衡量颜色插值
                float majorIntensity = max(axMajor, ayMajor);
                float4 col = lerp(_GridColor, _MajorGridColor, majorIntensity);
                col.a *= alpha;
                return col;
            }
            ENDHLSL
        }
    }
    FallBack "Hidden/Universal Render Pipeline/FallbackError"
} 