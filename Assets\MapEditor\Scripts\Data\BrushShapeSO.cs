using UnityEngine;

namespace MapEditor.Data
{
    /// <summary>
    /// 画笔形状（Alpha 蒙版）定义。
    /// </summary>
    [CreateAssetMenu(fileName = "BrushShape", menuName = "MapEditor/Brush Shape", order = 11)]
    public class BrushShapeSO : ScriptableObject
    {
        [Tooltip("灰度 AlphaMask 纹理 (白=100%, 黑=0%)")]
        public Texture2D alphaMask;

        private void OnValidate()
        {
            if (alphaMask != null)
                alphaMask.filterMode = FilterMode.Bilinear;
        }
    }
} 