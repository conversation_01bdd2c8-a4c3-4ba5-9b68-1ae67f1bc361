# 如何实现一个新的服务

本文档旨在指导开发者在 MapEditor 项目中创建一个新的服务。服务是项目架构的核心，用于封装独立的业务逻辑单元，并通过事件总线与其他系统部分解耦通信。

## 1. 核心概念

- **`IService` 接口**: 所有服务的基接口，定义了服务的生命周期方法，包括 `SetUp`, `Initialize`, `Start`, 和 `OnDestroy`。
- **`ServiceBase` 基类**: 一个抽象基类，实现了 `IService` 接口。它提供了对 `MapEditorCore` (通过 `core` 属性) 和 `EventBus` 的便捷访问方法（如 `RegisterEvent`, `PublishEvent`, `GetService`）。强烈建议所有新服务都继承自此类。
- **`MapEditorCore`**: 作为服务容器和管理器。它负责服务的注册、生命周期调用和依赖注入（通过 `GetService` 方法）。
- **`MapEditorStarter`**: 项目的启动器。所有核心服务都在此类的 `InitializeServices` 方法中被实例化并注册到 `MapEditorCore`。
- **生命周期**: 服务的生命周期由 `MapEditorCore` 严格管理：
    1.  **构造**: 服务被实例化。
    2.  **`SetUp`**: `MapEditorCore` 调用，传入 `core` 和 `eventSystem` 的引用。
    3.  **`Initialize`**: 在服务注册后立即调用。适合用于设置内部状态和订阅那些在服务启动前就可能触发的事件。
    4.  **`Start`**: 在所有服务都完成 `Initialize` 后的第一个 `Update` 帧调用。适合用于执行需要依赖其他服务已完成初始化的逻辑。
    5.  **`OnDestroy`**: 在编辑器关闭或服务被注销时调用。用于清理资源和取消事件订阅。

## 2. 开发流程

以下是创建一个新的“自动保存服务” (`AutoSaveService`) 的完整流程，您可以此为模板。

### 第 1 步：创建服务类

在 `Assets/MapEditor/Scripts/Services/` 目录下创建一个新的 C# 脚本。让这个类继承自 `ServiceBase`。

```csharp
// In Assets/MapEditor/Scripts/Services/AutoSaveService.cs
using MapEditor.Core;
using UnityEngine;

namespace MapEditor.Services
{
    // 如果服务需要每帧更新，可以同时实现 IUpdate 或 ILateUpdate 接口
    public class AutoSaveService : ServiceBase, IUpdate
    {
        private float autoSaveInterval = 300f; // 5 minutes
        private float timeSinceLastSave = 0f;

        // 在此方法中进行服务的初始化设置
        public override void Initialize()
        {
            Debug.Log("AutoSaveService Initialized");
            // 例如，从配置文件加载自动保存间隔
        }

        // 在所有服务都初始化后调用
        public override void Start()
        {
            Debug.Log("AutoSaveService Started");
            // 订阅需要在所有服务启动后才响应的事件
            RegisterEvent<MapDataChangedEvent>(OnMapDataChanged);
        }

        // 如果实现了 IUpdate，此方法会被 MapEditorCore 每帧调用
        public void Update()
        {
            timeSinceLastSave += Time.deltaTime;
            if (timeSinceLastSave >= autoSaveInterval)
            {
                TriggerAutoSave();
                timeSinceLastSave = 0f;
            }
        }

        private void OnMapDataChanged(MapDataChangedEvent evt)
        {
            // 当地图数据发生重要变化时，重置计时器
            timeSinceLastSave = 0f;
        }

        private void TriggerAutoSave()
        {
            Debug.Log("Triggering auto-save...");
            // 发布保存请求事件，由 SaveService 处理
            PublishEvent(new SaveRequestEvent(isAutoSave: true));
        }

        // 在服务销毁时调用，用于清理
        public override void OnDestroy()
        { 
            // 取消事件订阅，释放资源
            UnregisterEvent<MapDataChangedEvent>(OnMapDataChanged);
            Debug.Log("AutoSaveService Destroyed");
        }
    }
}
```

**关键点**:

-   **继承 `ServiceBase`**: 自动获得了对 `core` 和事件系统的访问能力。
-   **实现生命周期方法**: 根据需求在 `Initialize`, `Start`, 和 `OnDestroy` 中编写逻辑。
-   **实现 `IUpdate` / `ILateUpdate` (可选)**: 如果服务需要每帧执行代码，实现这两个接口之一，`MapEditorCore` 会自动在相应的生命周期中调用它们。
-   **服务间通信**: 通过 `PublishEvent` 发布事件，而不是直接调用其他服务的方法。通过 `GetService` 获取对其他服务的引用仅在绝对必要时使用（例如，需要立即从另一个服务获取数据）。

### 第 2 步：注册服务

为了让 `MapEditorCore` 管理您的新服务，您必须在 `MapEditorStarter` 中注册它。

打开 `Assets/MapEditor/Scripts/Manager/MapEditorStarter.cs` 文件，在 `InitializeServices` 方法中，添加一行代码来实例化并注册您的服务。

```csharp
// In MapEditorStarter.cs, inside InitializeServices()
private void InitializeServices(IMapEditorCore editorCore)
{
    // ... 注册其他服务 ...

    editorCore.RegisterService(new SaveService());
    editorCore.RegisterService(new AutoSaveService()); // <-- 添加您的新服务
    editorCore.RegisterService(new PrefabSizeCache());

    Debug.Log("All services registered.");
}
```

**注册顺序**: 服务的注册顺序通常不重要，因为 `Start` 方法确保了所有服务在执行关键逻辑前都已完成 `Initialize`。但是，如果某个服务的 `Initialize` 逻辑依赖于另一个服务，请确保被依赖的服务先被注册。

## 3. 总结

创建一个新服务的流程非常直接：

1.  **创建** 一个继承自 `ServiceBase` 的新类，用于封装特定的业务逻辑。
2.  **实现** `Initialize`, `Start`, 和 `OnDestroy` 等生命周期方法。
3.  如果需要，**实现** `IUpdate` 或 `ILateUpdate` 接口以接收每帧回调。
4.  **使用** `RegisterEvent`, `PublishEvent` 等方法与系统的其他部分进行交互。
5.  在 `MapEditorStarter` 的 `InitializeServices` 方法中 **注册** 新服务的实例。
