using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using MapEditor.Data.Layers;
using MapEditor.Rendering.RenderProxy;
using MapEditor.Services;

namespace MapEditor.Rendering.Layers
{
    /// <summary>
    /// 对象层渲染器，负责管理对象层的渲染代理
    /// </summary>
    public class ObjectLayerRenderer : LayerRenderer
    {
        private ObjectLayer objectLayer;
        private GridService gridService;

        public override void Initialize(IMapLayer layer)
        {
            base.Initialize(layer);
            objectLayer = layer as ObjectLayer;
            
            // 获取GridService引用
            gridService = MapEditorCore.Instance?.GetService<GridService>();
        }

        protected override ChunkRenderProxy CreateProxy(ChunkCoord coord, ChunkBase chunk)
        {
            if (!(chunk is ObjectChunk objectChunk))
            {
                // 使用 MapLayer 提供的公开接口创建 ObjectChunk
                if (LinkedLayer is MapLayer mapLayer)
                {
                    objectChunk = mapLayer.GetOrCreateChunk<ObjectChunk>(coord);
                }
                else
                {
                    // 如果不是 MapLayer，创建一个临时的
                    objectChunk = new ObjectChunk(coord, LinkedLayer.ChunkSize);
                }
            }

            return new ObjectChunkRenderProxy(coord, objectChunk, this);
        }

        protected override void UpdateProxy(ChunkRenderProxy proxy, ChunkBase chunk)
        {
            base.UpdateProxy(proxy, chunk);
            
            if (proxy is ObjectChunkRenderProxy objectProxy && chunk is ObjectChunk objectChunk)
            {
                // 检查版本并重建
                objectProxy.RebuildIfNeeded(objectChunk);
            }
        }

        /// <summary>
        /// 判断世界坐标是否在当前图层范围内（封装基类方法，便于本类调用）。
        /// </summary>
        private bool IsInsideLayer(Vector2 worldPosition)
        {
            return IsWorldPositionInsideLayer(worldPosition);
        }

        /// <summary>
        /// 将给定世界坐标限制在图层可编辑区域内。
        /// </summary>
        private Vector2 ClampToLayerBounds(Vector2 worldPosition)
        {
            if (LinkedLayer is not MapLayer mapLayer) return worldPosition;

            Vector2Int realSizePixel = mapLayer.RealLayerSize;
            float maxX = realSizePixel.x / (float)MapEditorConfig.PixelsPerUnit;
            float maxY = realSizePixel.y / (float)MapEditorConfig.PixelsPerUnit;

            float clampedX = Mathf.Clamp(worldPosition.x, 0f, maxX);
            float clampedY = Mathf.Clamp(worldPosition.y, 0f, maxY);
            return new Vector2(clampedX, clampedY);
        }

        /// <summary>
        /// 在指定位置添加对象实例
        /// </summary>
        public ObjectInstance AddObjectAt(Vector2 worldPosition, string prefabGuid, float rotation = 0f, Vector2? scale = null)
        {
            // 超出地图范围不允许创建
            if (!IsInsideLayer(worldPosition))
            {
                Debug.LogWarning($"AddObjectAt: 位置{worldPosition} 超出图层范围，忽略创建。");
                return null;
            }

            // 计算应该属于哪个Chunk
            ChunkCoord chunkCoord = GetChunkCoordFromWorldPosition(worldPosition);
            
            // 获取或创建ObjectChunk
            ObjectChunk objectChunk = GetOrCreateObjectChunk(chunkCoord);
            
            // 创建对象实例
            ObjectInstance instance = new ObjectInstance(prefabGuid, worldPosition, rotation, scale);
            
            // 添加到chunk
            objectChunk.AddObject(instance);
            
            // ========== 渲染代理处理 BEGIN ==========
            // 尝试获取已存在的代理；如不存在则立即创建
            if (!proxies.TryGetValue(chunkCoord, out ChunkRenderProxy proxy) || !(proxy is ObjectChunkRenderProxy))
            {
                proxy = CreateProxy(chunkCoord, objectChunk);
                if (proxy != null)
                {
                    proxies[chunkCoord] = proxy;
                }
            }
            
            // 此时若代理有效，确保把实例同步到场景
            if (proxy is ObjectChunkRenderProxy objectProxy)
            {
                objectProxy.AddObjectInstance(instance);
                // 计算并更新占用的网格格子
                UpdateInstanceOccupiedCells(instance, objectProxy);
            }
            // ========== 渲染代理处理 END ==========

            return instance;
        }

        /// <summary>
        /// 移除对象
        /// </summary>
        public bool RemoveObject(string instanceId)
        {
            // 找到包含该对象的chunk
            foreach (var kvp in proxies)
            {
                var chunk = GetObjectChunk(kvp.Key);
                if (chunk != null && chunk.RemoveObject(instanceId))
                {
                    // 从渲染代理中移除
                    var proxy = kvp.Value as ObjectChunkRenderProxy;
                    proxy?.RemoveObjectInstance(instanceId);
                    return true;
                }
            }
            return false;
        }
        
        /// <summary>
        /// 移除对象（通过对象实例）
        /// </summary>
        public bool RemoveObject(ObjectInstance instance)
        {
            if (instance == null) return false;
            return RemoveObject(instance.InstanceId);
        }

        /// <summary>
        /// 更新对象实例
        /// </summary>
        public void UpdateObjectInstance(ObjectInstance instance)
        {
            if (instance == null) return;

            // 将位置限制在地图范围内
            instance.Position = ClampToLayerBounds(instance.Position);

            // 计算新的Chunk坐标
            ChunkCoord newChunkCoord = GetChunkCoordFromWorldPosition(instance.Position);
            
            // 查找对象当前所在的Chunk
            ObjectChunk currentChunk = null;
            ChunkCoord currentCoord = default;
            
            foreach (var coord in GetAllChunkCoords())
            {
                var chunk = GetObjectChunk(coord);
                if (chunk != null && chunk.FindObject(instance.InstanceId) != null)
                {
                    currentChunk = chunk;
                    currentCoord = coord;
                    break;
                }
            }

            if (currentChunk == null)
            {
                Debug.LogWarning($"找不到对象实例: {instance.InstanceId}");
                return;
            }

            // 如果需要跨Chunk移动
            if (!currentCoord.Equals(newChunkCoord))
            {
                // 从原Chunk移除
                currentChunk.RemoveObject(instance.InstanceId);
                if (proxies.TryGetValue(currentCoord, out ChunkRenderProxy currentProxy) && currentProxy is ObjectChunkRenderProxy currentObjectProxy)
                {
                    currentObjectProxy.RemoveObjectInstance(instance.InstanceId);
                }

                // 添加到新Chunk
                ObjectChunk newChunk = GetOrCreateObjectChunk(newChunkCoord);
                newChunk.AddObject(instance);
                
                if (proxies.TryGetValue(newChunkCoord, out ChunkRenderProxy newProxy) && newProxy is ObjectChunkRenderProxy newObjectProxy)
                {
                    newObjectProxy.AddObjectInstance(instance);
                    UpdateInstanceOccupiedCells(instance, newObjectProxy);
                }
                else
                {
                    // 创建新的代理
                    var createdProxy = CreateProxy(newChunkCoord, newChunk);
                    if (createdProxy != null)
                    {
                        proxies[newChunkCoord] = createdProxy;
                        if (createdProxy is ObjectChunkRenderProxy createdObjectProxy)
                        {
                            UpdateInstanceOccupiedCells(instance, createdObjectProxy);
                        }
                    }
                }
            }
            else
            {
                // 在同一个Chunk内更新
                if (proxies.TryGetValue(currentCoord, out ChunkRenderProxy proxy) && proxy is ObjectChunkRenderProxy objectProxy)
                {
                    objectProxy.UpdateObjectInstance(instance);
                    UpdateInstanceOccupiedCells(instance, objectProxy);
                }
            }
        }

        /// <summary>
        /// 根据射线检测选择对象
        /// </summary>
        public ObjectInstance SelectObjectAtPosition(Vector2 worldPosition)
        {
            Debug.Log($"ObjectLayerRenderer.SelectObjectAtPosition: 检测位置 {worldPosition}");
            
            // 使用标准的OverlapPoint检测所有碰撞体
            Collider2D hit = Physics2D.OverlapPoint(worldPosition);
            if (hit != null)
            {
                Debug.Log($"检测到碰撞体: {hit.gameObject.name}");
                ObjectIdentifier identifier = hit.GetComponent<ObjectIdentifier>();
                if (identifier != null)
                {
                    // 使用新的选择区域检测方法
                    if (identifier.IsPointInSelectionArea(worldPosition))
                    {
                        Debug.Log($"找到ObjectIdentifier: InstanceId={identifier.InstanceId}, ChunkCoord={identifier.ChunkCoord}");
                        // 验证对象是否属于当前层
                        var chunk = GetObjectChunk(identifier.ChunkCoord);
                        if (chunk != null)
                        {
                            var obj = chunk.FindObject(identifier.InstanceId);
                            if (obj != null)
                            {
                                Debug.Log($"找到对象实例: {obj.GetDisplayName()}");
                            }
                            return obj;
                        }
                        else
                        {
                            Debug.LogWarning($"找不到Chunk: {identifier.ChunkCoord}");
                        }
                    }
                    else
                    {
                        Debug.Log($"点击位置不在对象 {identifier.InstanceId} 的选择区域内");
                    }
                }
                else
                {
                    Debug.LogWarning($"碰撞体 {hit.gameObject.name} 没有ObjectIdentifier组件");
                }
            }
            else
            {
                Debug.Log("未检测到任何碰撞体");
            }
            
            return null;
        }

        /// <summary>
        /// 获取当前层的所有对象实例
        /// </summary>
        public List<ObjectInstance> GetAllObjects()
        {
            List<ObjectInstance> allObjects = new List<ObjectInstance>();
            
            foreach (var coord in GetAllChunkCoords())
            {
                var chunk = GetObjectChunk(coord);
                if (chunk != null)
                {
                    allObjects.AddRange(chunk.Objects);
                }
            }
            
            return allObjects;
        }
        
        /// <summary>
        /// 根据ID获取对象实例
        /// </summary>
        public ObjectInstance GetObjectById(string instanceId)
        {
            foreach (var coord in GetAllChunkCoords())
            {
                var chunk = GetObjectChunk(coord);
                if (chunk != null)
                {
                    var obj = chunk.FindObject(instanceId);
                    if (obj != null)
                    {
                        return obj;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 获取所有Chunk坐标
        /// </summary>
        private IEnumerable<ChunkCoord> GetAllChunkCoords()
        {
            if (LinkedLayer is MapLayer mapLayer)
            {
                return mapLayer.GetAllChunks().Select(c => c.Coord);
            }
            return new List<ChunkCoord>();
        }

        /// <summary>
        /// 获取或创建ObjectChunk
        /// </summary>
        private ObjectChunk GetOrCreateObjectChunk(ChunkCoord coord)
        {
            if (LinkedLayer is MapLayer mapLayer)
            {
                return mapLayer.GetOrCreateChunk<ObjectChunk>(coord);
            }
            return null;
        }

        /// <summary>
        /// 获取ObjectChunk
        /// </summary>
        private ObjectChunk GetObjectChunk(ChunkCoord coord)
        {
            if (LinkedLayer is MapLayer mapLayer)
            {
                var chunks = mapLayer.GetAllChunks();
                var chunk = chunks.FirstOrDefault(c => c.Coord.Equals(coord));
                return chunk as ObjectChunk;
            }
            return null;
        }

        /// <summary>
        /// 根据世界坐标计算Chunk坐标
        /// </summary>
        private ChunkCoord GetChunkCoordFromWorldPosition(Vector2 worldPosition)
        {
            // 将世界坐标转换为像素坐标
            Vector2Int pixelPos = WorldToPixel(worldPosition);
            
            // 计算Chunk坐标
            int chunkX = Mathf.FloorToInt(pixelPos.x / (float)LinkedLayer.ChunkSize);
            int chunkY = Mathf.FloorToInt(pixelPos.y / (float)LinkedLayer.ChunkSize);
            
            return new ChunkCoord(chunkX, chunkY);
        }

        /// <summary>
        /// 根据已反序列化的 Chunk 数据重新创建渲染代理(用于加载地图)。
        /// </summary>
        public void BuildProxiesFromSavedChunks(MapLayer mapLayer)
        {
            if (mapLayer == null) return;

            foreach (var chunk in mapLayer.GetAllChunks())
            {
                var coord = chunk.Coord;
                if (!proxies.TryGetValue(coord, out var proxy))
                {
                    proxy = CreateProxy(coord, chunk);
                    if (proxy != null)
                    {
                        proxies[coord] = proxy;
                        // 立即构建渲染内容
                        UpdateProxy(proxy, chunk);
                        
                        // 重新计算所有对象的占用格子（防止预制体变更导致的不一致）
                        if (proxy is ObjectChunkRenderProxy objectProxy && chunk is ObjectChunk objectChunk)
                        {
                            foreach (var instance in objectChunk.Objects)
                            {
                                UpdateInstanceOccupiedCells(instance, objectProxy);
                            }
                        }
                    }
                }
                else
                {
                    // 确保代理内容与数据同步
                    UpdateProxy(proxy, chunk);
                    
                    // 重新计算所有对象的占用格子
                    if (proxy is ObjectChunkRenderProxy objectProxy && chunk is ObjectChunk objectChunk)
                    {
                        foreach (var instance in objectChunk.Objects)
                        {
                            UpdateInstanceOccupiedCells(instance, objectProxy);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 尝试通过实例ID获取对应的GameObject。
        /// </summary>
        /// <param name="instanceId">对象实例ID</param>
        /// <param name="gameObject">结果GameObject</param>
        /// <returns>找到返回true</returns>
        public bool TryGetGameObject(string instanceId, out GameObject gameObject)
        {
            gameObject = null;
            foreach (var proxy in proxies.Values)
            {
                if (proxy is ObjectChunkRenderProxy objProxy)
                {
                    gameObject = objProxy.GetGameObject(instanceId);
                    if (gameObject != null)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 标记对象所在的chunk为脏
        /// </summary>
        public void MarkObjectChunkDirty(ObjectInstance obj)
        {
            if (obj == null || LinkedLayer == null) return;
            
            // 计算对象所在的chunk坐标
            var chunkCoord = new ChunkCoord(
                Mathf.FloorToInt(obj.Position.x / LinkedLayer.ChunkSize),
                Mathf.FloorToInt(obj.Position.y / LinkedLayer.ChunkSize)
            );
            
            // 标记chunk为脏
            if (LinkedLayer is MapLayer mapLayer)
            {
                mapLayer.MarkChunkDirty(chunkCoord);
            }
        }

        /// <summary>
        /// 更新对象实例占用的网格格子
        /// </summary>
        /// <param name="instance">对象实例</param>
        /// <param name="objectProxy">对象渲染代理</param>
        private void UpdateInstanceOccupiedCells(ObjectInstance instance, ObjectChunkRenderProxy objectProxy)
        {
            if (gridService == null)
            {
                Debug.LogWarning("ObjectLayerRenderer: GridService未初始化，无法计算占用格子");
                return;
            }

            // 尝试获取GameObject
            if (objectProxy.TryGetGameObject(instance.InstanceId, out GameObject gameObject))
            {
                var identifier = gameObject.GetComponent<ObjectIdentifier>();
                if (identifier != null)
                {
                    // 使用新的占用格子计算方法
                    var occupiedCells = identifier.GetOccupiedCells(gridService);
                    instance.OccupiedCells = occupiedCells;
                    
                    Debug.Log($"对象 {instance.GetDisplayName()} 占用网格格子: {occupiedCells.Count} 个");
                }
                else
                {
                    Debug.LogWarning($"对象 {instance.GetDisplayName()} 没有ObjectIdentifier组件");
                    instance.OccupiedCells = new List<Vector2Int>();
                }
            }
            else
            {
                Debug.LogWarning($"无法获取对象 {instance.GetDisplayName()} 的GameObject");
                instance.OccupiedCells = new List<Vector2Int>();
            }
        }
    }
} 