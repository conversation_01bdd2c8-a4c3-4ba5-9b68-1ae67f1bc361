using MapEditor.Core;
using UnityEngine;
using MapEditor.Manager;
using System.Collections.Generic;
using MapEditor.Event;

namespace MapEditor.Services
{
    /// <summary>
    /// 图层渲染服务，通过事件驱动与 SceneRenderManager 协作，负责业务逻辑和渲染器缓存管理
    /// </summary>
    public class LayerRenderService : ServiceBase, ILayerRenderService
    {
        // 渲染器缓存，通过事件异步填充
        private readonly Dictionary<string, LayerRenderer> rendererCache = new();

        public override void Initialize()
        {
            // 订阅渲染器注册事件，用于维护缓存
            RegisterEvent<LayerRendererRegisteredEvent>(OnRendererRegistered);
        }

        public override void Start()
        {
            // 注册相关事件
            RegisterEvent<MapDataChangedEvent>(OnMapDataChanged);
        }

        /// <summary>
        /// 为图层创建渲染器 - 通过事件触发
        /// </summary>
        /// <param name="layer">图层对象</param>
        /// <returns>创建的渲染器</returns>
        public ILayerRenderer CreateRenderer(IMapLayer layer)
        {
            if (layer == null)
            {
                Debug.LogError("Cannot create renderer for null layer");
                return null;
            }

            // 发送创建请求事件，SceneRenderManager 会响应
            PublishEvent(new LayerRenderRequestEvent
            {
                Action = LayerRenderAction.Create,
                Layer = layer
            });

            Debug.Log($"已请求为图层 '{layer.LayerName}' 创建渲染器");
            // 返回当前缓存的渲染器（如果有的话），否则返回null
            return GetRenderer(layer);
        }

        /// <summary>
        /// 移除图层渲染器 - 通过事件触发
        /// </summary>
        /// <param name="layer">图层对象</param>
        public void RemoveRenderer(IMapLayer layer)
        {
            if (layer == null)
            {
                Debug.LogError("Cannot remove renderer for null layer");
                return;
            }

            // 发送移除请求事件
            PublishEvent(new LayerRenderRequestEvent
            {
                Action = LayerRenderAction.Remove,
                Layer = layer
            });
            
            // 从缓存中移除
            rendererCache.Remove(layer.LayerId);
            
            Debug.Log($"已移除图层 '{layer.LayerName}' 的渲染器");
        }

        /// <summary>
        /// 设置图层可见性 - 通过事件触发
        /// </summary>
        /// <param name="layer">图层对象</param>
        /// <param name="visible">是否可见</param>
        public void SetRendererVisibility(IMapLayer layer, bool visible)
        {
            if (layer == null)
            {
                Debug.LogError("Cannot set visibility for null layer");
                return;
            }

            PublishEvent(new LayerRenderRequestEvent
            {
                Action = LayerRenderAction.UpdateVisibility,
                Layer = layer,
                Visibility = visible
            });
            
            Debug.Log($"设置图层 '{layer.LayerName}' 可见性为: {visible}");
        }

        /// <summary>
        /// 获取图层渲染器 - 从缓存返回
        /// </summary>
        /// <param name="layer">图层对象</param>
        /// <returns>图层渲染器</returns>
        public ILayerRenderer GetRenderer(IMapLayer layer)
        {
            if (layer == null)
            {
                Debug.LogError("Cannot get renderer for null layer");
                return null;
            }

            rendererCache.TryGetValue(layer.LayerId, out var renderer);
            return renderer;
        }

        /// <summary>
        /// 清理所有渲染器 - 通过事件触发
        /// </summary>
        public void ClearAll()
        {
            PublishEvent(new LayerRenderRequestEvent
            {
                Action = LayerRenderAction.ClearAll
            });
            
            // 清空缓存
            rendererCache.Clear();
            Debug.Log("已请求清理所有图层渲染器");
        }

        /// <summary>
        /// 更新所有渲染器 - 通过事件触发
        /// </summary>
        public void UpdateAll()
        {
            PublishEvent(new LayerRenderRequestEvent
            {
                Action = LayerRenderAction.UpdateAll
            });
            
            Debug.Log("已请求更新所有图层渲染器");
        }

        /// <summary>
        /// 移除指定ID的图层渲染器 - 通过事件触发
        /// </summary>
        public void RemoveRenderer(string layerId)
        {
            // 尝试从缓存获取图层对象
            if (rendererCache.TryGetValue(layerId, out var renderer))
            {
                RemoveRenderer(renderer.LinkedLayer);
            }
            else
            {
                // 即使缓存中没有，也要发送移除事件
                rendererCache.Remove(layerId);
                Debug.Log($"已请求移除图层ID '{layerId}' 的渲染器");
            }
        }

        /// <summary>
        /// 获取指定ID的图层渲染器 - 从缓存返回
        /// </summary>
        public ILayerRenderer GetRenderer(string layerId)
        {
            rendererCache.TryGetValue(layerId, out var renderer);
            return renderer;
        }

        /// <summary>
        /// 设置指定ID图层的可见性 - 通过事件触发
        /// </summary>
        public void SetRendererVisibility(string layerId, bool visible)
        {
            if (rendererCache.TryGetValue(layerId, out var renderer))
            {
                SetRendererVisibility(renderer.LinkedLayer, visible);
            }
        }

        /// <summary>
        /// 获取指定层级的容器Transform - 委托给SceneRenderManager
        /// </summary>
        public Transform GetLayerContainer(RenderLayer layer)
        {
            // 这个方法需要立即返回结果，暂时通过SceneRenderer获取
            var sceneRenderer = core.SceneRenderer as SceneRenderManager;
            return sceneRenderer?.GetLayerContainer(layer);
        }

        /// <summary>
        /// 获取指定层级的排序顺序 - 委托给SceneRenderManager
        /// </summary>
        public int GetLayerSortingOrder(RenderLayer layer, int sublayerOffset = 0)
        {
            // 这个方法需要立即返回结果，暂时通过SceneRenderer获取
            var sceneRenderer = core.SceneRenderer as SceneRenderManager;
            return sceneRenderer?.GetLayerSortingOrder(layer, sublayerOffset) ?? 0;
        }

        // ===== 新增: 获取全部渲染器 =====
        public IEnumerable<ILayerRenderer> GetAllRenderers()
        {
            return rendererCache.Values;
        }

        /// <summary>
        /// 渲染器注册事件处理 - 更新缓存
        /// </summary>
        private void OnRendererRegistered(LayerRendererRegisteredEvent evt)
        {
            if (evt.Layer != null && evt.Renderer != null)
            {
                rendererCache[evt.Layer.LayerId] = evt.Renderer;
                Debug.Log($"图层 '{evt.Layer.LayerName}' 的渲染器已注册到缓存");
            }
        }

        /// <summary>
        /// 地图数据变更事件处理
        /// </summary>
        /// <param name="evt">事件数据</param>
        private void OnMapDataChanged(MapDataChangedEvent evt)
        {
            // 根据数据变更类型执行相应的渲染更新
            switch (evt.ChangeType)
            {
                case MapDataChangeType.TerrainModified:
                    Debug.Log($"地形数据已修改，图层: {evt.LayerId}");
                    // 可以在这里触发特定的地形渲染更新
                    break;
                
                case MapDataChangeType.ObjectAdded:
                case MapDataChangeType.ObjectRemoved:
                case MapDataChangeType.ObjectModified:
                    Debug.Log($"对象数据已修改，图层: {evt.LayerId}");
                    // 可以在这里触发特定的对象渲染更新
                    break;
                
                default:
                    Debug.Log($"未知的数据变更类型: {evt.ChangeType}");
                    break;
            }
        }
    }
} 