using UnityEngine;

namespace MapEditor.Data
{
    /// <summary>
    /// 独立地表纹理资源，每张纹理一个 ScriptableObject。
    /// </summary>
    [CreateAssetMenu(fileName = "GroundTexture", menuName = "MapEditor/Ground Texture", order = 12)]
    public class GroundTextureSO : ScriptableObject
    {
        [Tooltip("与贴图索引对应的全局唯一编号，必须 >= 0，不可重复")] public int textureIndex = -1;

        [Tooltip("实际用于渲染的地表纹理")] public Texture2D texture;

        [Tooltip("UV 平铺系数")] public Vector2 uvScale = Vector2.one;

        [Tooltip("UI 显示名")] public string displayName;

        private void OnValidate()
        {
            if (texture != null)
            {
                texture.filterMode = FilterMode.Bilinear;
            }
        }
    }
} 