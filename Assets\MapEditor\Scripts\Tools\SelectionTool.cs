using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Rendering;
using UnityEngine.UIElements;
using MapEditor.Tools.Selection;
using MapEditor.UI;
using MapEditor.Event;

namespace MapEditor.Tools
{
    /// <summary>
    /// 通用选择工具，支持不同图层类型注册自己的选择行为和UI
    /// </summary>
    public class SelectionTool : MapToolBase
    {
        private IEventSystem eventService;
        private readonly Dictionary<LayerType, ISelectionBehavior> selectionBehaviors = new();
        private readonly Dictionary<LayerType, ISelectionUI> selectionUIs = new();
        
        private ISelectionBehavior currentBehavior;
        private ISelectionUI currentUI;
        private LayerType? currentLayerType;
        
        // 选择状态
        private SelectionState currentState = SelectionState.None;
        private Vector2 selectionStartPos;
        private Vector2 lastMousePosition;
        private List<ISelectable> selectedObjects = new List<ISelectable>();
        
        // 选择框
        private VisualSelectionBox selectionBox;
        
        // 高亮框，用于显示已选对象的包围盒
        private VisualSelectionBox highlightBox;
        
        // 延迟获取EventService的属性
        private IEventSystem EventService
        {
            get
            {
                if (eventService == null)
                {
                    eventService = editorCore.EventSystem;
                }
                return eventService;
            }
        }
        
        /// <summary>
        /// 选择工具的状态
        /// </summary>
        public enum SelectionState
        {
            None,
            Selecting,
            Selected,
            Dragging,
            Scaling,
            Rotating
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SelectionTool(IMapEditorCore editorCore)
            : base("SelectionTool", "选择工具", editorCore)
        {
            Debug.Log($"SelectionTool 构造函数，实例哈希码: {this.GetHashCode()}");
        }

        /// <summary>
        /// 注册图层特定的选择行为
        /// </summary>
        public void RegisterSelectionBehavior(ISelectionBehavior behavior)
        {
            if (behavior == null) return;
            
            selectionBehaviors[behavior.SupportedLayerType] = behavior;
            Debug.Log($"SelectionTool({this.GetHashCode()}) 注册了 {behavior.SupportedLayerType} 图层的选择行为，当前注册数量: {selectionBehaviors.Count}");
        }

        /// <summary>
        /// 注册图层特定的选择UI
        /// </summary>
        public void RegisterSelectionUI(ISelectionUI ui)
        {
            if (ui == null) return;
            
            selectionUIs[ui.SupportedLayerType] = ui;
            Debug.Log($"注册了 {ui.SupportedLayerType} 图层的选择UI");
        }

        /// <summary>
        /// 获取当前活动的选择行为
        /// </summary>
        public ISelectionBehavior GetCurrentBehavior()
        {
            return currentBehavior;
        }

        /// <summary>
        /// 获取当前活动的选择UI
        /// </summary>
        public ISelectionUI GetCurrentUI()
        {
            return currentUI;
        }

        /// <summary>
        /// 工具激活时调用
        /// </summary>
        public override void OnActivate()
        {
            base.OnActivate();
            
            // 创建选择框
            if (selectionBox == null)
            {
                var boxObj = new GameObject("SelectionBox");
                selectionBox = boxObj.AddComponent<VisualSelectionBox>();
                // 选择框用于框选，不显示控制手柄
                selectionBox.SetShowHandles(false);
            }
            
            // 创建高亮框（带控制手柄，可后续扩展）
            if (highlightBox == null)
            {
                var highlightObj = new GameObject("HighlightBox");
                highlightBox = highlightObj.AddComponent<VisualSelectionBox>();
                highlightBox.SetShowHandles(true);
                highlightBox.SetVisible(false);
            }
            
            // EventService会在第一次使用时自动获取
            if (EventService != null)
            {
                EventService.Subscribe<ActiveLayerChangedEvent>(OnActiveLayerChanged);
                EventService.Subscribe<RequestSelectObjectEvent>(OnRequestSelectObject);
                EventService.Subscribe<DeleteKeyEvent>(OnDeleteKeyPressed);
            }
            
            // 检查当前活动图层
            var activeLayer = editorCore.LayerStore?.ActiveLayer;
            if (activeLayer != null)
            {
                UpdateForLayerType(activeLayer.Type);
            }
            
            PublishToolStatusChanged("选择工具已激活");
        }

        /// <summary>
        /// 工具停用时调用
        /// </summary>
        public override void OnDeactivate()
        {
            base.OnDeactivate();
            
            // 取消订阅事件
            if (EventService != null)
            {
                EventService.Unsubscribe<ActiveLayerChangedEvent>(OnActiveLayerChanged);
                EventService.Unsubscribe<RequestSelectObjectEvent>(OnRequestSelectObject);
                EventService.Unsubscribe<DeleteKeyEvent>(OnDeleteKeyPressed);
            }
            
            // 清理当前行为
            currentBehavior = null;
            
            // 清理UI
            currentUI?.ClearSelectionPanel();
            currentUI = null;
            
            currentLayerType = null;
            
            // 清除选择
            ClearSelection();
            
            // 隐藏高亮框
            if (highlightBox != null)
            {
                highlightBox.SetVisible(false);
            }
            
            PublishToolStatusChanged("已清除选择");
        }

        /// <summary>
        /// 处理图层切换事件
        /// </summary>
        private void OnActiveLayerChanged(ActiveLayerChangedEvent evt)
        {
            UpdateForLayerType(evt.NewLayer?.Type ?? LayerType.Ground);
        }

        /// <summary>
        /// 根据图层类型更新当前行为和UI
        /// </summary>
        private void UpdateForLayerType(LayerType layerType)
        {
            Debug.Log($"SelectionTool({this.GetHashCode()}) UpdateForLayerType: {layerType}, 已注册行为数量: {selectionBehaviors.Count}");
            
            // 如果图层类型没有变化，不需要更新
            if (currentLayerType == layerType)
            {
                return;
            }

            // 清理旧的UI
            currentUI?.ClearSelectionPanel();

            // 设置新的行为
            if (selectionBehaviors.TryGetValue(layerType, out var newBehavior))
            {
                currentBehavior = newBehavior;
                Debug.Log($"切换到 {layerType} 图层的选择行为");
            }
            else
            {
                currentBehavior = null;
                Debug.LogWarning($"SelectionTool({this.GetHashCode()}) 没有为 {layerType} 图层注册选择行为。已注册的图层类型: {string.Join(", ", selectionBehaviors.Keys)}");
            }

            // 设置新的UI
            if (selectionUIs.TryGetValue(layerType, out var newUI))
            {
                currentUI = newUI;
                // 设置UI管理器
                var uiManager = UIManager.Instance as IUIManager;
                if (uiManager != null)
                {
                    currentUI.SetUIManager(uiManager);
                }
                Debug.Log($"切换到 {layerType} 图层的选择UI");
            }
            else
            {
                currentUI = null;
                Debug.LogWarning($"没有为 {layerType} 图层注册选择UI");
            }

            currentLayerType = layerType;
            
            // 清除当前选择
            ClearSelection();

            // 当切换到支持的UI时立即创建空面板，避免UI缺失
            if (currentUI != null)
            {
                var container = GetSelectionUIContainer();
                if (container != null)
                {
                    currentUI.CreateSelectionPanel(container, new List<ISelectable>());
                }
            }
        }

        /// <summary>
        /// 处理请求选择对象事件
        /// </summary>
        private void OnRequestSelectObject(RequestSelectObjectEvent evt)
        {
            if (currentBehavior == null || string.IsNullOrEmpty(evt.ObjectId))
                return;
                
            var activeLayer = editorCore.LayerStore?.ActiveLayer;
            if (activeLayer == null || activeLayer.Type != LayerType.Object)
                return;
                
            // 获取对象
            var objectService = editorCore.GetService<ObjectService>();
            var objects = objectService?.GetAllObjectsInCurrentLayer();
            var targetObject = objects?.FirstOrDefault(obj => obj.InstanceId == evt.ObjectId);
            
            if (targetObject != null)
            {
                // 转换为ISelectable
                var selectable = new SelectableObjectInstance(targetObject, activeLayer.LayerId, editorCore);
                
                // 设置选择
                var selectables = new List<ISelectable> { selectable };
                SetSelection(selectables, evt.MultiSelect);
            }
        }

        /// <summary>
        /// 处理场景输入
        /// </summary>
        public override void OnSceneInput(InputContext context)
        {
            if (currentBehavior == null)
            {
                return;
            }

            var activeLayer = editorCore.LayerStore?.ActiveLayer;
            if (activeLayer == null)
            {
                return;
            }
            
            // 处理鼠标输入
            if (context.IsLeftMouseDown)
            {
                switch (currentState)
                {
                    case SelectionState.None:
                        // 开始选择
                        HandleSelectionStart(context.WorldPosition, context.IsShiftDown);
                        break;
                        
                    case SelectionState.Selecting:
                        // 更新框选
                        UpdateRectSelection(context.WorldPosition);
                        break;
                        
                    case SelectionState.Selected:
                        // 先检查缩放手柄
                        int handleIdx = highlightBox != null ? highlightBox.GetHandleAtPosition(context.WorldPosition) : -1;
                        if (handleIdx >= 0 && currentBehavior.SupportsScaling)
                        {
                            currentBehavior.StartScale(selectedObjects, handleIdx, context.WorldPosition);
                            currentState = SelectionState.Scaling;
                        }
                        // 检查旋转手柄
                        else if (highlightBox != null && highlightBox.GetRotationHandleAtPosition(context.WorldPosition) && currentBehavior.SupportsRotation)
                        {
                            currentBehavior.StartRotate(selectedObjects, context.WorldPosition);
                            currentState = SelectionState.Rotating;
                        }
                        else if (IsClickOnSelected(context.WorldPosition))
                        {
                            // 点击在对象上 -> 拖拽
                            if (currentBehavior.SupportsDragging)
                            {
                                currentBehavior.StartDrag(selectedObjects, context.WorldPosition);
                                currentState = SelectionState.Dragging;
                            }
                        }
                        else
                        {
                            // 开始新的选择
                            HandleSelectionStart(context.WorldPosition, context.IsShiftDown);
                        }
                        break;
                        
                    case SelectionState.Dragging:
                        // 更新拖拽
                        currentBehavior.UpdateDrag(context.WorldPosition);
                        // 拖拽过程中实时更新高亮框
                        UpdateHighlightBox();
                        break;
                    case SelectionState.Scaling:
                        currentBehavior.UpdateScale(context.WorldPosition);
                        UpdateHighlightBox();
                        break;
                    case SelectionState.Rotating:
                        currentBehavior.UpdateRotate(context.WorldPosition);
                        UpdateHighlightBox();
                        break;
                }
                
                lastMousePosition = context.WorldPosition;
            }
            else
            {
                // 鼠标释放
                switch (currentState)
                {
                    case SelectionState.Selecting:
                        // 完成选择
                        CompleteSelection(context.IsShiftDown);
                        break;
                        
                    case SelectionState.Dragging:
                        // 结束拖拽
                        currentBehavior.EndDrag();
                        currentState = SelectionState.Selected;
                        break;
                    case SelectionState.Scaling:
                        currentBehavior.EndScale();
                        currentState = SelectionState.Selected;
                        break;
                    case SelectionState.Rotating:
                        currentBehavior.EndRotate();
                        currentState = SelectionState.Selected;
                        break;
                }
            }
        }

        /// <summary>
        /// 更新预览
        /// </summary>
        public override void UpdatePreview()
        {
            // 选择工具通常不需要预览
        }

        /// <summary>
        /// 开始选择
        /// </summary>
        private void HandleSelectionStart(Vector2 worldPosition, bool isMultiSelect)
        {
            selectionStartPos = worldPosition;
            var activeLayer = editorCore.LayerStore?.ActiveLayer;
            
            // 尝试点选
            var clickedObjects = currentBehavior.HandlePointSelection(worldPosition, isMultiSelect, activeLayer);
            
            if (clickedObjects.Count > 0)
            {
                // 点击选中了对象
                SetSelection(clickedObjects, isMultiSelect);
                currentState = SelectionState.Selected;
            }
            else
            {
                // 开始框选
                currentState = SelectionState.Selecting;
                selectionBox?.SetVisible(true);
            }
        }

        /// <summary>
        /// 更新框选
        /// </summary>
        private void UpdateRectSelection(Vector2 currentPosition)
        {
            // 计算选择框
            var rect = GetSelectionRect(selectionStartPos, currentPosition);
            
            // 更新选择框显示
            selectionBox?.SetRect(rect);
        }

        /// <summary>
        /// 完成选择
        /// </summary>
        private void CompleteSelection(bool isMultiSelect)
        {
            var rect = GetSelectionRect(selectionStartPos, lastMousePosition);
            var activeLayer = editorCore.LayerStore?.ActiveLayer;
            
            // 执行框选
            var selectedInRect = currentBehavior.HandleRectSelection(rect, isMultiSelect, activeLayer);
            
            // 设置选择
            SetSelection(selectedInRect, isMultiSelect);
            
            // 隐藏选择框
            selectionBox?.SetVisible(false);
            
            currentState = selectedObjects.Count > 0 ? SelectionState.Selected : SelectionState.None;
        }

        /// <summary>
        /// 设置选中的对象
        /// </summary>
        private void SetSelection(List<ISelectable> objects, bool isMultiSelect)
        {
            if (!isMultiSelect)
            {
                selectedObjects.Clear();
            }
            
            selectedObjects.AddRange(objects);
            
            // 确保UI已创建
            if (currentUI != null)
            {
                var container = GetSelectionUIContainer();
                if (container != null)
                {
                    if (objects.Count > 0 || container.childCount == 0)
                    {
                        // 重新创建面板以确保容器已绑定
                        currentUI.CreateSelectionPanel(container, selectedObjects);
                    }
                    else
                    {
                        currentUI.UpdateSelectionPanel(selectedObjects);
                    }
                }
            }
            
            // 更新高亮框
            UpdateHighlightBox();
            
            // 发布选择变化事件
            PublishSelectionChanged();
        }

        /// <summary>
        /// 清除选择
        /// </summary>
        private void ClearSelection()
        {
            selectedObjects.Clear();
            currentState = SelectionState.None;
            
            // 隐藏选择框
            selectionBox?.SetVisible(false);
            
            // 清理UI
            currentUI?.ClearSelectionPanel();
            
            // 隐藏高亮框
            if (highlightBox != null)
            {
                highlightBox.SetVisible(false);
            }
            
            // 发布选择变化事件
            PublishSelectionChanged();
        }

        /// <summary>
        /// 发布选择变化事件
        /// </summary>
        private void PublishSelectionChanged()
        {
            if (EventService != null)
            {
                var evt = new SelectionChangedEvent
                {
                    SourceToolId = this.ToolId
                };
                
                // 将ISelectable转换为GameObject（如果可能）
                var gameObjects = new List<GameObject>();
                var objectInstances = new List<Data.Chunks.ObjectInstance>();
                
                foreach (var selectable in selectedObjects)
                {
                    if (selectable is MonoBehaviour mb)
                    {
                        gameObjects.Add(mb.gameObject);
                    }
                    else if (selectable is SelectableObjectInstance soi)
                    {
                        // 添加ObjectInstance到事件中
                        objectInstances.Add(soi.ObjectInstance);
                    }
                }
                
                evt.SelectedObjects = gameObjects;
                evt.SelectedObjectInstances = objectInstances;
                
                EventService.Publish(evt);
            }
        }

        /// <summary>
        /// 检查是否点击在选中的对象上
        /// </summary>
        private bool IsClickOnSelected(Vector2 worldPosition)
        {
            foreach (var obj in selectedObjects)
            {
                var bounds = obj.GetBounds();
                if (bounds.Contains(worldPosition))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取选择矩形
        /// </summary>
        private Rect GetSelectionRect(Vector2 start, Vector2 end)
        {
            var min = Vector2.Min(start, end);
            var max = Vector2.Max(start, end);
            return new Rect(min, max - min);
        }

        /// <summary>
        /// 更新已选对象的高亮框
        /// </summary>
        private void UpdateHighlightBox()
        {
            if (highlightBox == null) return;

            if (selectedObjects == null || selectedObjects.Count == 0)
            {
                highlightBox.SetVisible(false);
                return;
            }

            Rect boundingRect = new Rect();
            bool first = true;
            foreach (var obj in selectedObjects)
            {
                Rect r = obj.GetBounds();
                if (first)
                {
                    boundingRect = r;
                    first = false;
                }
                else
                {
                    float xMin = Mathf.Min(boundingRect.xMin, r.xMin);
                    float yMin = Mathf.Min(boundingRect.yMin, r.yMin);
                    float xMax = Mathf.Max(boundingRect.xMax, r.xMax);
                    float yMax = Mathf.Max(boundingRect.yMax, r.yMax);
                    boundingRect = new Rect(xMin, yMin, xMax - xMin, yMax - yMin);
                }
            }

            highlightBox.SetRect(boundingRect);
            highlightBox.SetVisible(true);
        }

        /// <summary>
        /// 获取用于承载选择UI的容器
        /// </summary>
        private VisualElement GetSelectionUIContainer()
        {
            var uiManager = UIManager.Instance as IUIManager;
            if (uiManager?.Root == null) return null;

            // 尝试获取主面板属性容器
            var scroll = uiManager.Root.Q<ScrollView>("PropertiesContainer");
            if (scroll != null)
            {
                return scroll.contentContainer;
            }

            return uiManager.Root;
        }

        /// <summary>
        /// 处理Delete键事件
        /// </summary>
        private void OnDeleteKeyPressed(DeleteKeyEvent evt)
        {
            if (currentBehavior == null || !currentBehavior.SupportsDeletion || selectedObjects.Count == 0)
                return;
                
            currentBehavior.DeleteSelected(selectedObjects);
            ClearSelection();
            PublishToolStatusChanged("已删除选中的对象");
        }
    }
} 