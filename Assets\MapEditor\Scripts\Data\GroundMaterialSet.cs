using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Data
{
    /// <summary>
    /// 地表材质集合，最多支持 7 张纹理（第8通道用于coverage）。通过 Resources.Load(&quot;GroundMaterials/xxx&quot;) 获取。
    /// </summary>
    [CreateAssetMenu(fileName = "GroundMaterialSet", menuName = "MapEditor/Ground Material Set", order = 10)]
    public class GroundMaterialSet : ScriptableObject
    {
       public List<Texture2D> surfaceTextures = new List<Texture2D>();

        [Tooltip("每张纹理的平铺系数(缺省为 1,1)")] public Vector2[] uvScales = new Vector2[7];

        [Tooltip("UI 显示名称")] public string[] displayNames = new string[7];

        private void OnValidate()
        {
 
        }
    }
} 