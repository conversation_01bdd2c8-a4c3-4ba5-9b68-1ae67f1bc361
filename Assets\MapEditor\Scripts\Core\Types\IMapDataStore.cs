using System.Collections.Generic;

namespace MapEditor.Core
{
    /// <summary>
    /// 地图数据管理器接口，负责地图数据的加载、保存和修改
    /// </summary>
    public interface IMapDataStore
    {
        /// <summary>
        /// 当前加载的地图数据
        /// </summary>
        IMapData CurrentMap { get; }
        
        /// <summary>
        /// 当前地图所在目录(绝对路径)。若尚未加载/创建地图则为空字符串。
        /// </summary>
        string CurrentMapDirectory { get; }
        
        /// <summary>
        /// 创建新地图（指定目录与 ChunkSize）
        /// </summary>
        IMapData CreateNewMap(string mapName, UnityEngine.Vector2Int mapSize, int chunkSize, string directoryPath);
        
        /// <summary>
        /// 加载地图
        /// </summary>
        IMapData LoadMap(string mapId);
        
        /// <summary>
        /// 通过完整文件路径加载地图。
        /// </summary>
        IMapData LoadMapFromFile(string filePath);
        
        /// <summary>
        /// 保存当前地图
        /// </summary>
        void SaveMap();
        
        /// <summary>
        /// 另存为新地图
        /// </summary>
        void SaveMapAs(string newMapName);
        
        /// <summary>
        /// 自动保存当前地图（由 AutoSaveService 调用）
        /// </summary>
        void SaveAutoBackup();
        
        /// <summary>
        /// 关闭当前地图
        /// </summary>
        void CloseMap();
        
        /// <summary>
        /// 地图是否已修改（有未保存的更改）
        /// </summary>
        bool HasUnsavedChanges();
        
        /// <summary>
        /// 获取本地已保存的所有地图信息（只读取元数据，不加载进内存）。
        /// </summary>
        IEnumerable<MapInfo> ListSavedMaps();
    }
} 