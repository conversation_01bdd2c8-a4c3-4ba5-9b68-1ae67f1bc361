<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:VisualElement name="GridSettingsContainer" class="panel-container">
        <ui:Label text="网格设置" display-tooltip-when-elided="true" class="panel-title" />
        
        <ui:VisualElement name="SettingsContent" class="settings-content">
            <!-- 网格颜色设置 -->
            <ui:VisualElement name="GridColorRow" class="setting-row">
                <ui:Label text="网格颜色" class="setting-label" />
                <ui:VisualElement name="GridColorField" class="color-field" />
            </ui:VisualElement>
            
            <!-- 主网格颜色设置 -->
            <ui:VisualElement name="MajorGridColorRow" class="setting-row">
                <ui:Label text="主网格颜色" class="setting-label" />
                <ui:VisualElement name="MajorGridColorField" class="color-field" />
            </ui:VisualElement>
            
            <!-- 普通网格间隔设置 -->
            <ui:VisualElement name="GridIntervalRow" class="setting-row">
                <ui:Label text="网格间隔" class="setting-label" />
                <ui:IntegerField name="GridIntervalField" class="integer-field" value="16" />
            </ui:VisualElement>
            
            <!-- 线宽设置 -->
            <ui:VisualElement name="LineWidthRow" class="setting-row">
                <ui:Label text="线宽" class="setting-label" />
                <ui:FloatField name="LineWidthField" class="float-field" value="0.05" />
            </ui:VisualElement>
            
            <!-- 主线宽设置 -->
            <ui:VisualElement name="MajorLineWidthRow" class="setting-row">
                <ui:Label text="主线宽" class="setting-label" />
                <ui:FloatField name="MajorLineWidthField" class="float-field" value="0.1" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML> 