using UnityEngine;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Rendering.Layers;
using MapEditor.Event;
using System.Collections.Generic;

namespace MapEditor.Tools
{
    /// <summary>
    /// 曲线绘制工具，用于创建和编辑平滑曲线并进行地形绘制
    /// </summary>
    public class CurveTool : MapToolBase
    {
        // 曲线数据
        private List<Vector2> controlPoints = new List<Vector2>();
        private List<CurveSegment> curveSegments = new List<CurveSegment>();
        private bool isEditing = false;
        
        // 绘制参数
        private CurveStrokeSettings strokeSettings;
        
        // 子系统
        private ControlPointManager controlPointManager;
        private CurveGenerator curveGenerator;
        private CurvePreview curvePreview;
        
        // 编辑状态
        private CurveEditState currentState = CurveEditState.Idle;
        private int selectedControlPointIndex = -1;
        private bool isDraggingPoint = false;
        
        // 服务引用
        private MapService mapService;
        
        /// <summary>
        /// 获取当前曲线设置
        /// </summary>
        public CurveStrokeSettings CurrentSettings => strokeSettings;
        
        /// <summary>
        /// 获取当前控制点列表
        /// </summary>
        public IReadOnlyList<Vector2> ControlPoints => controlPoints;
        
        /// <summary>
        /// 获取当前曲线段列表
        /// </summary>
        public IReadOnlyList<CurveSegment> CurveSegments => curveSegments;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="editorCore">编辑器核心引用</param>
        public CurveTool(IMapEditorCore editorCore) 
            : base("CurveTool", "曲线工具", editorCore, new[] { LayerType.Ground })
        {
            mapService = editorCore.GetService<MapService>();
            
            // 初始化默认设置
            strokeSettings = new CurveStrokeSettings(
                width: 2.0f,
                edgeSoftness: 0.5f,
                materialIndex: 0,
                strength: 1.0f,
                curveType: CurveType.CatmullRom,
                resolution: 10.0f,
                shape: null,
                hardness: 1.0f
            );
            
            // 初始化子系统
            controlPointManager = new ControlPointManager(this);
            curveGenerator = new CurveGenerator();
            curvePreview = new CurvePreview();
        }
        
        /// <summary>
        /// 工具激活时初始化
        /// </summary>
        public override void OnActivate()
        {
            base.OnActivate();
            
            // 初始化预览系统
            curvePreview.Initialize(editorCore.SceneRenderer);
            curvePreview.SetVisible(true);
            
            // 重置状态
            currentState = CurveEditState.Idle;
            selectedControlPointIndex = -1;
            isDraggingPoint = false;
            
            Debug.Log("曲线工具已激活");
        }
        
        /// <summary>
        /// 工具停用时清理
        /// </summary>
        public override void OnDeactivate()
        {
            base.OnDeactivate();
            
            // 隐藏预览
            curvePreview?.SetVisible(false);
            
            // 清理编辑状态
            ClearCurve();
            
            Debug.Log("曲线工具已停用");
        }
        
        /// <summary>
        /// 处理场景输入
        /// </summary>
        public override void OnSceneInput(InputContext context)
        {
            Vector2 worldPos = GetWorldPositionFromMousePosition(context.ScreenPosition);
            
            // 更新预览位置
            curvePreview?.UpdateMousePosition(worldPos);

            

            // 右键添加控制点
            if (context.IsRightMouseDown)
            {
                AddControlPoint(worldPos);
                return;
            }

            // 左键按下：开始选择或拖拽
            if (context.IsLeftMouseDown)
            {
                if (!isDraggingPoint) // 避免在拖拽过程中重复执行
                {
                    int hitIndex = controlPointManager.GetControlPointAt(worldPos, 0.5f);
                    if (hitIndex >= 0)
                    {
                        // 点击到了一个点
                        SelectControlPoint(hitIndex);
                        StartDraggingPoint(hitIndex);
                    }
                    else
                    {
                        // 点击了空白处，取消选择
                        DeselectControlPoint();
                    }
                }
            }
            else if (isDraggingPoint)
            {
                // 鼠标释放，停止拖拽但保持选中
                StopDraggingPoint();
            }

            // 左键拖拽中
            if (isDraggingPoint)
            {
                MoveControlPoint(selectedControlPointIndex, worldPos);
            }

            // Esc 键：清除当前曲线
            if (context.IsKeyDown(KeyCode.Escape))
            {
                ClearCurve();
            }

            // 检查删除键
            if (context.IsKeyDown(KeyCode.Delete) && selectedControlPointIndex != -1)
            {
                DeleteControlPoint(selectedControlPointIndex);
            }
        }
        
        /// <summary>
        /// 更新工具预览
        /// </summary>
        public override void UpdatePreview()
        {
            curvePreview?.UpdateRender();
        }

        
        
        public void UpdateControlPointVisuals()
        {
            controlPointManager.UpdateControlPointVisuals(controlPoints, selectedControlPointIndex);
        }

        #region 状态处理方法
        
        #endregion
        
        #region 曲线操作方法

        private void DeleteControlPoint(int index)
        {
            if (index >= 0 && index < controlPoints.Count)
            {
                controlPoints.RemoveAt(index);
                DeselectControlPoint(); // 取消选择
                UpdateControlPointVisuals();
                RegenerateCurve();
                PublishToolStatusChanged($"已删除控制点 {index + 1}");
            }
        }
        
        private void StartPlacingPoints(Vector2 worldPos)
        {
            currentState = CurveEditState.PlacingPoints;
            controlPoints.Clear();
            AddControlPoint(worldPos);
            PublishToolStatusChanged("开始放置控制点");
        }
        
        private void AddControlPoint(Vector2 worldPos)
        {
            controlPoints.Add(worldPos);
            UpdateControlPointVisuals();
            RegenerateCurve();
            PublishToolStatusChanged($"已放置控制点 {controlPoints.Count}");
        }
        
        private void FinishPlacingPoints()
        {
            if (controlPoints.Count >= 2)
            {
                currentState = CurveEditState.Previewing;
                RegenerateCurve();
                PublishToolStatusChanged("完成控制点放置，进入预览模式");
            }
            else
            {
                PublishToolStatusChanged("至少需要2个控制点");
            }
        }
        
        private void CancelPlacingPoints()
        {
            ClearCurve();
            PublishToolStatusChanged("取消操作");
        }
        
        private void StartDraggingPoint(int index)
        {
            selectedControlPointIndex = index;
            isDraggingPoint = true;
            PublishToolStatusChanged($"开始拖拽控制点 {index + 1}");
        }
        
        private void MoveControlPoint(int index, Vector2 worldPos)
        {
            if (index >= 0 && index < controlPoints.Count)
            {
                controlPoints[index] = worldPos;
                UpdateControlPointVisuals();
                RegenerateCurve();
            }
        }
        
        private void StopDraggingPoint()
        {
            isDraggingPoint = false;
            PublishToolStatusChanged("停止拖拽控制点");
        }

        private void SelectControlPoint(int index)
        {
            if (selectedControlPointIndex != index)
            {
                selectedControlPointIndex = index;
                UpdateControlPointVisuals();
                PublishToolStatusChanged($"选中控制点 {index + 1}");
            }
        }

        private void DeselectControlPoint()
        {
            if (selectedControlPointIndex != -1)
            {
                selectedControlPointIndex = -1;
                UpdateControlPointVisuals();
                PublishToolStatusChanged("取消选择");
            }
        }
        
        private void RegenerateCurve()
        {
            if (controlPoints.Count >= 2)
            {
                curveSegments = curveGenerator.GenerateCurve(controlPoints, strokeSettings);
                curvePreview.UpdateCurvePreview(curveSegments, strokeSettings);
            }
            else
            {
                curveSegments.Clear();
                curvePreview.ClearPreview();
            }
        }
        
        public void ExecuteDraw()
        {
            if (curveSegments.Count > 0)
            {
                // TODO: 集成到GPU绘制管线
                DrawCurveToLayer();
                ClearCurve();
                PublishToolStatusChanged("曲线绘制完成");
            }
        }
        
        private void DrawCurveToLayer()
        {
            var renderer = mapService.GetActiveLayerRenderer() as TilemapLayerRenderer;
            if (renderer != null && curveSegments.Count > 0)
            {
                try
                {
                    // 使用GPU绘制管线绘制曲线
                    renderer.DrawCurveStrokeGPU(curveSegments, strokeSettings);
                    Debug.Log($"曲线绘制完成 - 段数: {curveSegments.Count}, 宽度: {strokeSettings.width}, 材质: {strokeSettings.materialIndex}");
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"曲线绘制失败: {ex.Message}");
                }
            }
            else
            {
                Debug.LogWarning("无法获取有效的Tilemap渲染器或曲线段为空");
            }
        }
        
        private void ClearCurve()
        {
            controlPoints.Clear();
            curveSegments.Clear();
            DeselectControlPoint();
            isDraggingPoint = false;
            
            controlPointManager.ClearControlPointVisuals();
            curvePreview.ClearPreview();
        }
        
        #endregion
        
        #region 参数设置方法
        
        /// <summary>
        /// 设置描边宽度
        /// </summary>
        public void SetStrokeWidth(float width)
        {
            strokeSettings = new CurveStrokeSettings(
                Mathf.Max(0.1f, width),
                strokeSettings.edgeSoftness,
                strokeSettings.materialIndex,
                strokeSettings.strength,
                strokeSettings.curveType,
                strokeSettings.resolution,
                strokeSettings.shape,
                strokeSettings.hardness
            );
            RegenerateCurve();
        }
        
        /// <summary>
        /// 设置边缘柔和度
        /// </summary>
        public void SetEdgeSoftness(float softness)
        {
            strokeSettings = new CurveStrokeSettings(
                strokeSettings.width,
                Mathf.Clamp01(softness),
                strokeSettings.materialIndex,
                strokeSettings.strength,
                strokeSettings.curveType,
                strokeSettings.resolution,
                strokeSettings.shape,
                strokeSettings.hardness
            );
            RegenerateCurve();
        }
        
        /// <summary>
        /// 设置材质索引
        /// </summary>
        public void SetMaterialIndex(int index)
        {
            strokeSettings = new CurveStrokeSettings(
                strokeSettings.width,
                strokeSettings.edgeSoftness,
                Mathf.Max(0, index),
                strokeSettings.strength,
                strokeSettings.curveType,
                strokeSettings.resolution,
                strokeSettings.shape,
                strokeSettings.hardness
            );
        }
        
        /// <summary>
        /// 设置绘制强度
        /// </summary>
        public void SetStrength(float strength)
        {
            strokeSettings = new CurveStrokeSettings(
                strokeSettings.width,
                strokeSettings.edgeSoftness,
                strokeSettings.materialIndex,
                Mathf.Clamp01(strength),
                strokeSettings.curveType,
                strokeSettings.resolution,
                strokeSettings.shape,
                strokeSettings.hardness
            );
        }
        
        /// <summary>
        /// 设置曲线类型
        /// </summary>
        public void SetCurveType(CurveType type)
        {
            strokeSettings = new CurveStrokeSettings(
                strokeSettings.width,
                strokeSettings.edgeSoftness,
                strokeSettings.materialIndex,
                strokeSettings.strength,
                type,
                strokeSettings.resolution,
                strokeSettings.shape,
                strokeSettings.hardness
            );
            RegenerateCurve();
        }

        /// <summary>
        /// 设置硬度
        /// </summary>
        public void SetHardness(float hardness)
        {
            strokeSettings = new CurveStrokeSettings(
                strokeSettings.width,
                strokeSettings.edgeSoftness,
                strokeSettings.materialIndex,
                strokeSettings.strength,
                strokeSettings.curveType,
                strokeSettings.resolution,
                strokeSettings.shape,
                Mathf.Clamp(hardness, 0.1f, 5.0f)
            );
            RegenerateCurve();
        }
        
        #endregion
    }
    
    /// <summary>
    /// 曲线编辑状态枚举
    /// </summary>
    public enum CurveEditState
    {
        Idle,               // 空闲状态
        PlacingPoints,      // 放置控制点
        EditingPoint,       // 编辑控制点
        Previewing          // 预览绘制
    }
}
 