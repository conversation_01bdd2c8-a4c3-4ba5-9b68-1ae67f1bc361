using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 网格类型枚举
    /// </summary>
    public enum GridType
    {
        Square,     // 方形网格
        Diamond,    // 菱形网格
        Hexagonal,  // 六边形网格 (预留)
        Isometric   // 等距网格 (预留)
    }
    
    /// <summary>
    /// 网格渲染器接口，定义网格渲染功能
    /// </summary>
    public interface IGridRenderer : IRenderable
    {
        /// <summary>
        /// 网格类型
        /// </summary>
        GridType GridType { get; }
        
        /// <summary>
        /// 网格单元大小
        /// </summary>
        Vector2 CellSize { get; set; }
        
        /// <summary>
        /// 网格颜色
        /// </summary>
        Color GridColor { get; set; }
        
        /// <summary>
        /// 主要网格线颜色（如10x10的主要分隔线）
        /// </summary>
        Color MajorGridColor { get; set; }
        
        /// <summary>
        /// 主要网格线间隔
        /// </summary>
        int MajorGridInterval { get; set; }
        
        /// <summary>
        /// 网格线宽度
        /// </summary>
        float LineWidth { get; set; }
        
        /// <summary>
        /// 主要网格线宽度
        /// </summary>
        float MajorLineWidth { get; set; }
        
        /// <summary>
        /// 设置网格范围
        /// </summary>
        /// <param name="bounds">网格范围</param>
        void SetGridBounds(Rect bounds);
        
        /// <summary>
        /// 世界坐标转换为网格坐标
        /// </summary>
        /// <param name="worldPosition">世界坐标</param>
        /// <returns>网格坐标（整数坐标）</returns>
        Vector2Int WorldToGrid(Vector2 worldPosition);
        
        /// <summary>
        /// 网格坐标转换为世界坐标（网格单元中心点）
        /// </summary>
        /// <param name="gridPosition">网格坐标</param>
        /// <returns>世界坐标</returns>
        Vector2 GridToWorld(Vector2Int gridPosition);
        
        /// <summary>
        /// 获取离世界坐标最近的网格单元中心点
        /// </summary>
        /// <param name="worldPosition">世界坐标</param>
        /// <returns>最近的网格单元中心点世界坐标</returns>
        Vector2 SnapToGrid(Vector2 worldPosition);
        
        /// <summary>
        /// 重建网格
        /// </summary>
        void RebuildGrid();
    }
} 