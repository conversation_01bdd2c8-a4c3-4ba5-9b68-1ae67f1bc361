using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Utility;
using System.Threading.Tasks;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// 新建地图对话框
    /// </summary>
    public class NewMapDialog : UIPanel
    {
        public override string PanelId => "newMapDialog";
        public override string DisplayName => "新建地图";
        
        // UI控件引用
        private TextField mapNameField;
        private DropdownField mapSizeDropdown;
        private VisualElement customSizeContainer;
        private IntegerField widthField;
        private IntegerField heightField;
        private IntegerField chunkSizeField;
        private VisualElement textureGrid;
        private Button createButton;
        private Button cancelButton;
        
        // 数据
        private readonly List<string> mapSizeOptions = new()
        {
            "512 × 512",
            "1024 × 1024",
            "2048 × 2048",
            "4096 × 4096",
            "自定义"
        };
        
        private readonly Dictionary<string, Vector2Int> mapSizeMap = new()
        {
            { "512 × 512", new Vector2Int(512, 512) },
            { "1024 × 1024", new Vector2Int(1024, 1024) },
            { "2048 × 2048", new Vector2Int(2048, 2048) },
            { "4096 × 4096", new Vector2Int(4096, 4096) }
        };
        
        // 使用实际的地表纹理数据
        private GroundTextureSO[] availableTextures;
        private int selectedTextureIndex = -1; // 使用纹理索引而不是字符串ID
        
        // 事件
        public event Action<NewMapDialogData> OnMapCreated;
        public event Action OnCancelled;
        
        public NewMapDialog(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
            // 默认隐藏对话框
            IsVisible = false;
        }
        
        public override void Initialize()
        {
            // 查找UI控件
            mapNameField = FindElement<TextField>("MapNameField");
            mapSizeDropdown = FindElement<DropdownField>("MapSizeDropdown");
            customSizeContainer = FindElement<VisualElement>("CustomSizeContainer");
            widthField = FindElement<IntegerField>("WidthField");
            heightField = FindElement<IntegerField>("HeightField");
            chunkSizeField = FindElement<IntegerField>("ChunkSizeField");
            textureGrid = FindElement<VisualElement>("TextureGrid");
            createButton = FindElement<Button>("CreateButton");
            cancelButton = FindElement<Button>("CancelButton");
            
            if (mapNameField == null || mapSizeDropdown == null || 
                createButton == null || cancelButton == null)
            {
                Debug.LogError("NewMapDialog: 缺少必要的UI控件");
                return;
            }
            
            // 设置地图尺寸下拉选项
            mapSizeDropdown.choices = mapSizeOptions;
            mapSizeDropdown.value = mapSizeOptions[1]; // 默认选择"1024 × 1024"
            
            // 注册事件
            mapSizeDropdown.RegisterValueChangedCallback(OnMapSizeChanged);
            createButton.clicked += OnCreateButtonClicked;
            cancelButton.clicked += OnCancelButtonClicked;
            
            // 加载可用的地表纹理
            LoadAvailableTextures();
            
            // 初始化纹理选择网格
            InitializeTextureGrid();
            
            // 设置默认值
            ResetToDefaults();
            
            Debug.Log("NewMapDialog 初始化完成");
        }
        
        /// <summary>
        /// 加载可用的地表纹理
        /// </summary>
        private void LoadAvailableTextures()
        {
            availableTextures = GroundTextureProvider.GetAllTextures();
            if (availableTextures == null || availableTextures.Length == 0)
            {
                Debug.LogWarning("没有找到可用的地表纹理");
                availableTextures = new GroundTextureSO[0];
            }
        }
        
        /// <summary>
        /// 显示对话框
        /// </summary>
        public void ShowDialog()
        {
            ResetToDefaults();
            IsVisible = true;
            
            // 聚焦到名称输入框
            mapNameField?.Focus();
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        private void ResetToDefaults()
        {
            if (mapNameField != null)
                mapNameField.value = "新地图";
                
            if (mapSizeDropdown != null)
                mapSizeDropdown.value = mapSizeOptions[1]; // 1024 × 1024
                
            if (customSizeContainer != null)
                customSizeContainer.style.display = DisplayStyle.None;
                
            if (widthField != null)
                widthField.value = 1024;
                
            if (heightField != null)
                heightField.value = 1024;
                
            if (chunkSizeField != null)
                chunkSizeField.value = 256;
            
            // 默认选择第一个纹理（如果有的话）
            selectedTextureIndex = availableTextures != null && availableTextures.Length > 0 ? 0 : -1;
            UpdateTextureSelection();
        }
        
        /// <summary>
        /// 初始化纹理选择网格
        /// </summary>
        private void InitializeTextureGrid()
        {
            if (textureGrid == null) return;
            
            textureGrid.Clear();
            
            if (availableTextures == null || availableTextures.Length == 0)
            {
                var noTextureLabel = new Label("没有可用的地表纹理")
                {
                    style = { color = Color.gray, unityTextAlign = TextAnchor.MiddleCenter }
                };
                textureGrid.Add(noTextureLabel);
                return;
            }
            
            for (int i = 0; i < availableTextures.Length; i++)
            {
                var textureInfo = availableTextures[i];
                if (textureInfo == null) continue;
                
                var textureButton = new Button()
                {
                    name = $"TextureButton_{i}",
                    text = string.IsNullOrEmpty(textureInfo.displayName) ? $"纹理{textureInfo.textureIndex}" : textureInfo.displayName
                };
                
                // 设置按钮样式
                textureButton.style.width = 80;
                textureButton.style.height = 60;
                textureButton.style.marginLeft = textureButton.style.marginRight = 
                textureButton.style.marginTop = textureButton.style.marginBottom = 2;
                textureButton.style.borderLeftWidth = textureButton.style.borderRightWidth = 
                textureButton.style.borderTopWidth = textureButton.style.borderBottomWidth = 2;
                textureButton.style.borderLeftColor = textureButton.style.borderRightColor = 
                textureButton.style.borderTopColor = textureButton.style.borderBottomColor = Color.gray;
                
                // 使用实际纹理作为背景（如果有的话）
                if (textureInfo.texture != null)
                {
                    textureButton.style.backgroundImage = new StyleBackground(textureInfo.texture);
                    textureButton.style.backgroundSize = new BackgroundSize(BackgroundSizeType.Contain);
                    textureButton.style.backgroundRepeat = new BackgroundRepeat(Repeat.NoRepeat, Repeat.NoRepeat);
                    // 减小文字大小，让纹理更清晰
                    textureButton.style.fontSize = 10;
                    textureButton.style.color = Color.white;
                    // 添加文字阴影效果
                    textureButton.style.textShadow = new TextShadow
                    {
                        offset = new Vector2(1, 1),
                        blurRadius = 1,
                        color = Color.black
                    };
                }
                
                // 注册点击事件
                int textureIndex = i;
                textureButton.clicked += () => OnTextureSelected(textureIndex);
                
                textureGrid.Add(textureButton);
            }
        }
        
        /// <summary>
        /// 地图尺寸改变事件
        /// </summary>
        private void OnMapSizeChanged(ChangeEvent<string> evt)
        {
            bool isCustom = evt.newValue == "自定义";
            if (customSizeContainer != null)
            {
                customSizeContainer.style.display = isCustom ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }
        
        /// <summary>
        /// 纹理选择事件
        /// </summary>
        private void OnTextureSelected(int textureIndex)
        {
            if (textureIndex >= 0 && textureIndex < availableTextures.Length)
            {
                selectedTextureIndex = textureIndex;
                UpdateTextureSelection();
                Debug.Log($"选择纹理: {availableTextures[textureIndex].displayName} (索引: {availableTextures[textureIndex].textureIndex})");
            }
        }
        
        /// <summary>
        /// 更新纹理选择状态
        /// </summary>
        private void UpdateTextureSelection()
        {
            if (textureGrid == null) return;
            
            // 重置所有按钮状态
            for (int i = 0; i < textureGrid.childCount; i++)
            {
                if (textureGrid[i] is Button button)
                {
                    button.style.borderLeftColor = button.style.borderRightColor = 
                    button.style.borderTopColor = button.style.borderBottomColor = Color.gray;
                    button.style.borderLeftWidth = button.style.borderRightWidth = 
                    button.style.borderTopWidth = button.style.borderBottomWidth = 2;
                }
            }
            
            // 高亮选中的纹理
            if (selectedTextureIndex >= 0 && selectedTextureIndex < textureGrid.childCount)
            {
                var selectedButton = textureGrid[selectedTextureIndex] as Button;
                if (selectedButton != null)
                {
                    selectedButton.style.borderLeftColor = selectedButton.style.borderRightColor = 
                    selectedButton.style.borderTopColor = selectedButton.style.borderBottomColor = Color.yellow;
                    selectedButton.style.borderLeftWidth = selectedButton.style.borderRightWidth = 
                    selectedButton.style.borderTopWidth = selectedButton.style.borderBottomWidth = 3;
                }
            }
        }
        
        /// <summary>
        /// 获取当前选择的地图尺寸
        /// </summary>
        private Vector2Int GetSelectedMapSize()
        {
            string selectedSize = mapSizeDropdown.value;
            
            if (selectedSize == "自定义")
            {
                return new Vector2Int(
                    widthField?.value ?? 1024,
                    heightField?.value ?? 1024
                );
            }
            
            return mapSizeMap.ContainsKey(selectedSize) ? mapSizeMap[selectedSize] : new Vector2Int(1024, 1024);
        }
        
        private async void OnCreateButtonClicked()
        {
            // 验证输入
            string mapName = mapNameField.value?.Trim();
            if (string.IsNullOrEmpty(mapName))
            {
                RequestShowMessage("错误", "地图名称不能为空", MessageType.Error);
                return;
            }
            
            // 检查地图名称是否重复
            if (IsMapNameExists(mapName))
            {
                RequestShowMessage("错误", "地图名称已存在，请使用其他名称", MessageType.Error);
                return;
            }
            
            // 验证地图尺寸
            Vector2Int mapSize = GetSelectedMapSize();
            if (mapSize.x <= 0 || mapSize.y <= 0 || mapSize.x > 16384 || mapSize.y > 16384)
            {
                RequestShowMessage("错误", "地图尺寸必须在1-16384之间", MessageType.Error);
                return;
            }
            
            // 计算实际地图尺寸，确保能被 ChunkSize 整除
            int chunkSize = chunkSizeField?.value ?? 256;
            if (chunkSize <= 0)
            {
                RequestShowMessage("错误", "Chunk 大小必须大于 0", MessageType.Error);
                return;
            }

            int realWidth = Mathf.CeilToInt(mapSize.x / (float)chunkSize) * chunkSize;
            int realHeight = Mathf.CeilToInt(mapSize.y / (float)chunkSize) * chunkSize;
            Vector2Int realMapSize = new Vector2Int(realWidth, realHeight);

            // 弹出目录选择框（异步）
            string directory = await FileDialogHelper.SelectFolderAsync("选择地图保存目录");
            if (string.IsNullOrEmpty(directory))
            {
                RequestShowMessage("提示", "未选择目录，已取消新建地图", MessageType.Info);
                return;
            }

            // 创建地图数据，传递选中纹理的全局索引和 ChunkSize
            var mapData = new NewMapDialogData
            {
                mapName = mapName,
                mapSize = realMapSize,
                baseTextureId = selectedTextureIndex >= 0 && selectedTextureIndex < availableTextures.Length ? 
                               availableTextures[selectedTextureIndex].textureIndex.ToString() : string.Empty,
                baseTextureIndex = selectedTextureIndex >= 0 && selectedTextureIndex < availableTextures.Length ? 
                                  availableTextures[selectedTextureIndex].textureIndex : -1,
                baseTexture = selectedTextureIndex >= 0 && selectedTextureIndex < availableTextures.Length ? 
                             availableTextures[selectedTextureIndex].texture : null,
                directoryPath = directory,
                chunkSize = chunkSize
            };
            
            // 触发创建事件
            OnMapCreated?.Invoke(mapData);
            
            // 隐藏对话框
            IsVisible = false;
            
            Debug.Log($"创建地图: {mapData.mapName} ({mapData.mapSize.x}×{mapData.mapSize.y}), 基础纹理索引: {mapData.baseTextureIndex}");
        }
        
        private void OnCancelButtonClicked()
        {
            // 触发取消事件
            OnCancelled?.Invoke();
            
            // 隐藏对话框
            IsVisible = false;
            
            Debug.Log("取消创建地图");
        }
        
        /// <summary>
        /// 检查地图名称是否已存在
        /// </summary>
        private bool IsMapNameExists(string mapName)
        {
            // 这里可以扩展为检查已保存的地图列表
            // 暂时简单检查当前地图
            var mapService = MapEditorCore.Instance.GetService<MapService>();
            if (mapService?.CurrentMap != null)
            {
                return mapService.CurrentMap.MapName.Equals(mapName, StringComparison.OrdinalIgnoreCase);
            }
            
            return false;
        }
    }
    
    /// <summary>
    /// 基础纹理信息（简化版本，已弃用，保留用于兼容性）
    /// </summary>
    [Serializable]
    public class BasicTextureInfo
    {
        public string name;
        public Color color;
    }
}