using UnityEngine;
using MapEditor.Core;
using MapEditor.Config;

namespace MapEditor.Rendering
{
    /// <summary>
    /// 基于屏幕空间 Shader 的网格渲染器。
    /// 只使用一个覆盖地图范围的 Quad，由 Shader 决定何处绘制网格线。
    /// </summary>
    public class ShaderGridRenderer : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IGridRenderer
    {
        [Header("网格设置")]
        [SerializeField] private Vector2 cellSize = Vector2.one;
        [SerializeField] private Color gridColor = new(0.6f, 0.6f, 0.6f, 0.4f);
        [SerializeField] private Color majorGridColor = new(0.9f, 0.9f, 0.9f, 0.6f);
        [SerializeField] private int majorGridInterval = 5;
        [SerializeField] private float lineWidthWU = 0.05f; // 次网格线宽
        [SerializeField] private float majorLineWidthWU = 0.1f; // 主网格线宽

        [Header("渲染设置")]
        [SerializeField] private Material gridMaterialTemplate;
        [SerializeField] private int renderOrder = 0;

        // 运行时
        private Rect bounds = new(0, 0, 10, 10);
        private Mesh quadMesh;
        private MeshRenderer meshRenderer;

        #region IGridRenderer 属性
        public GridType GridType => GridType.Square;
        public Vector2 CellSize { get => cellSize; set { cellSize = value; ApplyMaterialParams(); } }
        public Color GridColor { get => gridColor; set { gridColor = value; ApplyMaterialParams(); } }
        public Color MajorGridColor { get => majorGridColor; set { majorGridColor = value; ApplyMaterialParams(); } }
        public int MajorGridInterval { get => majorGridInterval; set { majorGridInterval = value; ApplyMaterialParams(); } }
        public float LineWidth { get => lineWidthWU; set { lineWidthWU = value; ApplyMaterialParams(); } }
        public float MajorLineWidth { get => majorLineWidthWU; set { majorLineWidthWU = value; ApplyMaterialParams(); } }
        public int RenderOrder => renderOrder;
        public RenderLayer Layer => RenderLayer.Grid;
        public bool IsVisible { get => meshRenderer.enabled; set => meshRenderer.enabled = value; }
        #endregion

        private void Awake()
        {
            if (gridMaterialTemplate == null)
            {
                gridMaterialTemplate = new Material(Shader.Find("MapEditor/GridOverlay"));
            }

            quadMesh = GenerateQuadMesh();
            var mf = gameObject.AddComponent<MeshFilter>();
            mf.sharedMesh = quadMesh;

            meshRenderer = gameObject.AddComponent<MeshRenderer>();
            meshRenderer.material = new Material(gridMaterialTemplate);
            meshRenderer.sortingOrder = SortingOrderConfig.GridFixedOrder;

            RebuildGrid();
            ApplyMaterialParams();
        }

        private Mesh GenerateQuadMesh()
        {
            Mesh m = new Mesh();
            m.name = "GridQuad";
            // placeholder, vertices will be overwritten in RebuildGrid
            m.vertices = new Vector3[] { Vector3.zero, Vector3.right, Vector3.up, Vector3.one };
            m.uv = new Vector2[] { Vector2.zero, Vector2.right, Vector2.up, Vector2.one };
            m.triangles = new int[] { 0, 1, 2, 2, 1, 3 };
            m.RecalculateBounds();
            return m;
        }

        private void ApplyMaterialParams()
        {
            if (meshRenderer == null || meshRenderer.sharedMaterial == null) return;
            var mat = meshRenderer.sharedMaterial;
            mat.SetColor("_GridColor", gridColor);
            mat.SetColor("_MajorGridColor", majorGridColor);
            mat.SetVector("_CellSize", cellSize);
            mat.SetFloat("_MajorInterval", majorGridInterval);
            mat.SetFloat("_LineWidth", lineWidthWU);
            mat.SetFloat("_MajorLineWidth", majorLineWidthWU);
            mat.SetVector("_GridOrigin", new Vector4(bounds.xMin, bounds.yMin, 0, 0));
        }

        public void SetGridBounds(Rect newBounds)
        {
            bounds = newBounds;
            RebuildGrid();
            ApplyMaterialParams();
        }

        public Vector2Int WorldToGrid(Vector2 worldPosition)
        {
            int x = Mathf.FloorToInt((worldPosition.x - bounds.xMin) / cellSize.x);
            int y = Mathf.FloorToInt((worldPosition.y - bounds.yMin) / cellSize.y);
            return new Vector2Int(x, y);
        }

        public Vector2 GridToWorld(Vector2Int gridPosition)
        {
            float x = bounds.xMin + (gridPosition.x + 0.5f) * cellSize.x;
            float y = bounds.yMin + (gridPosition.y + 0.5f) * cellSize.y;
            return new Vector2(x, y);
        }

        public Vector2 SnapToGrid(Vector2 worldPosition)
        {
            var gp = WorldToGrid(worldPosition);
            return GridToWorld(gp);
        }

        public void RebuildGrid()
        {
            if (quadMesh == null) return;
            // Create quad covering bounds
            Vector3 bl = new Vector3(bounds.xMin, bounds.yMin, 0);
            Vector3 br = new Vector3(bounds.xMax, bounds.yMin, 0);
            Vector3 tl = new Vector3(bounds.xMin, bounds.yMax, 0);
            Vector3 tr = new Vector3(bounds.xMax, bounds.yMax, 0);
            quadMesh.vertices = new Vector3[] { bl, br, tl, tr };
            quadMesh.triangles = new int[] { 0, 1, 2, 2, 1, 3 };
            quadMesh.RecalculateBounds();
        }

        public void UpdateRender() { /* no-op */ }
    }
} 