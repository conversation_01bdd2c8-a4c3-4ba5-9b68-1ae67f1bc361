using UnityEngine;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Rendering.Layers;
using MapEditor.Event;

namespace MapEditor.Tools
{
    /// <summary>
    /// 画笔工具，用于在地图上绘制纹理
    /// </summary>
    public class BrushTool : MapToolBase
    {
        // 画笔参数
        private float brushSize = 1.0f;
        private float brushStrength = 1.0f;
        private Texture2D brushTexture;
        private Color brushColor = Color.white;
        
        // 绘制状态
        private bool isDrawing = false;
        private Vector2 lastDrawPosition;
        private MapService _mapService;
        
        // 预览渲染器
        private BrushPreviewRenderer previewRenderer;
        
        // 新增：当前笔刷设置
        private BrushSettings currentSettings;
        // 形状和材质资源
        private Data.BrushShapeSO defaultShape;

        private ToolManager toolManager;

        // 新增：获取当前设置
        public BrushSettings CurrentSettings => currentSettings;
        
        // 新增：刷子间隔逻辑参数
        private float _pendingDistance = 0f; // 累积的未落点距离
        private const float SPACING_FACTOR = 0.25f; // 距笔刷大小的比例，决定落点间距 (更密集避免断流)
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="editorCore">编辑器核心引用</param>
        public BrushTool(IMapEditorCore editorCore) 
            : base("BrushTool", "画笔工具", editorCore, new[] { LayerType.Ground })
        {
            _mapService = editorCore.GetService<MapService>();
            toolManager = editorCore.GetService<ToolManager>();
            // 加载默认画笔纹理
            brushTexture = Resources.Load<Texture2D>("Brushes/1");
            if (brushTexture == null)
            {
                // 如果没有找到默认画笔，创建一个简单的圆形画笔
                brushTexture = CreateDefaultBrushTexture();
            }

            // 加载默认形状 (若无则用 brushTexture)
            defaultShape = Resources.Load<Data.BrushShapeSO>("BrushShapes/DefaultShape");
            if (defaultShape == null)
            {
                // 动态创建一个 shape 容器以便后续引用
                defaultShape = ScriptableObject.CreateInstance<MapEditor.Data.BrushShapeSO>();
                defaultShape.alphaMask = brushTexture;
            }

            // 初始化 settings（materialIndex 会在 UI 中设置）
            currentSettings = new BrushSettings(0, brushSize, brushStrength, defaultShape, 0, 0.5f, 10.0f);

            // 订阅画笔大小快捷键事件
            RegisterEvent<BrushSizeShortcutEvent>(OnBrushSizeShortcut);
        }
        
        /// <summary>
        /// 工具激活时初始化
        /// </summary>
        public override void OnActivate()
        {
            base.OnActivate();
            
            // 创建预览渲染器
            if (previewRenderer == null)
            {
                GameObject rendererObj = new ("BrushPreviewRenderer");
                previewRenderer = rendererObj.AddComponent<BrushPreviewRenderer>();
                previewRenderer.Initialize(editorCore.SceneRenderer, brushTexture);
            }
            
            previewRenderer.gameObject.SetActive(true);
            previewRenderer.SetBrushProperties(brushSize, brushColor);
        }
        
        /// <summary>
        /// 工具停用时清理
        /// </summary>
        public override void OnDeactivate()
        {
            base.OnDeactivate();
            
            // 隐藏预览渲染器
            previewRenderer?.gameObject.SetActive(false);
            
            // 重置绘制状态
            isDrawing = false;
        }
        
        /// <summary>
        /// 处理场景输入
        /// </summary>
        public override void OnSceneInput(InputContext context)
        {
            // 获取画笔位置（世界坐标）
            Vector2 brushPos = GetWorldPositionFromMousePosition(context.ScreenPosition);
            
            // 更新预览位置
            previewRenderer?.SetPosition(brushPos);
            
            // 处理鼠标按下事件（开始绘制）
            if (context.IsLeftMouseDown && !isDrawing)
            {
                BeginDraw(brushPos);
            }
            // 处理鼠标抬起事件（结束绘制）
            else if (!context.IsLeftMouseDown && isDrawing)
            {
                EndDraw();
            }
            
            // 如果正在绘制，执行绘制操作
            if (isDrawing)
            {
                DrawGPU(brushPos); // 使用 GPU 路径
                lastDrawPosition = brushPos;
            }
            
        }
        
        /// <summary>
        /// 更新工具预览
        /// </summary>
        public override void UpdatePreview()
        {
            previewRenderer?.UpdateRender();
        }
        
        /// <summary>
        /// 开始绘制
        /// </summary>
        private void BeginDraw(Vector2 position)
        {
            isDrawing = true;
            lastDrawPosition = position;
            _pendingDistance = 0f; // 重置距离累积

            // 首次立即绘制一次
            var renderer = _mapService.GetActiveLayerRenderer() as TilemapLayerRenderer;
            renderer?.DrawBrushGPU(position, currentSettings);

            PublishToolStatusChanged("开始绘制");
        }
        

        
        /// <summary>
        /// 结束绘制
        /// </summary>
        private void EndDraw()
        {
            isDrawing = false;
            // 结束时确保在最终位置再落一次，避免因剩余距离<spacing 导致的尾端断流
            var renderer = _mapService.GetActiveLayerRenderer() as TilemapLayerRenderer;
            if (renderer != null)
            {
                // 使用当前鼠标位置进行最后一次 stamp
                Vector2 mouseWorldPos = GetWorldPositionFromMousePosition(Input.mousePosition);
                renderer.DrawBrushGPU(mouseWorldPos, currentSettings);
            }
            PublishToolStatusChanged("结束绘制");
            
            // 可以在这里发送绘制完成事件
        }
        
 

        
        /// <summary>
        /// 调整画笔大小
        /// </summary>
        private void AdjustBrushSize(float delta)
        {
            // 调整画笔大小，限制在合理范围内
            brushSize = Mathf.Clamp(brushSize + delta * 0.5f, 0.5f, 10f);

            // NEW: 同步到当前笔刷设置，确保绘制逻辑使用最新大小
            currentSettings.size = brushSize;
            
            // 更新预览
            previewRenderer?.SetBrushProperties(brushSize, brushColor);

            PublishToolStatusChanged($"画笔大小: {brushSize:F1}");
        }
        
       
        /// <summary>
        /// 设置画笔大小
        /// </summary>
        public void SetBrushSize(float size)
        {
            brushSize = Mathf.Clamp(size, 0.5f, 10f);
            currentSettings.size = brushSize;
            previewRenderer?.SetBrushProperties(brushSize, brushColor);
        }
        
        /// <summary>
        /// 设置画笔强度
        /// </summary>
        public void SetBrushStrength(float strength)
        {
            brushStrength = Mathf.Clamp01(strength);
            currentSettings.strength = brushStrength;
        }
        
        /// <summary>
        /// 创建默认画笔纹理（简单的圆形）
        /// </summary>
        private Texture2D CreateDefaultBrushTexture()
        {
            int size = 64;
            Texture2D texture = new(size, size, TextureFormat.RGBA32, false)
            {
                filterMode = FilterMode.Bilinear
            };

            Color[] colors = new Color[size * size];
            float center = size / 2f;
            
            for (int y = 0; y < size; y++)
            {
                for (int x = 0; x < size; x++)
                {
                    float distance = Vector2.Distance(new Vector2(x, y), new Vector2(center, center));
                    float alpha = 1f - Mathf.Clamp01(distance / center);
                    
                    // 软化边缘
                    alpha = Mathf.SmoothStep(0, 1, alpha);
                    
                    colors[y * size + x] = new Color(1, 1, 1, alpha);
                }
            }
            
            texture.SetPixels(colors);
            texture.Apply();
            
            return texture;
        }
        
 
        /// <summary>
        /// 通过键盘快捷键调整画笔大小
        /// </summary>
        /// <param name="direction">+1 放大，-1 缩小</param>
        public void ChangeBrushSize(int direction)
        {
            // direction>0 放大，direction<0 缩小
            AdjustBrushSize(direction);
        }

        /// <summary>
        /// 使用 GPU RenderTexture 绘制权重。
        /// </summary>
        private void DrawGPU(Vector2 position)
        {
            if (layerManager?.ActiveLayer == null) return;
            var renderer = _mapService.GetActiveLayerRenderer() as TilemapLayerRenderer;
            if (renderer == null) return;

            // 计算本次位移
            Vector2 delta = position - lastDrawPosition;
            float dist = delta.magnitude;
            if (dist <= 0f) return; // 当前位置与上一次 stamp 相同，不再重复绘制

            // 计算固定空间间隔
            float spacing = Mathf.Max(brushSize * SPACING_FACTOR, 0.001f);

            // 累加距离并按间隔落点
            _pendingDistance += dist;
            Vector2 dir = delta / dist; // 单位方向

            while (_pendingDistance >= spacing)
            {
                lastDrawPosition += dir * spacing; // 沿方向推进一个间隔
                renderer.DrawBrushGPU(lastDrawPosition, currentSettings);
                _pendingDistance -= spacing;
            }
        }

        /// <summary>
        /// 设置笔刷形状
        /// </summary>
        public void SetBrushShape(Data.BrushShapeSO shape)
        {
            if (shape == null) return;
            currentSettings.shape = shape;
            // 同步预览形状，使所见即所得
            if (previewRenderer != null && shape.alphaMask != null)
            {
                previewRenderer.SetBrushTexture(shape.alphaMask);
                previewRenderer.SetBrushProperties(brushSize, brushColor);
            }
        }

        /// <summary>
        /// 设置材质索引 (0-7)
        /// </summary>
        public void SetMaterialIndex(int index)
        {
            currentSettings.materialIndex = Mathf.Max(0, index);
        }

        /// <summary>
        /// 设置边缘模糊半径
        /// </summary>
        public void SetBlurRadius(int radius)
        {
            currentSettings.blurRadius = Mathf.Max(0, radius);
        }

        /// <summary>
        /// 设置笔刷硬度 (0-1)
        /// </summary>
        public void SetHardness(float hardness)
        {
            currentSettings.hardness = Mathf.Clamp01(hardness);
        }

        /// <summary>
        /// 设置保边模糊强度
        /// </summary>
        public void SetBilateralFactor(float factor)
        {
            currentSettings.bilateralFactor = Mathf.Clamp(factor, 1.0f, 20.0f);
        }

        #region 事件处理

        /// <summary>
        /// 处理画笔大小快捷键事件。
        /// </summary>
        private void OnBrushSizeShortcut(BrushSizeShortcutEvent evt)
        {
            // 仅当当前活动工具为本画笔工具时处理
            if (toolManager?.ActiveTool == this)
            {
                ChangeBrushSize(evt.Direction);
            }
        }

        #endregion
    }
    

}