// SplatBlur.compute
// 对权重 RT 执行 separable 双边滤波（Bilateral Filter），实现保边模糊
// 处理 8 通道权重（7 权重 + coverage），分两张 RGBA 纹理

#pragma kernel CSBlurHorizontal
#pragma kernel CSBlurVertical
#pragma target 5.0

RWTexture2D<float4> _Splat0;    // 0-3 通道
RWTexture2D<float4> _Splat1;    // 4-6 通道 + coverage
int _BlurRadius;                // 模糊半径
float _BilateralFactor;         // 双边滤波强度因子

// 动态计算高斯权重
float Gauss(float x, float sigma)
{
    return exp(-(x * x) / (2.0 * sigma * sigma));
}

// 统一的模糊处理函数
void ApplyBilateralBlur(uint3 id, int2 blurDir)
{
    uint width, height;
    _Splat0.GetDimensions(width, height);
    
    if(id.x >= width || id.y >= height) return;
    if(_BlurRadius <= 0) return;
    
    int2 coord = int2(id.xy);
    int radius = _BlurRadius;

    // 读取中心点权重
    float4 center_w0 = _Splat0[coord];
    float4 center_w1 = _Splat1[coord];

    float4 sum0 = 0;
    float4 sum1 = 0;
    float totalWeight = 0;

    // 循环邻域像素 (包括中心点)
    for(int i = -radius; i <= radius; i++)
    {
        int2 sampleCoord = coord + blurDir * i;

        // 边界检查
        if (sampleCoord.x < 0 || sampleCoord.x >= (int)width || sampleCoord.y < 0 || sampleCoord.y >= (int)height)
        {
            continue;
        }
        
        // 读取邻域像素
        float4 neighbor_w0 = _Splat0[sampleCoord];
        float4 neighbor_w1 = _Splat1[sampleCoord];

        // 1. 空间权重 (高斯)
        // 将半径映射到标准差，sigma 越大，模糊越平滑
        float sigma = max(1.0, (float)radius / 2.0);
        float spatial_w = Gauss(i, sigma);

        // 2. 值域权重 (颜色/权重相似度)
        // 计算两张贴图的权重向量距离。不比较 coverage (alpha通道)
        float diff0 = distance(center_w0.rgb, neighbor_w0.rgb);
        float diff1 = distance(center_w1.rgb, neighbor_w1.rgb);
        // 使用 exp 函数将距离映射到 (0, 1] 区间，差异越大，权重越小
        float range_w = exp(-(diff0 + diff1) * _BilateralFactor);

        // 3. 最终权重 = 空间权重 * 值域权重
        float final_w = spatial_w * range_w;
        
        sum0 += neighbor_w0 * final_w;
        sum1 += neighbor_w1 * final_w;
        totalWeight += final_w;
    }
    
    // 归一化并写回
    if(totalWeight > 1e-6)
    {
        _Splat0[coord] = sum0 / totalWeight;
        _Splat1[coord] = sum1 / totalWeight;
    }
}


[numthreads(8,8,1)]
void CSBlurHorizontal (uint3 id : SV_DispatchThreadID)
{
    ApplyBilateralBlur(id, int2(1, 0));
}

[numthreads(8,8,1)]
void CSBlurVertical (uint3 id : SV_DispatchThreadID)
{
    ApplyBilateralBlur(id, int2(0, 1));
}