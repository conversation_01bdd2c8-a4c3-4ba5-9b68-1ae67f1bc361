namespace MapEditor.Config
{
    /// <summary>
    /// SortingOrder 配置，定义各渲染层级的数值区间
    /// </summary>
    public static class SortingOrderConfig
    {
        /// <summary>
        /// Ground 地表图层区间 (0-1999)
        /// </summary>
        public static readonly (int min, int max) GroundRange = (0, 1999);
        
        /// <summary>
        /// Grid 网格层固定值
        /// </summary>
        public const int GridFixedOrder = 2000;
        
        /// <summary>
        /// Object 对象图层区间 (3000-7999)
        /// </summary>
        public static readonly (int min, int max) ObjectRange = (3000, 7999);
        
        /// <summary>
        /// Preview 预览层区间 (8000-8499)
        /// </summary>
        public static readonly (int min, int max) PreviewRange = (8000, 8499);
        
        /// <summary>
        /// UI 层区间 (8500-8999)
        /// </summary>
        public static readonly (int min, int max) UIRange = (8500, 8999);

        public static (int min, int max) GetRange(MapEditor.Core.LayerType type)
        {
            return type switch
            {
                MapEditor.Core.LayerType.Ground => GroundRange,
                MapEditor.Core.LayerType.Object => ObjectRange,
                MapEditor.Core.LayerType.Decoration => PreviewRange,
                MapEditor.Core.LayerType.Logic => UIRange,
                MapEditor.Core.LayerType.Grid => (GridFixedOrder, GridFixedOrder),
                _ => (0, 0)
            };
        }
    }
} 