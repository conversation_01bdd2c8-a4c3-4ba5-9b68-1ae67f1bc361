using System;
using System.Collections.Generic;
using MapEditor.Core;
using UnityEngine;

namespace MapEditor.Data.Chunks
{
    /// <summary>
    /// 对象Chunk，存储该Chunk内的所有2D对象实例数据
    /// </summary>
    [Serializable]
    public class ObjectChunk : ChunkBase
    {
        [SerializeField] private List<ObjectInstance> objects = new List<ObjectInstance>();

        /// <summary>
        /// 该Chunk内的所有对象实例
        /// </summary>
        public List<ObjectInstance> Objects => objects;

        public ObjectChunk(ChunkCoord coord, int chunkSize) : base(coord)
        {
        }

        /// <summary>
        /// 添加对象实例
        /// </summary>
        public void AddObject(ObjectInstance instance)
        {
            objects.Add(instance);
            MarkDirty();
        }

        /// <summary>
        /// 移除对象实例
        /// </summary>
        public bool RemoveObject(string instanceId)
        {
            int index = objects.FindIndex(obj => obj.InstanceId == instanceId);
            if (index >= 0)
            {
                objects.RemoveAt(index);
                MarkDirty();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 根据ID查找对象实例
        /// </summary>
        public ObjectInstance FindObject(string instanceId)
        {
            return objects.Find(obj => obj.InstanceId == instanceId);
        }

        /// <summary>
        /// 清空所有对象
        /// </summary>
        public void ClearObjects()
        {
            objects.Clear();
            MarkDirty();
        }
    }

    /// <summary>
    /// 对象实例数据
    /// </summary>
    [Serializable]
    public class ObjectInstance
    {
        [SerializeField] private string instanceId;
        [SerializeField] private string prefabGuid; // 预制体的GUID或路径
        [SerializeField] private string displayName; // 对象的自定义显示名称
        [SerializeField] private Vector2 position;  // 世界坐标位置
        [SerializeField] private float rotation;    // 旋转角度（Z轴）
        [SerializeField] private Vector2 scale = Vector2.one;    // 缩放
        [SerializeField] private List<Vector2Int> occupiedCells = new List<Vector2Int>(); // 占用的网格格子

        /// <summary>
        /// 实例唯一ID
        /// </summary>
        public string InstanceId 
        { 
            get => instanceId; 
            set => instanceId = value; 
        }

        /// <summary>
        /// 预制体GUID
        /// </summary>
        public string PrefabGuid 
        { 
            get => prefabGuid; 
            set => prefabGuid = value; 
        }
        
        /// <summary>
        /// 对象的自定义显示名称
        /// </summary>
        public string DisplayName 
        { 
            get => displayName; 
            set => displayName = value; 
        }
        
        /// <summary>
        /// 获取对象的显示名称，如果没有自定义名称则返回PrefabGuid
        /// </summary>
        public string GetDisplayName()
        {
            return !string.IsNullOrEmpty(displayName) ? displayName : prefabGuid;
        }

        /// <summary>
        /// 世界坐标位置
        /// </summary>
        public Vector2 Position 
        { 
            get => position; 
            set => position = value; 
        }

        /// <summary>
        /// 旋转角度
        /// </summary>
        public float Rotation 
        { 
            get => rotation; 
            set => rotation = value; 
        }

        /// <summary>
        /// 缩放
        /// </summary>
        public Vector2 Scale 
        { 
            get => scale; 
            set => scale = value; 
        }

        /// <summary>
        /// 占用的网格格子坐标列表
        /// </summary>
        public List<Vector2Int> OccupiedCells 
        { 
            get => occupiedCells; 
            set => occupiedCells = value ?? new List<Vector2Int>(); 
        }

        public ObjectInstance()
        {
            instanceId = Guid.NewGuid().ToString();
            prefabGuid = "";
            position = Vector2.zero;
            rotation = 0f;
            scale = Vector2.one;
        }

        public ObjectInstance(string prefabGuid, Vector2 position, float rotation = 0f, Vector2? scale = null)
        {
            this.instanceId = Guid.NewGuid().ToString();
            this.prefabGuid = prefabGuid;
            this.position = position;
            this.rotation = rotation;
            this.scale = scale ?? Vector2.one;
        }
    }
} 