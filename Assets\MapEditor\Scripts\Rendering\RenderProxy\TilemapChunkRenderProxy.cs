using UnityEngine;
using MapEditor.Data.Chunks;
using System.IO;

using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Rendering.Layers;

namespace MapEditor.Rendering.RenderProxy
{
    public class TilemapChunkRenderProxy :ChunkRenderProxy
    {
        public Texture2D Texture { get; private set; }
        public SpriteRenderer SpriteRenderer { get; private set; }
        public TilemapChunk TilemapChunk { get; private set; }
        // 标记贴图是否有脏数据, 由 TilemapLayerRenderer 统一调度保存
        private bool _dirty = false;
        public bool IsDirty => _dirty;
        public MaterialPropertyBlock propertyBlock;

    

        
        public TilemapChunkRenderProxy(ChunkCoord coord, TilemapChunk tilemapChunk, LayerRenderer layerRenderer ) : base(coord, layerRenderer)
        {
            TilemapChunk = tilemapChunk;


            // 尝试加载或创建贴图
            LoadOrCreateTexture();

            // 创建并配置 SpriteRenderer 以展示贴图（可根据需要调整像素单位等参数）
            CreateSpriteRenderer();
        }

        public void LoadOrCreateTexture()
        {
            if (TilemapChunk == null)
            {
                Debug.LogError("TilemapChunk is null, cannot load or create texture.");
                return;
            }

            // 获取当前地图目录
            var mapDataStore = MapEditorCore.Instance.MapDataStore;
            var mapDir = mapDataStore?.CurrentMapDirectory;
            if (string.IsNullOrEmpty(mapDir))
            {
                Debug.LogError("CurrentMapDirectory is empty, cannot locate texture directory.");
                return;
            }

            string texturesDir = Path.Combine(mapDir, "TilemapTextures");
            string fileName = TilemapChunk.TilemapId + ".raw";
            string filePath = Path.Combine(texturesDir, fileName);

            // 如果存在则加载
            Texture2D loaded = MapEditor.Utility.RawTextureUtility.LoadTexture(filePath);
            if (loaded != null)
            {
                Texture = loaded;
                return;
            }

            // 不存在则创建 blank texture
            int texSize = Mathf.Max(TilemapChunk.ChunkSize, 1);
            Texture = new Texture2D(texSize, texSize, TextureFormat.RGBA32, false)
            {
                filterMode = FilterMode.Point
            };

            var pixels = new Color[texSize * texSize];
            for (int i = 0; i < pixels.Length; i++)
                pixels[i] = new Color(1, 1, 1, 0);
            Texture.SetPixels(pixels);
            Texture.Apply();

            // 保存初始空白贴图
            MapEditor.Utility.RawTextureUtility.SaveTexture(Texture, filePath);
        }

        /// <summary>
        /// 将当前 Texture 数据保存/更新到磁盘（与 TilemapId 对应的 png 文件）。
        /// </summary>
        public void SaveTexture()
        {
            if (Texture == null || TilemapChunk == null)
            {
                Debug.LogWarning("Cannot save texture: Texture or TilemapChunk is null.");
                return;
            }

            var mapDataStore = MapEditorCore.Instance.MapDataStore;
            var mapDir = mapDataStore?.CurrentMapDirectory;
            if (string.IsNullOrEmpty(mapDir)) return;

            string texturesDir = Path.Combine(mapDir, "TilemapTextures");
            string fileName = TilemapChunk.TilemapId + ".raw";
            string filePath = Path.Combine(texturesDir, fileName);

            MapEditor.Utility.RawTextureUtility.SaveTexture(Texture, filePath);
        }

        /// <summary>
        /// 创建 SpriteRenderer 实例用于显示贴图。
        /// </summary>
        private void CreateSpriteRenderer()
        {
            if (Texture == null) return;
            GameObject go = new($"TilemapChunk_{Coord.X}_{Coord.Y}");

            int cs = TilemapChunk?.ChunkSize ?? Texture.width;
            float unitsPerPixel = 1f / MapEditorConfig.PixelsPerUnit;
            go.transform.position = new Vector3(Coord.X * cs * unitsPerPixel, Coord.Y * cs * unitsPerPixel, 0);
            go.transform.parent = layerRenderer.transform;
            SpriteRenderer = go.AddComponent<SpriteRenderer>();

            // 创建全像素精灵
            SpriteRenderer.sprite = Sprite.Create(Texture,
                                                  new Rect(0, 0, Texture.width, Texture.height),
                                                  new Vector2(0f, 0f),
                                                  MapEditorConfig.PixelsPerUnit,
                                                  0,
                                                  SpriteMeshType.FullRect);

            // 设置 SortingOrder，确保多 TilemapLayer 之间按照 MapLayer.Order 叠放
            if(layerRenderer is LayerRenderer lr)
            {
                SpriteRenderer.sortingOrder = lr.LayerOrder;
            }

            // 使用新的基于 Texture2DArray 的 SplatMixArrayLit 材质
            var shader = Shader.Find("MapEditor/SplatMixArrayLit");
            if (shader != null)
            {
                var mat = new Material(shader);
                SpriteRenderer.sharedMaterial = mat;
            }
            else
            {
                Debug.LogError("SplatMixArrayLit shader not found, fallback to default material");
            }

            propertyBlock = new MaterialPropertyBlock();

            // 根据 Chunk 中记录的贴图索引刷新材质槽位
            ApplySurfaceTextureMapping();
        }

        /// <summary>
        /// 将本 Chunk 的 <see cref="TilemapChunk.MaterialIndexTable"/> 推送到材质属性块。
        /// 绑定 Texture2DArray 资源，并设置 _IndexTable0/1 常量供 Shader 采样。
        /// </summary>
        public void ApplySurfaceTextureMapping()
        {
            if (SpriteRenderer == null || propertyBlock == null) return;

            // 1) 设置 TextureArray
            propertyBlock.SetTexture("_SurfaceArray", GroundTextureArrayProvider.SurfaceArray);

            // 2) 填充通道索引表
            int[] table = new int[7];
            for (int i = 0; i < 7; i++) table[i] = -1;
            if (TilemapChunk != null && TilemapChunk.MaterialIndexTable != null)
            {
                int count = Mathf.Min(TilemapChunk.MaterialIndexTable.Count, 7);
                for (int i = 0; i < count; i++)
                {
                    table[i] = TilemapChunk.MaterialIndexTable[i];
                }
            }

            // 旧的 TilemapLayer 索引表已废弃，不再尝试回退

            Vector4 indices0 = new(table[0], table[1], table[2], table[3]);
            Vector4 indices1 = new(table[4], table[5], table[6], -1);

            propertyBlock.SetVector("_IndexTable0", indices0);
            propertyBlock.SetVector("_IndexTable1", indices1);

            // 新增: 设置每个槽位的 uvScale (xy)。
            Vector4[] uvScales = new Vector4[7];
            for (int i = 0; i < 7; i++)
            {
                Vector2 scale = Vector2.one;
                if (table[i] >= 0)
                {
                    scale = GroundTextureArrayProvider.GetUVScale(table[i]);
                }
                uvScales[i] = new Vector4(scale.x, scale.y, 0f, 0f);
            }
            propertyBlock.SetVectorArray("_UVScales", uvScales);

            SpriteRenderer.SetPropertyBlock(propertyBlock);
        }

        /// <summary>
        /// 更新权重 RT 绑定，让渲染结果实时反映。
        /// </summary>
        public void UpdateWeightTextures(RenderTexture[] rts)
        {
            if (SpriteRenderer == null || propertyBlock == null || rts == null) return;
            propertyBlock.SetTexture("_Splat0", rts[0]);
            propertyBlock.SetTexture("_Splat1", rts[1]);
            SpriteRenderer.SetPropertyBlock(propertyBlock);
        }

        protected override void InternalRebuild(ChunkBase chunk)
        {
            
        }

        public override void Dispose()
        {
            if (SpriteRenderer != null)
            {
                Object.Destroy(SpriteRenderer.gameObject);
                SpriteRenderer = null;
            }

            if (Texture != null)
            {
                Object.Destroy(Texture);
                Texture = null;
            }

            TilemapChunk = null;
        }


        public override void SetVisibility(bool visible){
            if (SpriteRenderer != null)
            {
                SpriteRenderer.enabled = visible;
            }
        }

        internal void ApplyTextureChanges()
        {
            // 应用到 GPU
            if (Texture == null) return;
            Texture.Apply();
            _dirty = true;
        }

        /// <summary>
        /// 保存完成后由外部调用, 重置脏标记。
        /// </summary>
        public void MarkClean()
        {
            _dirty = false;
        }
    }
}