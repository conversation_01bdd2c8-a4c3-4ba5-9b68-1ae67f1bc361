# 如何实现一个新的图层类型

本文档将指导您如何在 MapEditor 项目中添加一种全新的图层类型。这需要创建数据结构、渲染器，并将其集成到现有的服务和工厂中。

## 1. 核心概念

- **`LayerType` 枚举**: 在 `Assets/MapEditor/Scripts/Core/Types/LayerType.cs` 中定义了所有可用的图层类型。添加新图层的第一步是在这里添加一个新的枚举值。
- **`MapLayer` (数据层)**: 这是一个抽象基类，定义了图层的数据结构。每个图层都由若干个 `Chunk` 组成。您需要创建一个继承自 `MapLayer` 的新类来定义您的图层数据。
- **`ChunkBase` (数据块)**: 图层数据的基本单元。您需要创建一个继承自 `ChunkBase` 的类来存储该图层在每个块内的具体数据。
- **`LayerRenderer` (渲染层)**: 这是一个基类，负责将 `MapLayer` 的数据可视化到 Unity 场景中。您需要创建一个新的渲染器类来处理新图层的渲染逻辑。
- **`ChunkRenderProxy` (渲染代理)**: 在场景中实际代表一个 `Chunk` 的 `GameObject`。您的 `LayerRenderer` 将会创建和管理这些代理。
- **工厂模式(强制)**: 所有 `Chunk` 工厂必须在 `Assets/MapEditor/Scripts/Manager/MapEditorStarter.cs` 的 `RegisterChunkFactories` 方法中集中注册，严禁在其他地方（包括静态构造函数）调用 `RegisterChunkFactory`。

## 2. 开发流程

以下是创建一个新的"高度图层" (`HeightLayer`) 的完整流程，您可以此为模板。

### 第 1 步：定义图层类型

打开 `Assets/MapEditor/Scripts/Core/Types/LayerType.cs` 文件，在枚举中添加您的新图层类型。

```csharp
public enum LayerType
{
    Ground,     // 地表层
    Height,     // 高度层 (新添加)
    Object,     // 对象层
    // ... 其他图层
}
```

### 第 2 步：创建数据结构

#### 2.1. 创建 Chunk 数据类

在 `Assets/MapEditor/Scripts/Data/Chunks/` 目录下创建一个新的 C# 脚本，用于定义该图层每个 `Chunk` 存储的数据。这个类必须继承自 `ChunkBase`。

```csharp
// In Assets/MapEditor/Scripts/Data/Chunks/HeightChunk.cs
using MapEditor.Core;
using UnityEngine;

namespace MapEditor.Data.Chunks
{
    [System.Serializable]
    public class HeightChunk : ChunkBase
    {
        // 定义此 Chunk 需要序列化和存储的数据
        [SerializeField] private string heightMapId; // 例如，存储高度图的文件名或 ID

        public string HeightMapId { get => heightMapId; set => heightMapId = value; }

        public HeightChunk(ChunkCoord coord, int chunkSize) : base(coord)
        {
            // 初始化 Chunk 数据
            heightMapId = System.Guid.NewGuid().ToString();
        }
    }
}
```

#### 2.2. 创建 Layer 数据类

在 `Assets/MapEditor/Scripts/Data/Layers/` 目录下创建一个新的 C# 脚本，继承自 `MapLayer`。这个类代表了您的新图层。

```csharp
// In Assets/MapEditor/Scripts/Data/Layers/HeightLayer.cs
using MapEditor.Core;
using MapEditor.Data.Chunks;

namespace MapEditor.Data.Layers
{
    [System.Serializable]
    public class HeightLayer : MapLayer
    {
        // 指定图层类型
        public override LayerType Type => LayerType.Height;

        // 可选：在构造函数中设置一个合理的默认排序值
        public HeightLayer()
        {
            Order = -5; // 位于地表层(-10)与对象层(0)之间
        }
    }
}
```

### 第 3 步：创建渲染器

在 `Assets/MapEditor/Scripts/Rendering/Layers/` 目录下创建一个新的 C# 脚本，继承自 `LayerRenderer`。这个类将负责把 `HeightLayer` 的数据渲染到屏幕上。

```csharp
// In Assets/MapEditor/Scripts/Rendering/Layers/HeightLayerRenderer.cs
using System.Collections.Generic;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using MapEditor.Rendering.RenderProxy;
using UnityEngine;

namespace MapEditor.Rendering.Layers
{
    public class HeightLayerRenderer : LayerRenderer
    {
        // 存储每个 Chunk 对应的渲染资源，例如 RenderTexture 或 Mesh
        private readonly Dictionary<ChunkCoord, RenderTexture> chunkHeightRTs = new();

        public override void Initialize(IMapLayer layer)
        {
            base.Initialize(layer);
            // TODO: 初始化渲染器所需的资源，如 Shader / Material
        }

        // 创建或更新 Chunk 的渲染代理
        protected override ChunkRenderProxy CreateProxy(ChunkCoord coord, ChunkBase chunk)
        {
            if (chunk is HeightChunk hc)
            {
                // 创建一个自定义的 RenderProxy 来处理渲染
                var proxy = new HeightChunkRenderProxy(coord, hc, this);
                // TODO: 更新 proxy 的材质、网格等
                return proxy;
            }
            return null;
        }

        // 提供给工具或其他服务调用的接口，用于修改渲染数据
        public void DrawBrushGPU(Vector2 worldPos, HeightBrushSettings settings)
        {
            // TODO: 通过 ComputeShader 或 Shader Graph 修改 RenderTexture
        }
    }
}
```

#### 3.1. 创建 `HeightChunkRenderProxy`

与现有 `TilemapChunkRenderProxy`、`ObjectChunkRenderProxy` 类似，您需要在 `Assets/MapEditor/Scripts/Rendering/RenderProxy/` 目录下创建一个渲染代理，用于把 `HeightChunk` 的数据映射到场景中的 `GameObject` 或 `Mesh`。

```csharp
// In Assets/MapEditor/Scripts/Rendering/RenderProxy/HeightChunkRenderProxy.cs
using MapEditor.Core;
using MapEditor.Data.Chunks;
using UnityEngine;

namespace MapEditor.Rendering.RenderProxy
{
    public class HeightChunkRenderProxy : ChunkRenderProxy
    {
        private readonly HeightChunk heightChunk;
        private readonly HeightLayerRenderer renderer;

        public HeightChunkRenderProxy(ChunkCoord coord, HeightChunk chunk, HeightLayerRenderer renderer) : base(coord)
        {
            heightChunk = chunk;
            this.renderer = renderer;
            BuildVisual();
        }

        private void BuildVisual()
        {
            // TODO: 根据高度数据生成 Mesh 或更新 Material
        }

        public override void Dispose()
        {
            base.Dispose();
            // TODO: 释放临时资源
        }
    }
}
```

### 第 4 步：集成到系统中

#### 4.1. 更新 `MapEditorStarter`

打开 `Assets/MapEditor/Scripts/Manager/MapEditorStarter.cs`。在 `RegisterChunkFactories` 方法中，需要确保您的新图层类被加载，以便其静态构造函数能够执行。最简单的方法是添加一行对新 Chunk 类的引用。

```csharp
// In MapEditorStarter.cs, inside RegisterChunkFactories()
private void RegisterChunkFactories()
{
    // ... 其他工厂注册 ...
    MapLayer.RegisterChunkFactory<MapEditor.Data.Chunks.HeightChunk>((coord, size) => new MapEditor.Data.Chunks.HeightChunk(coord, size));
}
```
*（注意：在当前项目中，`HeightLayer` 的静态构造函数已经处理了注册，但对于新图层，在此处显式注册是更稳妥的做法，可以确保在需要时工厂已准备就绪。）*

#### 4.2. 更新 `SceneRenderManager`

打开 `Assets/MapEditor/Scripts/Manager/SceneRenderManager.cs`。在 `OnLayerRenderRequest` 事件处理器中，添加创建新图层渲染器的逻辑。

```csharp
// In SceneRenderManager.cs, inside OnLayerRenderRequest()
private void OnLayerRenderRequest(LayerRenderRequestEvent evt)
{
    switch (evt.Action)
    {
        case LayerRenderAction.Create:
            if (evt.Layer != null)
            {
                // ... 其他图层类型 ...
                else if (evt.Layer.Type == LayerType.Height) CreateHeightRendererInternal(evt.Layer);

                // ...
            }
            break;
        // ...
    }
}

// 添加创建新渲染器的私有方法
private void CreateHeightRendererInternal(IMapLayer mapLayer)
{
    if (groundLayerContainer == null) InitializeLayerContainers();

    var go = new GameObject(mapLayer.LayerName);
    var heightRenderer = go.AddComponent<MapEditor.Rendering.Layers.HeightLayerRenderer>();

    heightRenderer.Initialize(mapLayer);
    go.transform.SetParent(groundLayerContainer); // 选择一个合适的容器
    layerRenderers[RenderLayer.Ground].Add(heightRenderer); // 添加到渲染器列表
}
```

#### 4.3. 更新 `MapService`

如果加载旧地图时需要特殊处理（例如，从旧格式迁移数据），您可能需要在 `MapService.HandleLoadedMap` 中添加相应的逻辑。

## 5. 总结

创建一个新图层类型的流程可以归纳为：

1.  **定义** 新的 `LayerType` 枚举值。
2.  **创建** `ChunkBase` 和 `MapLayer` 的派生类来定义数据结构。
3.  **注册** 新 `Chunk` 的工厂方法。
4.  **创建** `LayerRenderer` 的派生类来实现渲染逻辑。
5.  **集成** 新的渲染器到 `SceneRenderManager` 的创建流程中。
