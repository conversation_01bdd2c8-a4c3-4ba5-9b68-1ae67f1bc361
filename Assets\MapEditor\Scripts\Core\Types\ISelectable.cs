using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 可选择对象接口，所有可被选择的对象都需要实现此接口
    /// </summary>
    public interface ISelectable
    {
        /// <summary>
        /// 对象的唯一标识符
        /// </summary>
        string Id { get; }
        
        /// <summary>
        /// 对象的显示名称
        /// </summary>
        string DisplayName { get; }
        
        /// <summary>
        /// 对象的位置
        /// </summary>
        Vector2 Position { get; set; }
        
        /// <summary>
        /// 对象的旋转角度（度）
        /// </summary>
        float Rotation { get; set; }
        
        /// <summary>
        /// 对象的缩放
        /// </summary>
        Vector2 Scale { get; set; }
        
        /// <summary>
        /// 获取对象的包围框
        /// </summary>
        Rect GetBounds();
        
        /// <summary>
        /// 对象是否可见
        /// </summary>
        bool IsVisible { get; }
        
        /// <summary>
        /// 对象是否被锁定（不可编辑）
        /// </summary>
        bool IsLocked { get; }
        
        /// <summary>
        /// 对象所属的图层ID
        /// </summary>
        string LayerId { get; }
        
        /// <summary>
        /// 获取对象类型
        /// </summary>
        string ObjectType { get; }
        
        /// <summary>
        /// 设置选中状态
        /// </summary>
        void SetSelected(bool selected);
        
        /// <summary>
        /// 获取选中状态
        /// </summary>
        bool IsSelected { get; }
    }
} 