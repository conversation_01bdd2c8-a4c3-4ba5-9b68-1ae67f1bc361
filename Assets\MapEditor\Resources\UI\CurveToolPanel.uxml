<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" editor-extension-mode="False">
    <ui:VisualElement name="CurveToolPanel" style="background-color: rgba(0, 0, 0, 0); padding-left: 8px; padding-right: 8px; padding-top: 8px; padding-bottom: 8px;">
        <ui:Label text="曲线工具" display-tooltip-when-elided="true" name="TitleLabel" class="section-title" style="font-size: 14px; -unity-font-style: bold; margin-bottom: 8px;" />
        
        <!-- 曲线类型选择 -->
        <ui:VisualElement name="CurveTypeSection" style="margin-bottom: 12px;">
            <ui:Label text="曲线类型" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px;" />
            <ui:DropdownField name="CurveTypeDropdown" index="1" choices="直线,Catmull-Rom样条,贝塞尔曲线" />
        </ui:VisualElement>
        
        <!-- 描边宽度 -->
        <ui:VisualElement name="WidthSection" style="margin-bottom: 12px;">
            <ui:Label text="描边宽度" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px;" />
            <ui:VisualElement style="flex-direction: row; align-items: center;">
                <ui:Slider picking-mode="Ignore" value="2" high-value="10" name="WidthSlider" low-value="0.1" style="flex-grow: 1; margin-right: 8px;" />
                <ui:FloatField value="2" name="WidthInput" style="width: 60px;" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 边缘柔和度 -->
        <ui:VisualElement name="SoftnessSection" style="margin-bottom: 12px;">
            <ui:Label text="边缘柔和度" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px;" />
            <ui:VisualElement style="flex-direction: row; align-items: center;">
                <ui:Slider picking-mode="Ignore" value="0.5" high-value="1" name="SoftnessSlider" low-value="0" style="flex-grow: 1; margin-right: 8px;" />
                <ui:FloatField value="0.5" name="SoftnessInput" style="width: 60px;" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 绘制强度 -->
        <ui:VisualElement name="StrengthSection" style="margin-bottom: 12px;">
            <ui:Label text="绘制强度" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px;" />
            <ui:VisualElement style="flex-direction: row; align-items: center;">
                <ui:Slider picking-mode="Ignore" value="1" high-value="1" name="StrengthSlider" low-value="0" style="flex-grow: 1; margin-right: 8px;" />
                <ui:FloatField value="1" name="StrengthInput" style="width: 60px;" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 曲线分辨率 -->
        <ui:VisualElement name="ResolutionSection" style="margin-bottom: 12px;">
            <ui:Label text="曲线分辨率" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px;" />
            <ui:VisualElement style="flex-direction: row; align-items: center;">
                <ui:Slider picking-mode="Ignore" value="10" high-value="50" name="ResolutionSlider" low-value="2" style="flex-grow: 1; margin-right: 8px;" />
                <ui:FloatField value="10" name="ResolutionInput" style="width: 60px;" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- 材质选择 -->
        <ui:VisualElement name="MaterialSection" style="margin-bottom: 12px;">
            <ui:Label text="材质选择" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px;" />
            <ui:ScrollView name="MaterialGrid" style="height: 120px; background-color: rgba(255, 255, 255, 0.05); border-left-color: rgba(255, 255, 255, 0.1); border-right-color: rgba(255, 255, 255, 0.1); border-top-color: rgba(255, 255, 255, 0.1); border-bottom-color: rgba(255, 255, 255, 0.1); border-left-width: 1px; border-right-width: 1px; border-top-width: 1px; border-bottom-width: 1px; border-top-left-radius: 4px; border-bottom-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; padding-left: 4px; padding-right: 4px; padding-top: 4px; padding-bottom: 4px;">
                <ui:VisualElement name="MaterialContainer" style="flex-direction: row; flex-wrap: wrap;" />
            </ui:ScrollView>
        </ui:VisualElement>
        
        <!-- 操作按钮 -->
        <ui:VisualElement name="ActionsSection" style="margin-top: 16px;">
            <ui:Button name="RenderButton" text="渲染到图层" style="height: 30px; font-size: 14px;" />
        </ui:VisualElement>

        <!-- 操作说明 -->
        <ui:VisualElement name="InstructionsSection" style="margin-top: 16px; padding-left: 8px; padding-right: 8px; padding-top: 8px; padding-bottom: 8px; background-color: rgba(255, 255, 255, 0.05); border-top-left-radius: 4px; border-bottom-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px;">
            <ui:Label text="操作说明" display-tooltip-when-elided="true" class="property-label" style="margin-bottom: 4px; -unity-font-style: bold;" />
            <ui:Label text="• 右键点击添加控制点" display-tooltip-when-elided="true" style="font-size: 11px; margin-bottom: 2px;" />
            <ui:Label text="• 左键拖拽移动控制点" display-tooltip-when-elided="true" style="font-size: 11px; margin-bottom: 2px;" />
            <ui:Label text="• 选中控制点后按Delete键删除" display-tooltip-when-elided="true" style="font-size: 11px; margin-bottom: 2px;" />
            <ui:Label text="• ESC键取消操作" display-tooltip-when-elided="true" style="font-size: 11px;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML> 