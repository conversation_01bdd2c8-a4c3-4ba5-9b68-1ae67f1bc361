using UnityEngine;
using MapEditor.Core;
using MapEditor.Rendering.Layers;
using MapEditor.Tools;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.Tools
{
    /// <summary>
    /// 对象绘制工具，专门用于在场景中放置对象
    /// </summary>
    public class ObjectToolPaint : MapToolBase
    {
        private string selectedPrefabGuid;
        private Vector2 paintScale = Vector2.one;
        private float paintRotation = 0f;
        private GameObject previewObject;
        private bool isMouseDown = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ObjectToolPaint(IMapEditorCore editorCore)
            : base("ObjectToolPaint", "对象绘制", editorCore, new[] { LayerType.Object, LayerType.Decoration })
        {
   
        }
        
        /// <summary>
        /// 选中的预制体GUID
        /// </summary>
        public string SelectedPrefabGuid
        {
            get => selectedPrefabGuid;
            set
            {
                if (selectedPrefabGuid != value)
                {
                    selectedPrefabGuid = value;
                    ClearPreview();
                }
            }
        }
        
        /// <summary>
        /// 绘制时的缩放
        /// </summary>
        public Vector2 PaintScale
        {
            get => paintScale;
            set => paintScale = value;
        }
        
        /// <summary>
        /// 绘制时的旋转
        /// </summary>
        public float PaintRotation
        {
            get => paintRotation;
            set => paintRotation = value;
        }
        
        private string GetResourcesPath(string guid)
        {
            if (string.IsNullOrEmpty(guid)) return string.Empty;
            return guid.StartsWith("ObjectPrefabs/") ? guid : $"ObjectPrefabs/{guid}";
        }
        
        public override void OnSceneInput(InputContext context)
        {
            if (string.IsNullOrEmpty(selectedPrefabGuid))
            {
                PublishToolStatusChanged("请先选择要绘制的预制体");
                return;
            }

            // ===== 绘制逻辑 =====
            // 仅在本次帧检测到"左键第一次按下"时才绘制一次。
            if (context.IsLeftMouseDown && !isMouseDown)
            {
                isMouseDown = true;

                Vector2 worldPosition = context.WorldPosition;

                var objectRenderer = GetActiveObjectRenderer();
                if (objectRenderer != null)
                {
                    string resourcePath = GetResourcesPath(selectedPrefabGuid);
                    var newObject = objectRenderer.AddObjectAt(worldPosition, resourcePath, paintRotation, paintScale);
                    if (newObject != null)
                    {
                        PublishToolStatusChanged($"绘制对象: {newObject.InstanceId}");

                        // 1) 通知其他系统已新增对象
                        PublishEvent(new ObjectCreatedEvent
                        {
                            Instance = newObject,
                            LayerId = layerManager.ActiveLayer.LayerId
                        });

                        // 2) 触发地图数据变更事件，驱动渲染层刷新/持久化
                        PublishEvent(new MapDataChangedEvent
                        {
                            ChangeType = MapDataChangeType.ObjectAdded,
                            LayerId = layerManager.ActiveLayer.LayerId,
                            AffectedArea = new Rect(worldPosition, Vector2.one)
                        });
                    }
                }
                else
                {
                    PublishToolStatusChanged("请先选择或创建一个对象层");
                }
            }
            else if (!context.IsLeftMouseDown && isMouseDown)
            {
                // 鼠标释放，重置状态
                isMouseDown = false;
            }
        }
        
        public override void UpdatePreview()
        {
            if (!string.IsNullOrEmpty(selectedPrefabGuid))
            {
                UpdatePaintPreview();
            }
            else
            {
                ClearPreview();
            }
        }
        
        public override void OnActivate()
        {
            base.OnActivate();
            
            // 确保当前层是对象层
            var activeLayer = layerManager?.ActiveLayer;
            if (activeLayer?.Type != LayerType.Object && activeLayer?.Type != LayerType.Decoration)
            {
                PublishToolStatusChanged("警告: 当前活动层不是对象层或装饰层");
            }
        }
        
        public override void OnDeactivate()
        {
            base.OnDeactivate();
            ClearPreview();
        }
        
        private ObjectLayerRenderer GetActiveObjectRenderer()
        {
            var activeLayer = layerManager?.ActiveLayer;
            if (activeLayer?.Type == LayerType.Object || activeLayer?.Type == LayerType.Decoration)
            {
                var layerRenderService = editorCore?.GetService<LayerRenderService>();
                var renderer = layerRenderService?.GetRenderer(activeLayer);
                return renderer as ObjectLayerRenderer;
            }
            return null;
        }
        
        private void UpdatePaintPreview()
        {
            EnsurePreviewObject();
            
            if (previewObject != null)
            {
                // 更新预览对象位置
                Vector2 mouseWorldPos = GetMouseWorldPosition();
                previewObject.transform.position = new Vector3(mouseWorldPos.x, mouseWorldPos.y, 0);
                previewObject.transform.rotation = Quaternion.Euler(0, 0, paintRotation);
                previewObject.transform.localScale = new Vector3(paintScale.x, paintScale.y, 1);
                
                // 设置预览透明度
                SetObjectAlpha(previewObject, 0.5f);
            }
        }
        
        private void EnsurePreviewObject()
        {
            if (previewObject == null && !string.IsNullOrEmpty(selectedPrefabGuid))
            {
                // 加载预制体
                string prefabPath = GetResourcesPath(selectedPrefabGuid);
                GameObject prefab = Resources.Load<GameObject>(prefabPath);
                
                if (prefab != null)
                {
                    previewObject = GameObject.Instantiate(prefab);
                    previewObject.name = "PreviewObject";
                    
                    // 禁用碰撞体
                    var colliders = previewObject.GetComponentsInChildren<Collider2D>();
                    foreach (var collider in colliders)
                    {
                        collider.enabled = false;
                    }
                }
            }
        }
        
        private void SetObjectAlpha(GameObject obj, float alpha)
        {
            var renderers = obj.GetComponentsInChildren<SpriteRenderer>();
            foreach (var renderer in renderers)
            {
                Color color = renderer.color;
                color.a = alpha;
                renderer.color = color;
            }
        }
        
        private void ClearPreview()
        {
            if (previewObject != null)
            {
                GameObject.Destroy(previewObject);
                previewObject = null;
            }
        }
        
        private Vector2 GetMouseWorldPosition()
        {
            Camera camera = editorCore.SceneRenderer.RenderCamera;
            Vector3 mousePos = Input.mousePosition;
            mousePos.z = -camera.transform.position.z;
            return camera.ScreenToWorldPoint(mousePos);
        }
    }
    

} 