using MapEditor.Core;
using UnityEngine;

namespace MapEditor.Event
{
    /// <summary>
    /// 地图数据改变事件
    /// </summary>
    public struct MapDataChangedEvent
    {
        /// <summary>
        /// 改变类型
        /// </summary>
        public MapDataChangeType ChangeType;

        /// <summary>
        /// 受影响的图层ID
        /// </summary>
        public string LayerId;

        /// <summary>
        /// 修改区域
        /// </summary>
        public Rect AffectedArea;
    }

    /// <summary>
    /// 地图创建事件
    /// </summary>
    public class MapCreatedEvent
    {
        public IMapData MapData { get; set; }
    }

    /// <summary>
    /// 地图加载事件
    /// </summary>
    public class MapLoadedEvent
    {
        public IMapData MapData { get; set; }
    }

    /// <summary>
    /// 地图保存事件
    /// </summary>
    public class MapSavedEvent
    {
        public IMapData MapData { get; set; }
        public string SavePath { get; set; }
    }

    /// <summary>
    /// 地图另存为事件
    /// </summary>
    public class MapSavedAsEvent
    {
        public IMapData MapData { get; set; }
        public string OriginalMapId { get; set; }
    }

    /// <summary>
    /// 地图关闭事件
    /// </summary>
    public class MapClosedEvent
    {
        public string MapId { get; set; }
    }

    /// <summary>
    /// 地图修改事件
    /// </summary>
    public class MapChangedEvent
    {
        public IMapData MapData { get; set; }
    }
} 