using System;
using System.Collections.Generic;
using UnityEngine;


namespace MapEditor.Core
{
    /// <summary>
    /// 地图数据实现类，实现IMapData接口
    /// </summary>
    [Serializable]
    public class MapData : IMapData
    {
        [SerializeField] private string mapId;
        [SerializeField] private string mapName;
        [SerializeField] private Vector2Int mapSize;
        [SerializeField] private string creationTimeStr;
        [SerializeField] private string lastModifiedTimeStr;
        [SerializeField] private int chunkSize = 256;
        [SerializeField] private Vector2 gridCellSize = Vector2.one;
        [SerializeReference] private List<IMapLayer> layers = new ();
        
        private Dictionary<string, IMapLayer> layerLookup = new ();
        
        public string MapId => mapId;
        public string MapName 
        { 
            get => mapName;
            set 
            {
                mapName = value;
                LastModifiedTime = DateTime.Now;
            }
        }
        public Vector2Int MapSize => mapSize;
        
        /// <summary>
        /// 网格单元尺寸（世界单位）
        /// </summary>
        public Vector2 GridCellSize 
        { 
            get => gridCellSize; 
            set 
            { 
                gridCellSize = value; 
                LastModifiedTime = DateTime.Now; 
            } 
        }
        
        public DateTime CreationTime 
        { 
            get 
            {
                if (DateTime.TryParse(creationTimeStr, out DateTime result))
                {
                    return result;
                }
                return DateTime.Now;
            }
            private set => creationTimeStr = value.ToString("o");
        }
        
        public DateTime LastModifiedTime 
        { 
            get 
            {
                if (DateTime.TryParse(lastModifiedTimeStr, out DateTime result))
                {
                    return result;
                }
                return DateTime.Now;
            }
            set => lastModifiedTimeStr = value.ToString("o");
        }
        
        /// <summary>
        /// 创建地图时的 ChunkSize。
        /// </summary>
        public int ChunkSize => chunkSize;
        
        /// <summary>
        /// 创建新地图数据
        /// </summary>
        public MapData(string name, Vector2Int size) : this(name, size, 256) { }

        public MapData(string name, Vector2Int size, int chunkSize)
        {
            mapId = Guid.NewGuid().ToString();
            mapName = name;
            mapSize = size;
            this.chunkSize = Mathf.Max(1, chunkSize);
            CreationTime = DateTime.Now;
            LastModifiedTime = DateTime.Now;
            
        }
        

        
        /// <summary>
        /// 初始化图层查找字典
        /// </summary>
        public void Initialize()
        {
            layerLookup.Clear();
            foreach (var layer in layers)
            {
                layerLookup[layer.LayerId] = layer;

                // 兼容旧数据：如果图层的 ChunkSize 未初始化(<=0)，使用地图的 chunkSize。
                if (layer is MapLayer ml && ml.ChunkSize <= 0)
                {
                    ml.SetChunkSize(chunkSize);
                }
            }
        }

        public IEnumerable<IMapLayer> GetAllLayers()
        {
            return layers;
        }

        public IMapLayer GetLayer(string layerId)
        {
            if (layerLookup.TryGetValue(layerId, out var layer))
            {
                return layer;
            }
            return null;
        }

        public void AddLayer(IMapLayer layer)
        {
            if (layer == null) return;
            
            // 如果已存在相同ID的图层，则先移除
            layers.RemoveAll(l => l.LayerId == layer.LayerId);
            
            layers.Add(layer);
            layerLookup[layer.LayerId] = layer;
            
            // 确保图层顺序正确
            SortLayers();
            
            LastModifiedTime = DateTime.Now;
        }

        public void RemoveLayer(string layerId)
        {
            if (string.IsNullOrEmpty(layerId)) return;
            
            layers.RemoveAll(l => l.LayerId == layerId);
            layerLookup.Remove(layerId);
            
            LastModifiedTime = DateTime.Now;
        }
        
        /// <summary>
        /// 排序图层
        /// </summary>
        private void SortLayers()
        {
            layers.Sort((a, b) => a.Order.CompareTo(b.Order));
        }
    }
} 