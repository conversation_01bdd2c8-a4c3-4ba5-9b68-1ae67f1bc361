namespace MapEditor.Event
{

    /// <summary>
    /// 工具取消事件
    /// </summary>
    public struct ToolCancelEvent
    {
        // 事件数据
    }

    /// <summary>
    /// 工具快捷键事件
    /// </summary>
    public struct ToolShortcutEvent
    {
        /// <summary>
        /// 快捷键索引（0-8，对应数字键1-9）
        /// </summary>
        public int ShortcutIndex;
    }

    /// <summary>
    /// 画笔大小快捷键事件
    /// </summary>
    public struct BrushSizeShortcutEvent
    {
        /// <summary>
        /// 方向（-1表示减小，1表示增大）
        /// </summary>
        public int Direction;
    }

    /// <summary>
    /// Delete键事件
    /// </summary>
    public struct DeleteKeyEvent
    {
        // 事件数据
    }

}