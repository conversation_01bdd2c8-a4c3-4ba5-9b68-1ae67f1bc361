using System.Collections.Generic;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Data.Chunks;

namespace MapEditor.Rendering.RenderProxy
{
    /// <summary>
    /// 对象Chunk渲染代理，负责在场景中生成和管理该Chunk内的GameObject实例
    /// </summary>
    public class ObjectChunkRenderProxy : ChunkRenderProxy
    {
        // 存储实例ID到GameObject的映射
        private Dictionary<string, GameObject> instanceObjects = new Dictionary<string, GameObject>();
        
        // 父容器GameObject
        private GameObject chunkContainer;
        
        // 对象Chunk数据引用
        private ObjectChunk objectChunk;

        public ObjectChunkRenderProxy(ChunkCoord coord, ObjectChunk chunk, LayerRenderer layerRenderer) 
            : base(coord, layerRenderer)
        {
            objectChunk = chunk;
            CreateChunkContainer();
        }

        /// <summary>
        /// 创建Chunk容器GameObject
        /// </summary>
        private void CreateChunkContainer()
        {
            chunkContainer = new GameObject($"ObjectChunk_{Coord.X}_{Coord.Y}");
            chunkContainer.transform.SetParent(layerRenderer.transform);
            
            // 设置容器位置
            int chunkSize = layerRenderer?.LinkedLayer?.ChunkSize ?? 256;
            float unitsPerPixel = 1f / MapEditorConfig.PixelsPerUnit;
            Vector3 chunkWorldPos = new Vector3(
                Coord.X * chunkSize * unitsPerPixel, 
                Coord.Y * chunkSize * unitsPerPixel, 
                0
            );
            chunkContainer.transform.position = chunkWorldPos;
        }

        /// <summary>
        /// 重建渲染内容
        /// </summary>
        protected override void InternalRebuild(ChunkBase chunk)
        {
            if (!(chunk is ObjectChunk objChunk))
                return;

            objectChunk = objChunk;

            // 清理现有实例
            ClearAllInstances();

            // 重新创建所有对象实例
            foreach (var instance in objChunk.Objects)
            {
                CreateGameObjectForInstance(instance);
            }
        }

        /// <summary>
        /// 为对象实例创建GameObject
        /// </summary>
        private void CreateGameObjectForInstance(ObjectInstance instance)
        {
            // 从预制体GUID加载预制体
            GameObject prefab = LoadPrefabFromGuid(instance.PrefabGuid);
            if (prefab == null)
            {
                Debug.LogWarning($"无法加载预制体: {instance.PrefabGuid}");
                return;
            }

            // 实例化GameObject
            GameObject gameObject = Object.Instantiate(prefab, chunkContainer.transform);
            gameObject.name = $"{prefab.name}_{instance.InstanceId}";

            // 计算 Z 深度：Y 越小 → Z 越大（靠前）
            float depth = instance.Position.y * MapEditorConfig.YSortDepthFactor;

            // 设置变换
            gameObject.transform.position = new Vector3(instance.Position.x, instance.Position.y, depth);
            gameObject.transform.rotation = Quaternion.Euler(0, 0, instance.Rotation);
            gameObject.transform.localScale = new Vector3(instance.Scale.x, instance.Scale.y, 1);

            // 统一 SpriteRenderer 的 sortingOrder = LayerOrder
            if (layerRenderer is MapEditor.Core.LayerRenderer lr)
            {
                foreach (var sr in gameObject.GetComponentsInChildren<SpriteRenderer>(true))
                {
                    sr.sortingOrder = lr.LayerOrder;
                }
            }

            // 添加对象标识组件
            var identifier = gameObject.GetComponent<ObjectIdentifier>();
            if (identifier == null)
            {
                identifier = gameObject.AddComponent<ObjectIdentifier>();
            }
            identifier.InstanceId = instance.InstanceId;
            identifier.ChunkCoord = Coord;

            // 添加到映射表
            instanceObjects[instance.InstanceId] = gameObject;
        }

        /// <summary>
        /// 从GUID加载预制体
        /// </summary>
        private GameObject LoadPrefabFromGuid(string prefabGuid)
        {
            // prefabGuid现在包含完整的资源路径
            GameObject prefab = Resources.Load<GameObject>(prefabGuid);
            if (prefab != null)
            {
                Debug.Log($"成功加载预制体: {prefabGuid}");
                return prefab;
            }
            
            Debug.LogError($"无法加载预制体: {prefabGuid}");
            return null;
        }

        /// <summary>
        /// 清理所有实例
        /// </summary>
        private void ClearAllInstances()
        {
            foreach (var kvp in instanceObjects)
            {
                if (kvp.Value != null)
                {
                    Object.Destroy(kvp.Value);
                }
            }
            instanceObjects.Clear();
        }

        /// <summary>
        /// 添加新对象实例
        /// </summary>
        public void AddObjectInstance(ObjectInstance instance)
        {
            if (instanceObjects.ContainsKey(instance.InstanceId))
                return;

            CreateGameObjectForInstance(instance);
        }

        /// <summary>
        /// 移除对象实例
        /// </summary>
        public void RemoveObjectInstance(string instanceId)
        {
            if (instanceObjects.TryGetValue(instanceId, out GameObject gameObject))
            {
                Object.Destroy(gameObject);
                instanceObjects.Remove(instanceId);
            }
        }

        /// <summary>
        /// 更新对象实例的变换
        /// </summary>
        public void UpdateObjectInstance(ObjectInstance instance)
        {
            if (instanceObjects.TryGetValue(instance.InstanceId, out GameObject gameObject))
            {
                float depth = instance.Position.y * MapEditorConfig.YSortDepthFactor;
                gameObject.transform.position = new Vector3(instance.Position.x, instance.Position.y, depth);
                gameObject.transform.rotation = Quaternion.Euler(0, 0, instance.Rotation);
                gameObject.transform.localScale = new Vector3(instance.Scale.x, instance.Scale.y, 1);
            }
        }

        /// <summary>
        /// 获取指定实例的GameObject
        /// </summary>
        public GameObject GetGameObject(string instanceId)
        {
            instanceObjects.TryGetValue(instanceId, out GameObject gameObject);
            return gameObject;
        }

        /// <summary>
        /// 尝试获取指定实例的GameObject
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="gameObject">输出的GameObject</param>
        /// <returns>是否成功获取</returns>
        public bool TryGetGameObject(string instanceId, out GameObject gameObject)
        {
            return instanceObjects.TryGetValue(instanceId, out gameObject);
        }

        /// <summary>
        /// 获取所有GameObject
        /// </summary>
        public Dictionary<string, GameObject> GetAllGameObjects()
        {
            return new Dictionary<string, GameObject>(instanceObjects);
        }

        public override void Dispose()
        {
            ClearAllInstances();
            
            if (chunkContainer != null)
            {
                Object.Destroy(chunkContainer);
                chunkContainer = null;
            }
            
            objectChunk = null;
        }

        public override void SetVisibility(bool visible)
        {
            if (chunkContainer != null)
            {
                chunkContainer.SetActive(visible);
            }
        }
    }
} 