using System;
using System.Linq;
using UnityEngine;
using MapEditor.Core;
using System.Collections.Generic;
using MapEditor.Event;

namespace MapEditor.Data
{
    /// <summary>
    /// 图层管理器实现类，负责管理当前活动图层和图层操作
    /// </summary>
    public class LayerDataStore : ILayerDataStore
    {
        private IMapLayer activeLayer;
        private IMapDataStore dataManager;
        private IEventSystem eventSystem;
        
        public IMapLayer ActiveLayer 
        { 
            get => activeLayer;
            set
            {
                if (activeLayer != value)
                {
                    var oldLayer = activeLayer;
                    activeLayer = value;
                    OnActiveLayerChanged?.Invoke(activeLayer);
                    
                    // 发布图层切换事件
                    eventSystem?.Publish(new ActiveLayerChangedEvent
                    {
                        OldLayer = oldLayer,
                        NewLayer = activeLayer
                    });
                    
                    Debug.Log($"活动图层切换到: {activeLayer?.LayerName ?? "无"}");
                }
            }
        }
        
        public event Action<IMapLayer> OnActiveLayerChanged;
        public event Action<IMapLayer> OnLayerAdded;
        public event Action<string> OnLayerRemoved;
        
        public LayerDataStore(IMapDataStore dataManager, IEventSystem eventSystem)
        {
            this.dataManager = dataManager;
            this.eventSystem = eventSystem;
            
            // 监听地图创建事件，自动设置默认活动图层
            eventSystem.Subscribe<MapCreatedEvent>(OnMapCreated);
        }
        
        /// <summary>
        /// 为指定图层类型分配 SortingOrder
        /// </summary>
        private int AllocateOrder(LayerType type)
        {
            // 使用统一的 SortingOrder 配置获取区间
            (int min, int max) range = MapEditor.Config.SortingOrderConfig.GetRange(type);

            if (range.min == 0 && range.max == 0)
            {
                Debug.LogWarning($"图层类型 {type} 不支持自动分配 SortingOrder");
                return 0;
            }

            var used = dataManager.CurrentMap.GetAllLayers()
                .Where(l => l.Type == type)
                .Select(l => l.Order)
                .ToHashSet();

            for (int o = range.min; o <= range.max; o++)
            {
                if (!used.Contains(o))
                    return o;
            }

            Debug.LogError($"{type} SortingOrder 区间已满！");
            return range.max; // 极限回退
        }
        
        private void OnMapCreated(MapCreatedEvent evt)
        {
            // 地图创建后，自动设置第一个地表图层为活动图层
            var groundLayer = evt.MapData.GetAllLayers().FirstOrDefault(l => l.Type == LayerType.Ground);
            if (groundLayer != null)
            {
                SetActiveLayer(groundLayer.LayerId);
            }
        }
        
        public void SetActiveLayer(string layerId)
        {
            if (dataManager.CurrentMap == null)
            {
                Debug.LogWarning("没有当前地图，无法设置活动图层");
                return;
            }
            
            var layer = dataManager.CurrentMap.GetLayer(layerId);
            if (layer == null)
            {
                Debug.LogWarning($"找不到ID为 {layerId} 的图层");
                return;
            }
            
            ActiveLayer = layer;
        }
        
        public IMapLayer CreateLayer(string name, LayerType type)
        {
            if (dataManager.CurrentMap == null)
            {
                Debug.LogError("没有当前地图，无法创建图层");
                return null;
            }
            
            // 检查重名
            if (dataManager.CurrentMap.GetAllLayers().Any(l => l.LayerName == name))
            {
                Debug.LogWarning($"已存在同名图层: {name}");
                // 向UI发布失败事件
                eventSystem.Publish(new LayerCreateFailedEvent
                {
                    Reason = $"已存在同名图层：{name}"
                });
                return null;
            }

            // 创建新图层 根据类型实例化对应派生类
            MapEditor.Core.IMapLayer newLayer;
            switch (type)
            {
                case LayerType.Ground:
                    newLayer = new MapEditor.Data.Layers.TilemapLayer();
                    break;
                case LayerType.Object:
                    newLayer = new MapEditor.Data.Layers.ObjectLayer();
                    break;
                case LayerType.Decoration:
                case LayerType.Logic:
                default:
                    // 其他类型暂未实现，使用占位层
                    newLayer = new MapEditor.Data.Layers.TilemapLayer();
                    break;
            }

            // 设置共有字段
            newLayer.LayerId = Guid.NewGuid().ToString();
            newLayer.LayerName = name;
            newLayer.IsVisible = true;
            newLayer.IsLocked = false;
            newLayer.Order = AllocateOrder(type);
            
            // 添加到地图数据
            dataManager.CurrentMap.AddLayer(newLayer);
            
            // 设置 ChunkSize 与地图保持一致
            if (newLayer is MapLayer ml && dataManager.CurrentMap is MapEditor.Core.MapData md)
            {
                ml.SetChunkSize(md.ChunkSize);
            }
            
            // 触发事件
            OnLayerAdded?.Invoke(newLayer);
            eventSystem.Publish(new UI.LayerAddedEvent { Layer = newLayer });



            Debug.Log($"创建新图层: {name} ({type})");
            return newLayer;
        }
        
        public void DeleteLayer(string layerId)
        {
            if (dataManager.CurrentMap == null)
            {
                Debug.LogError("没有当前地图，无法删除图层");
                return;
            }
            
            var layer = dataManager.CurrentMap.GetLayer(layerId);
            if (layer == null)
            {
                Debug.LogWarning($"找不到ID为 {layerId} 的图层");
                return;
            }
            
            // 检查是否是当前活动图层
            if (ActiveLayer?.LayerId == layerId)
            {
                // 切换到其他图层
                var otherLayer = dataManager.CurrentMap.GetAllLayers()
                    .FirstOrDefault(l => l.LayerId != layerId);
                ActiveLayer = otherLayer;
            }
            
            // 从地图数据中移除
            dataManager.CurrentMap.RemoveLayer(layerId);
            
            // 触发事件
            OnLayerRemoved?.Invoke(layerId);
            eventSystem.Publish(new UI.LayerRemovedEvent { LayerId = layerId });
            
            Debug.Log($"删除图层: {layer.LayerName}");
        }
        
        public void RenameLayer(string layerId, string newName)
        {
            if (dataManager.CurrentMap == null)
            {
                Debug.LogError("没有当前地图，无法重命名图层");
                return;
            }
            
            var layer = dataManager.CurrentMap.GetLayer(layerId);
            if (layer == null)
            {
                Debug.LogWarning($"找不到ID为 {layerId} 的图层");
                return;
            }
            
            string oldName = layer.LayerName;
            layer.LayerName = newName;
            
            // 触发图层变更事件
            eventSystem.Publish(new UI.LayerChangedEvent { Layer = layer });
            
            Debug.Log($"图层重命名: {oldName} -> {newName}");
        }
        
        public bool CanDrawOnLayer(IMapLayer layer)
        {
            if (layer == null)
                return false;
                
            // 检查图层是否锁定
            if (layer.IsLocked)
                return false;
                
            // 检查图层是否可见
            if (!layer.IsVisible)
                return false;
                
            // 检查图层类型是否支持绘制
            switch (layer.Type)
            {
                case LayerType.Ground:
                case LayerType.Object:
                case LayerType.Decoration:
                    return true;
                case LayerType.Logic:
                    // 逻辑层暂不支持直接绘制
                    return false;
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// 根据图层ID获取图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        /// <returns>找到的图层，如果不存在返回null</returns>
        public IMapLayer GetLayerById(string layerId)
        {
            if (dataManager.CurrentMap == null)
                return null;
                
            return dataManager.CurrentMap.GetLayer(layerId);
        }
        
        /// <summary>
        /// 根据图层类型获取所有图层
        /// </summary>
        /// <param name="layerType">图层类型</param>
        /// <returns>指定类型的所有图层</returns>
        public IEnumerable<IMapLayer> GetLayersByType(LayerType layerType)
        {
            if (dataManager.CurrentMap == null)
                return new List<IMapLayer>();
                
            return dataManager.CurrentMap.GetAllLayers()
                .Where(layer => layer.Type == layerType);
        }

        public IMapLayer CreateLayer(IMapLayer layer)
        {
            // 添加到地图数据
            dataManager.CurrentMap.AddLayer(layer);

            // 触发事件
            OnLayerAdded?.Invoke(layer);
            eventSystem.Publish(new UI.LayerAddedEvent { Layer = layer });



            Debug.Log($"创建新图层: {layer.LayerName} ({layer.Type})");
            return layer;
        }
    }
}