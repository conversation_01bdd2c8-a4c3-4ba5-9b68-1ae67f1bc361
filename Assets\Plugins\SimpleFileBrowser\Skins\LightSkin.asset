%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66bc3ce4885990c40a88f80fe0ad0101, type: 3}
  m_Name: LightSkin
  m_EditorClassIdentifier: 
  m_font: {fileID: 11400000, guid: 7c53709cd96fa7c46a7603565c4b05d5, type: 2}
  m_fontSize: 14
  m_rowHeight: 30
  m_rowSpacing: 8
  m_windowColor: {r: 0.9338235, g: 0.9338235, b: 0.9338235, a: 1}
  m_filesListColor: {r: 1, g: 1, b: 1, a: 1}
  m_filesVerticalSeparatorColor: {r: 0.6397059, g: 0.6397059, b: 0.6397059, a: 1}
  m_titleBackgroundColor: {r: 0.30882353, g: 0.30882353, b: 0.30882353, a: 1}
  m_titleTextColor: {r: 1, g: 1, b: 1, a: 1}
  m_windowResizeGizmoColor: {r: 0.5294118, g: 0.5294118, b: 0.5294118, a: 1}
  m_headerButtonsColor: {r: 1, g: 1, b: 1, a: 1}
  m_windowResizeGizmo: {fileID: 21300000, guid: 285f1e681b119ce48ae469448241360b, type: 3}
  m_headerBackButton: {fileID: 21300000, guid: 130f660889a70c947bff4c8ba8a2c73e, type: 3}
  m_headerForwardButton: {fileID: 21300000, guid: 130f660889a70c947bff4c8ba8a2c73e, type: 3}
  m_headerUpButton: {fileID: 21300000, guid: 130f660889a70c947bff4c8ba8a2c73e, type: 3}
  m_headerContextMenuButton: {fileID: 21300000, guid: 3eb0ab0fddc930a498bef8ce7e149ea0, type: 3}
  m_inputFieldNormalBackgroundColor: {r: 1, g: 1, b: 1, a: 1}
  m_inputFieldInvalidBackgroundColor: {r: 1, g: 0.39215687, b: 0.39215687, a: 1}
  m_inputFieldTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_inputFieldPlaceholderTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 0.5}
  m_inputFieldSelectedTextColor: {r: 0.65882355, g: 0.80784315, b: 1, a: 0.7529412}
  m_inputFieldCaretColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_inputFieldBackground: {fileID: 21300000, guid: f668f62689b67d242ad33ff665594344, type: 3}
  m_buttonColor: {r: 1, g: 1, b: 1, a: 1}
  m_buttonTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_buttonBackground: {fileID: 21300000, guid: 2c46fa192de46b04e8281f225bee0230, type: 3}
  m_dropdownColor: {r: 1, g: 1, b: 1, a: 1}
  m_dropdownTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_dropdownArrowColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_dropdownCheckmarkColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_dropdownBackground: {fileID: 21300000, guid: 2c46fa192de46b04e8281f225bee0230, type: 3}
  m_dropdownArrow: {fileID: 21300000, guid: f73f38102c749484bbd884d4d8d87440, type: 3}
  m_dropdownCheckmark: {fileID: 21300000, guid: 4924b77159e651e4aaa9a22286725d94, type: 3}
  m_toggleColor: {r: 1, g: 1, b: 1, a: 1}
  m_toggleTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_toggleCheckmarkColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_toggleBackground: {fileID: 21300000, guid: f668f62689b67d242ad33ff665594344, type: 3}
  m_toggleCheckmark: {fileID: 21300000, guid: 4924b77159e651e4aaa9a22286725d94, type: 3}
  m_scrollbarBackgroundColor: {r: 0.9191176, g: 0.9191176, b: 0.9191176, a: 1}
  m_scrollbarColor: {r: 0.7647059, g: 0.7647059, b: 0.7647059, a: 1}
  m_fileHeight: 30
  m_fileIconsPadding: 6
  m_fileNormalBackgroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_fileAlternatingBackgroundColor: {r: 1, g: 1, b: 1, a: 0}
  m_fileHoveredBackgroundColor: {r: 0.8980392, g: 0.9529412, b: 1, a: 1}
  m_fileSelectedBackgroundColor: {r: 0.7058824, g: 0.8666667, b: 1, a: 1}
  m_fileNormalTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_fileSelectedTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_folderIcon: {fileID: 21300000, guid: 27947666271b231439b4409a81cb5d59, type: 3}
  m_driveIcon: {fileID: 21300000, guid: 81da6e5b6342f8f47a5a3a24221306c6, type: 3}
  m_defaultFileIcon: {fileID: 21300000, guid: 89f87b95a0cf73b4484ab74fe3f9ea26, type: 3}
  m_filetypeIcons:
  - extension: .txt
    icon: {fileID: 21300000, guid: d06b8cf70dff2fe418249a6276f9ff70, type: 3}
  - extension: .doc
    icon: {fileID: 21300000, guid: d06b8cf70dff2fe418249a6276f9ff70, type: 3}
  - extension: .docx
    icon: {fileID: 21300000, guid: d06b8cf70dff2fe418249a6276f9ff70, type: 3}
  - extension: .xml
    icon: {fileID: 21300000, guid: d06b8cf70dff2fe418249a6276f9ff70, type: 3}
  - extension: .json
    icon: {fileID: 21300000, guid: d06b8cf70dff2fe418249a6276f9ff70, type: 3}
  - extension: .rtf
    icon: {fileID: 21300000, guid: d06b8cf70dff2fe418249a6276f9ff70, type: 3}
  - extension: .pdf
    icon: {fileID: 21300000, guid: ac5741fcd06d2ce45b9ae2ce9df3c4a1, type: 3}
  - extension: .jpg
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .jpeg
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .png
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .gif
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .bmp
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .psd
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .tif
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .tiff
    icon: {fileID: 21300000, guid: 93482309cf79cef4a9ed8464c560334b, type: 3}
  - extension: .mp4
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .mkv
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .mov
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .avi
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .flv
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .webm
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .wmv
    icon: {fileID: 21300000, guid: 53a62df1d392a10498ec79719f8eef1f, type: 3}
  - extension: .mp3
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .wav
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .aac
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .m4a
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .ogg
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .wma
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .flac
    icon: {fileID: 21300000, guid: 293ea6a99c4ba134cae49556942ee0ff, type: 3}
  - extension: .zip
    icon: {fileID: 21300000, guid: 3b2a773e413d3e84f8c7c757734af584, type: 3}
  - extension: .rar
    icon: {fileID: 21300000, guid: 3b2a773e413d3e84f8c7c757734af584, type: 3}
  - extension: .7z
    icon: {fileID: 21300000, guid: 3b2a773e413d3e84f8c7c757734af584, type: 3}
  - extension: .gz
    icon: {fileID: 21300000, guid: 3b2a773e413d3e84f8c7c757734af584, type: 3}
  - extension: .exe
    icon: {fileID: 21300000, guid: c1e1f7ef76f59f54bbde5162f2709794, type: 3}
  m_fileMultiSelectionToggleOffIcon: {fileID: 21300000, guid: d6beaeac8de2af749a48581db778df3e, type: 3}
  m_fileMultiSelectionToggleOnIcon: {fileID: 21300000, guid: 30a2566f94f937b4998542121050e68b, type: 3}
  m_contextMenuBackgroundColor: {r: 0.955, g: 0.955, b: 0.955, a: 1}
  m_contextMenuTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_contextMenuSeparatorColor: {r: 0.6397059, g: 0.6397059, b: 0.6397059, a: 1}
  m_popupPanelsBackgroundColor: {r: 0.955, g: 0.955, b: 0.955, a: 1}
  m_popupPanelsTextColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_popupPanelsBackground: {fileID: 21300000, guid: b9c5fedc997f125448c4d8be2fc43bcd, type: 3}
