# MapEditor 项目架构说明

## 1. 概述

MapEditor 是一个基于 Unity 引擎的 2D 地图编辑器。项目采用**面向服务的架构 (Service-Oriented Architecture)** 和**事件驱动设计 (Event-Driven Design)**，旨在实现高度模块化、可扩展和易于维护的系统。

核心设计思想是将不同的功能领域（如数据管理、渲染、UI、工具等）封装到独立的服务中，服务之间通过一个中央事件总线进行解耦通信。

## 2. 核心组件

### 2.1. `MapEditorCore` - 核心协调器

`MapEditorCore` 是整个系统的入口和核心。它扮演着**服务定位器 (Service Locator)** 的角色，负责：

-   **生命周期管理**：在启动时初始化所有核心系统和服务。
-   **服务注册与发现**：提供一个中央注册表，用于注册和获取各种 `IService` 接口的实现。
-   **Update 循环**：为需要每帧更新的服务提供 `Update` 和 `LateUpdate` 的调用。

### 2.2. `EventBus` - 事件总线

`EventBus` 是系统内通信的枢纽。它是一个线程安全的事件系统，允许不同模块之间进行解耦的通信。

-   **发布/订阅模式**：组件可以订阅特定类型的事件，并在事件发布时收到通知。
-   **同步与异步**：支持同步事件（立即执行）和异步事件（在主线程的下一帧处理）。
-   **优先级**：订阅者可以指定优先级，以控制事件处理的顺序。

### 2.3. 服务 (`IService`)

服务是封装了特定业务逻辑的独立单元。所有服务都继承自 `ServiceBase`，后者提供了对 `MapEditorCore` 和 `EventBus` 的便捷访问。

主要服务包括：

-   **`MapService`**: 负责地图数据的核心业务逻辑，包括创建、加载、保存地图，以及管理图层。
-   **`LayerRenderService`**: 管理图层渲染器的生命周期。它不直接执行渲染，而是通过向 `SceneRenderManager` 发送事件来请求渲染器的创建和销毁。
-   **`ToolManager`**: 管理编辑器中的各种工具（如笔刷、选择工具），并处理工具的激活和切换。
-   **`UIManager`**: 负责管理所有基于 UI Toolkit 的 UI 面板和对话框。
-   **`InputHandler`**: 处理用户的鼠标和键盘输入，并将其转换为系统可以理解的事件。

### 2.4. 数据模型

-   **`MapData`**: 代表一个完整的地图，包含地图元数据（名称、尺寸等）和所有图层。
-   **`MapLayer`**: 地图图层的抽象基类。项目支持多种图层类型，如 `TilemapLayer` (地块图层), `ObjectLayer` (对象图层), 和 `HeightLayer` (高度图层)。
-   **`ChunkBase`**: 数据管理的基本单元。为了高效处理大世界，每个图层的数据被分割成固定大小的 `Chunk`。

### 2.5. 渲染系统

-   **`SceneRenderManager`**: 一个统一的渲染管理器，负责场景中的所有渲染任务。它管理 `IRenderable` 对象和 `LayerRenderer`。
-   **`LayerRenderer`**: 负责将一个 `MapLayer` 的数据可视化到场景中。例如，`TilemapLayerRenderer` 负责渲染地块纹理。
-   **`ChunkRenderProxy`**: 在场景中实际代表一个 `Chunk` 的 `GameObject`。它持有渲染所需的 `Material` 和 `Mesh`。

### 2.6. UI 系统

-   **`UIManager`**: 基于 **UI Toolkit**，管理所有 UI 元素的生命周期和交互。
-   **`UIPanel`**: UI 面板的基类接口，定义了面板的基本行为，如显示、隐藏和更新。

### 2.7. 工具系统 (`IMapTool`)

-   **`IMapTool`**: 定义了编辑器工具（如笔刷、橡皮擦、选择工具）的接口。
-   **`ToolManager`**: 负责注册和管理所有 `IMapTool` 的实例，并根据用户操作激活相应的工具。

## 3. 架构模式

-   **服务定位器 (Service Locator)**: `MapEditorCore` 作为服务的中央注册表。
-   **事件驱动架构 (Event-Driven Architecture)**: 组件间通过 `EventBus` 通信，降低耦合度。
-   **分层架构**: 逻辑上分为数据层 (`Data`)、服务层 (`Services`)、渲染层 (`Rendering`) 和 UI 层 (`UI`)。
-   **数据-渲染分离**: 数据层 (`MapLayer`, `Chunk`) 与渲染层 (`LayerRenderer`, `ChunkRenderProxy`) 分离。渲染器读取数据进行可视化，但不直接修改数据。
-   **Chunking**: 将地图数据划分为块，实现按需加载和渲染，优化性能。
-   **工厂模式 (Factory Pattern)**: 通过 `MapLayer.RegisterChunkFactory` 动态创建不同类型的 `Chunk` 实例，避免了反射。

## 4. 架构规范

为了保持项目的一致性和可维护性，开发时应遵循以下规范：

1.  **模块化**: 新功能应尽可能封装在新的 `IService` 中。
2.  **通信**: 优先使用 `EventBus` 进行模块间通信，避免直接引用。在 `Event` 目录下定义新的事件类型。
3.  **职责分离**:
    -   数据操作逻辑应位于 `MapService` 和 `Data` 目录中。
    -   渲染逻辑应位于 `LayerRenderService` 和 `Rendering` 目录中。
    -   UI 逻辑应位于 `UIManager` 和 `UI` 目录中。
4.  **UI 开发**: 所有新 UI 必须使用 **UI Toolkit** 构建，并作为 `UIPanel` 注册到 `UIManager`。
5.  **数据管理**: 所有空间相关的地图数据都必须通过 `Chunk` 进行管理。
6.  **工具开发**: 新的编辑工具必须实现 `IMapTool` 接口，并通过 `ToolManager` 进行管理。
7.  **命名约定**: 遵循 C# 的标准命名约定，并与项目中现有代码风格保持一致。
