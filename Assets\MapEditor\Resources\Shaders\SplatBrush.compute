// SplatBrush.compute
// 使用 S2 插值混合算法，实现更柔和的多材质过渡
// 支持 7 个材质通道(0-6) + 1个coverage通道，分布在两张 RGBA Texture (Splat0/1)

#pragma kernel CSMain
#pragma target 5.0

// 纹理尺寸 = ChunkSize
RWTexture2D<float4> _Splat0;
RWTexture2D<float4> _Splat1;
Texture2D<float>    _MaskTex;   // R 通道存储 brush mask (已乘 strength/flow)
SamplerState sampler_LinearClamp;

// 画笔参数
int2    _OffsetPx;   // 画笔左下角在 Chunk 像素坐标(允许为负值)
uint    _MaskSize;   // 画笔像素直径
uint    _Channel;    // 0-6 选中材质通道 (>=7 被忽略，通道7专用于coverage)
float   _Hardness;   // 笔刷硬度 (0.1-5.0)

[numthreads(8,8,1)]
void CSMain (uint3 id : SV_DispatchThreadID)
{
    uint width, height;
    _Splat0.GetDimensions(width, height);

    if(id.x >= _MaskSize || id.y >= _MaskSize) return; // 超出画笔区域

    int2 dstInt = _OffsetPx + int2(id.xy);
    if (dstInt.x < 0 || dstInt.y < 0 || dstInt.x >= (int)width || dstInt.y >= (int)height) return;
    uint2 dst = uint2(dstInt);

    // ---------------- 读取数据 ----------------
    float2 uv = (float2)id.xy / (float)_MaskSize;
    float mask = _MaskTex.SampleLevel(sampler_LinearClamp, uv, 0).r;  // 0-1
    if(mask <= 0) return;

    float4 w0 = _Splat0[dst];               // 材质通道0-3
    float4 w1 = _Splat1[dst];               // 材质通道4-6 + coverage

    // 提取7个材质权重与 coverage
    float weights[7];
    weights[0]=w0.r; weights[1]=w0.g; weights[2]=w0.b; weights[3]=w0.a;
    weights[4]=w1.r; weights[5]=w1.g; weights[6]=w1.b;

    float coverage = w1.a;                 // coverage通道 (0-1)

    uint tgt = _Channel;
    if(tgt >= 7) return; // 材质通道范围0-6，第7通道保留给coverage

    // -------------------------------------------------------------
    // S2+ 带硬度插值混合算法
    // -------------------------------------------------------------
    
    // 1. 应用硬度并进行线性插值
    // 使用 saturate 确保 mask 不为负，避免 pow 函数产生 a NaN 警告
    float effectiveMask = pow(saturate(mask), _Hardness);
    float sum = 0;
    [unroll]
    for(uint j = 0; j < 7; ++j)
    {
        float target = (j == tgt) ? 1.0 : 0.0;
        weights[j] = lerp(weights[j], target, effectiveMask);
        sum += weights[j];
    }

    // 2. 归一化：W_new[i] = W_tmp[i] / sum
    float invSum = (sum > 1e-6) ? rcp(sum) : 0.0;
    [unroll]
    for(uint k = 0; k < 7; ++k)
    {
        weights[k] *= invSum;
    }

    // 3. coverage 保持原值（或可选择设为 1）
    // 这里保持原有的喷枪特性
    // coverage = 1.0; // 可选：强制设为 1

    // ---------------- 写回 ----------------
    w0 = float4(weights[0], weights[1], weights[2], weights[3]);
    w1 = float4(weights[4], weights[5], weights[6], coverage);

    _Splat0[dst] = w0;
    _Splat1[dst] = w1;
} 