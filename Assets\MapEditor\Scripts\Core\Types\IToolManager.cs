using System.Collections.Generic;

namespace MapEditor.Core
{
    /// <summary>
    /// 工具管理器接口，负责管理所有地图编辑工具
    /// </summary>
    public interface IToolManager
    {
        /// <summary>
        /// 当前激活的工具
        /// </summary>
        IMapTool ActiveTool { get; }

        /// <summary>
        /// 注册一个工具
        /// </summary>
        void RegisterTool(IMapTool tool);

        /// <summary>
        /// 注销一个工具
        /// </summary>
        void UnregisterTool(string toolId);

        /// <summary>
        /// 激活指定ID的工具
        /// </summary>
        void ActivateTool(string toolId);

        /// <summary>
        /// 获取所有已注册的工具
        /// </summary>
        IMapTool[] GetAllTools();

        /// <summary>
        /// 获取所有已注册的工具ID
        /// </summary>
        IEnumerable<string> GetAllToolIds();

        /// <summary>
        /// 获取指定ID的工具
        /// </summary>
        IMapTool GetTool(string toolId);

        /// <summary>
        /// 获取支持指定图层类型的所有工具
        /// </summary>
        /// <param name="layerType">图层类型</param>
        /// <returns>支持该图层类型的工具列表</returns>
        IMapTool[] GetToolsForLayerType(LayerType layerType);

        /// <summary>
        /// 处理当前工具的输入
        /// </summary>
        /// <param name="context">输入上下文</param>
        void ProcessInput(InputContext context);

        /// <summary>
        /// 更新当前工具的预览
        /// </summary>
        void UpdateToolPreview();
    }

}