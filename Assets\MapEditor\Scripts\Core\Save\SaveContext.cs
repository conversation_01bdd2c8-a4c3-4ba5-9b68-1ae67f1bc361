using System;

namespace MapEditor.Core
{
    /// <summary>
    /// 保存上下文，向 ISavable 传递统一的环境信息。
    /// </summary>
    public readonly struct SaveContext
    {
        public readonly bool IsAuto;
        public readonly string MapDirectory;
        public readonly DateTime TimeStamp;

        public SaveContext(bool isAuto, string mapDirectory)
        {
            IsAuto = isAuto;
            MapDirectory = mapDirectory;
            TimeStamp = DateTime.Now;
        }
    }
} 