using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Event;

namespace MapEditor.Services
{
    /// <summary>
    /// 工具管理器，负责管理和切换编辑工具
    /// </summary>
    public class ToolManager : ServiceBase, IToolManager,IUpdate
    {
        // 存储所有注册的工具
        private readonly Dictionary<string, IMapTool> tools = new();
        
        // 当前激活的工具
        private IMapTool activeTool;
        private ILayerDataStore layerStore;
        // 编辑器核心引用
        //private readonly IMapEditorCore editorCore;

        /// <summary>
        /// 当前激活的工具
        /// </summary>
        public IMapTool ActiveTool => activeTool;


        public override void Initialize()
        {
            layerStore = core.LayerStore;
        }

        public override void Start()
        {
            RegisterEvent<ToolShortcutEvent>(OnToolShortcut);
            RegisterEvent<ToolCancelEvent>(OnToolCancel);
        }


        public void Update()
        {
            activeTool?.Update();
        }

        /// <summary>
        /// 注册一个工具
        /// </summary>
        public void RegisterTool(IMapTool tool)
        {
            if (tool == null || string.IsNullOrEmpty(tool.ToolId))
            {
                Debug.LogError("Cannot register null tool or tool with empty ID");
                return;
            }
            
            if (tools.ContainsKey(tool.ToolId))
            {
                Debug.LogWarning($"Tool with ID {tool.ToolId} already registered, replacing");
                tools[tool.ToolId] = tool;
            }
            else
            {
                tools.Add(tool.ToolId, tool);
            }
            
            Debug.Log($"Tool {tool.DisplayName} registered with ID: {tool.ToolId}");
        }
        
        /// <summary>
        /// 注销一个工具
        /// </summary>
        public void UnregisterTool(string toolId)
        {
            if (string.IsNullOrEmpty(toolId) || !tools.ContainsKey(toolId))
            {
                Debug.LogWarning($"Tool with ID {toolId} not found for unregistering");
                return;
            }
            
            if (activeTool != null && activeTool.ToolId == toolId)
            {
                activeTool.OnDeactivate();
                activeTool = null;
            }
            
            tools.Remove(toolId);
        }
        
        /// <summary>
        /// 激活指定ID的工具
        /// </summary>
        public void ActivateTool(string toolId)
        {
            if (string.IsNullOrEmpty(toolId))
            {
                Debug.LogError("Cannot activate tool with empty ID");
                return;
            }
            
            if (!tools.TryGetValue(toolId, out IMapTool newTool))
            {
                Debug.LogError($"Tool with ID {toolId} not found");
                return;
            }
            
            if (activeTool != null && activeTool.ToolId == toolId)
            {
                // 已经是当前工具，无需切换
                return;
            }
            
            // 停用当前工具
            var oldToolId = activeTool?.ToolId;
            activeTool?.OnDeactivate();
            
            // 激活新工具
            activeTool = newTool;
            activeTool.OnActivate();
            
            // 发布工具切换事件
            PublishEvent(new ToolChangedEvent
            {
                OldToolId = oldToolId,
                NewToolId = toolId
            });
        }
        
        /// <summary>
        /// 获取所有已注册的工具
        /// </summary>
        public IMapTool[] GetAllTools()
        {
            return tools.Values.ToArray();
        }
        
        /// <summary>
        /// 获取所有已注册的工具ID
        /// </summary>
        public IEnumerable<string> GetAllToolIds()
        {
            return tools.Keys;
        }
        
        /// <summary>
        /// 获取指定ID的工具
        /// </summary>
        public IMapTool GetTool(string toolId)
        {
            if (string.IsNullOrEmpty(toolId) || !tools.ContainsKey(toolId))
            {
                return null;
            }
            
            return tools[toolId];
        }
        
        /// <summary>
        /// 获取支持指定图层类型的所有工具
        /// </summary>
        public IMapTool[] GetToolsForLayerType(LayerType layerType)
        {
            return tools.Values
                .Where(tool => tool.SupportsLayerType(layerType))
                .ToArray();
        }
        
        /// <summary>
        /// 处理当前工具的输入
        /// </summary>
        public void ProcessInput(InputContext context)
        {
            activeTool?.OnSceneInput(context);
        }
        
        /// <summary>
        /// 更新当前工具的预览
        /// </summary>
        public void UpdateToolPreview()
        {
            activeTool?.UpdatePreview();
        }

        #region 事件处理

        /// <summary>
        /// 处理工具快捷键事件（数字键1-9）。
        /// </summary>
        private void OnToolShortcut(ToolShortcutEvent evt)
        {
            // 获取所有工具，确保索引有效
            var allTools = GetAllTools();
            if (evt.ShortcutIndex < 0 || evt.ShortcutIndex >= allTools.Length)
                return;

            var targetTool = allTools[evt.ShortcutIndex];

            // 检查是否支持当前图层
            var activeLayer = layerStore?.ActiveLayer;
            if (activeLayer != null && !targetTool.SupportsLayerType(activeLayer.Type))
            {
                Debug.LogWarning($"工具 {targetTool.DisplayName} 不支持当前图层类型 {activeLayer.Type}");
                return;
            }

            // 激活工具
            ActivateTool(targetTool.ToolId);
        }

        /// <summary>
        /// 处理工具取消事件（如 Esc 触发）。
        /// </summary>
        private void OnToolCancel(ToolCancelEvent evt)
        {
            // 若当前工具不是选择工具，则切回选择工具
            if (ActiveTool != null && ActiveTool.ToolId != "SelectionTool")
            {
                ActivateTool("SelectionTool");
            }
        }


        #endregion
    }
} 