using UnityEditor;
using UnityEngine;
using MapEditor.Rendering.RenderProxy;

// 该脚本位于 Editor 目录，仅在 Unity 编辑器中编译
[CustomEditor(typeof(ObjectColliderAdapter))]
public class ObjectColliderAdapterEditor : Editor
{
    private void OnSceneGUI()
    {
        ObjectColliderAdapter adapter = (ObjectColliderAdapter)target;

        if (adapter == null || adapter.mode != ColliderMode.Custom) return;
        var collider = adapter.customPhysicsCollider;
        if (collider == null || collider.shape != VirtualColliderShape.Polygon || collider.points == null || collider.points.Count < 1)
            return;

        Handles.color = Color.yellow;
        bool changed = false;

        for (int i = 0; i < collider.points.Count; i++)
        {
            Vector3 worldPos = adapter.transform.TransformPoint(collider.center + collider.points[i]);
            float size = HandleUtility.GetHandleSize(worldPos) * 0.08f;

            var fmh_26_68_638868415309743850 = Quaternion.identity; Vector3 newWorldPos = Handles.FreeMoveHandle(worldPos, size, Vector3.zero, Handles.CircleHandleCap);
            if (worldPos != newWorldPos)
            {
                Undo.RecordObject(adapter, "Move Polygon Point");
                collider.points[i] = (Vector2)adapter.transform.InverseTransformPoint(newWorldPos) - collider.center;
                changed = true;
            }
        }

        // 绘制多边形边线
        Handles.color = Color.red;
        for (int i = 0; i < collider.points.Count; i++)
        {
            Vector3 wp1 = adapter.transform.TransformPoint(collider.center + collider.points[i]);
            Vector3 wp2 = adapter.transform.TransformPoint(collider.center + collider.points[(i + 1) % collider.points.Count]);
            Handles.DrawLine(wp1, wp2);
        }

        if (changed)
        {
            adapter.MarkPhysicsBoundsDirty();
            EditorUtility.SetDirty(adapter);
        }
    }
} 