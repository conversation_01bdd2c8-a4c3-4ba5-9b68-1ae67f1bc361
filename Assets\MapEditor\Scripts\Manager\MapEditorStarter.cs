using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.UI;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Tools;
using MapEditor.Rendering;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using UnityEngine.EventSystems;

namespace MapEditor.Manager
{
    /// <summary>
    /// 地图编辑器启动器，用于初始化编辑器和测试
    /// </summary>
    public class MapEditorStarter : MonoBehaviour
    {

        [Header("UI设置")]
        [SerializeField] private UIDocument uiDocument;
        [SerializeField] private VisualTreeAsset mainUITemplate;
        [SerializeField] private VisualTreeAsset messageBoxTemplate;
        [SerializeField] private VisualTreeAsset confirmDialogTemplate;
        [SerializeField] private StyleSheet styleSheet;
        
        private MapEditorCore editorCore;
        


        /// <summary>
        /// 启动编辑器
        /// </summary>
        public void StartEditor()
        {

            // 注册所有 Chunk 工厂
            RegisterChunkFactories();

            // 1. 创建编辑器核心
            GameObject editorObj = new("MapEditorCore");
            // editorObj.transform.SetParent(transform); // 保持为根对象，避免DontDestroyOnLoad报错
            editorCore = editorObj.AddComponent<MapEditorCore>();
            DontDestroyOnLoad(editorObj);

            // 2. 初始化核心系统和服务
            InitializeCore();
            InitializeServices(editorCore);
            
            // // 3. 初始化UI
            // InitializeUI();
            
            Debug.Log("地图编辑器启动完成");
        }


        private void InitializeCore()
        {
            // 创建事件系统
            var eventSystem = new EventBus();

            
            // 创建事件系统更新器
            GameObject eventUpdaterObj = new("EventSystemUpdater");
            eventUpdaterObj.transform.SetParent(transform);
            var eventUpdater = eventUpdaterObj.AddComponent<EventSystemUpdater>();
            eventUpdater.Initialize(eventSystem);

            // 创建统一的场景渲染管理器
            GameObject renderManagerObj = new("SceneRenderManager");
            renderManagerObj.transform.SetParent(transform);
            var sceneRenderManager = renderManagerObj.AddComponent<SceneRenderManager>();
            sceneRenderManager.Initialize(eventSystem);

            // 创建数据管理器
            var mapDataStore = new MapDataStore(eventSystem);


            // 创建图层管理器
            var layerStore = new LayerDataStore(mapDataStore, eventSystem);


            editorCore.Initialize(
                eventSystem,
                sceneRenderManager,  // 同时作为 ISceneRenderer   
                mapDataStore,
                layerStore);
                
        }


        /// <summary>
        /// 初始化所有服务
        /// </summary>
        private void InitializeServices(IMapEditorCore editorCore)
        {

            // 注册服务到容器
            editorCore.RegisterService(new InputHandler());
            editorCore.RegisterService(new LayerRenderService());
            editorCore.RegisterService(new MapService());
            editorCore.RegisterService(new ToolManager());
            editorCore.RegisterService(new ToolService());
            editorCore.RegisterService(new ObjectService());
            editorCore.RegisterService(new GridService());
            editorCore.RegisterService(new AutoSaveService());
            editorCore.RegisterService(new SaveService());
            editorCore.RegisterService(new PrefabSizeCache());
            
            Debug.Log("All services registered.");
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 确保场景中存在 EventSystem，以便 InputHandler 能准确检测 UI 遮挡
            if (EventSystem.current == null)
            {
                var esObj = new GameObject("EventSystem");
                esObj.transform.SetParent(transform);
                esObj.AddComponent<EventSystem>();
#if ENABLE_INPUT_SYSTEM
                esObj.AddComponent<UnityEngine.InputSystem.UI.InputSystemUIInputModule>();
#else
                esObj.AddComponent<StandaloneInputModule>();
#endif
            }

            // 创建UI管理器实例
            GameObject uiObj = new("UIManager");
            uiObj.transform.SetParent(transform);
            var uiManagerComponent = uiObj.AddComponent<UIManager>();
            
            // 初始化UI
            uiManagerComponent.Initialize();
            uiManagerComponent.InitializeUI(uiDocument, mainUITemplate, messageBoxTemplate, confirmDialogTemplate, styleSheet);
        }


        private void Awake()
        {
            // 自动启动编辑器
            StartEditor();
        }

        private void Start()
        {
            InitializeUI();

        }
        
        /// <summary>
        /// 关闭编辑器系统
        /// </summary>
        public void ShutdownEditor()
        {      
            editorCore?.Shutdown();

            // 清理事件系统
            if (editorCore.EventSystem != null)
            {
                editorCore.EventSystem.Dispose();
            }
        }
        
        /// <summary>
        /// 应用退出时清理资源
        /// </summary>
        private void OnApplicationQuit()
        {
            ShutdownEditor();
        }

        /// <summary>
        /// 注册所有 Chunk 工厂，集中初始化，避免反射 & 静态构造函数依赖
        /// </summary>
        private void RegisterChunkFactories()
        {
            // 如果后续新增新的 Chunk 类型，请在此处补充注册逻辑
            MapLayer.RegisterChunkFactory<TilemapChunk>((coord, size) => new TilemapChunk(coord, size));
            MapLayer.RegisterChunkFactory<ObjectChunk>((coord, size) => new ObjectChunk(coord, size));
        }
    }
}
