using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Tools;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Event;
using System.Collections.Generic;

namespace MapEditor.UI
{
    /// <summary>
    /// 曲线工具设置面板
    /// </summary>
    public class CurveToolPanel : UIPanel
    {
        public override string PanelId => "CurveTool";
        public override string DisplayName => "曲线工具";
        
        // UI控件引用
        private DropdownField curveTypeDropdown;
        private Slider widthSlider;
        private FloatField widthInput;
        private Slider softnessSlider;
        private FloatField softnessInput;
        private Slider strengthSlider;
        private FloatField strengthInput;
        private Slider resolutionSlider;
        private FloatField resolutionInput;
        private Button renderButton;
        private VisualElement materialContainer;
        
        // 材质相关
        private GroundTextureSO[] groundTextures;
        private List<Button> materialButtons = new List<Button>();
        private CurveTool curveTool;
        
        public CurveToolPanel(IUIManager uiManager, VisualTreeAsset template) : base(uiManager, template) { }
        
        public override void Initialize()
        {
            // 获取CurveTool引用
            var toolService = MapEditorCore.Instance.GetService<ToolService>();
            curveTool = toolService?.GetTool("CurveTool") as CurveTool;
            if (curveTool == null)
            {
                Debug.LogError("CurveTool not found, CurveToolPanel disabled");
                return;
            }
            
            // 加载材质资源
            groundTextures = GroundTextureProvider.GetAllTextures();
            
            // 查找UI控件
            FindUIElements();
            
            // 设置初始值
            SetInitialValues();
            
            // 注册事件回调
            RegisterEventCallbacks();
            
            // 初始化材质网格
            InitializeMaterialGrid();
            
            // 订阅工具切换事件
            MapEditorCore.Instance.EventSystem?.Subscribe<ToolChangedEvent>(OnToolChanged);
            
            // 刷新UI显示
            RefreshUIFromTool();
            
            Debug.Log("CurveToolPanel initialized");
        }
        
        /// <summary>
        /// 查找UI控件
        /// </summary>
        private void FindUIElements()
        {
            curveTypeDropdown = FindElement<DropdownField>("CurveTypeDropdown");
            widthSlider = FindElement<Slider>("WidthSlider");
            widthInput = FindElement<FloatField>("WidthInput");
            softnessSlider = FindElement<Slider>("SoftnessSlider");
            softnessInput = FindElement<FloatField>("SoftnessInput");
            strengthSlider = FindElement<Slider>("StrengthSlider");
            strengthInput = FindElement<FloatField>("StrengthInput");
            resolutionSlider = FindElement<Slider>("ResolutionSlider");
            resolutionInput = FindElement<FloatField>("ResolutionInput");
            materialContainer = FindElement<VisualElement>("MaterialContainer");
            renderButton = FindElement<Button>("RenderButton");
        }
        
        /// <summary>
        /// 设置初始值
        /// </summary>
        private void SetInitialValues()
        {
            if (curveTool == null) return;
            
            var settings = curveTool.CurrentSettings;
            
            // 设置曲线类型下拉框
            curveTypeDropdown?.SetValueWithoutNotify(GetCurveTypeDisplayName(settings.curveType));
            
            // 设置各种参数的初始值
            widthSlider?.SetValueWithoutNotify(settings.width);
            widthInput?.SetValueWithoutNotify(settings.width);
            softnessSlider?.SetValueWithoutNotify(settings.edgeSoftness);
            softnessInput?.SetValueWithoutNotify(settings.edgeSoftness);
            strengthSlider?.SetValueWithoutNotify(settings.strength);
            strengthInput?.SetValueWithoutNotify(settings.strength);
            resolutionSlider?.SetValueWithoutNotify(settings.resolution);
            resolutionInput?.SetValueWithoutNotify(settings.resolution);
        }
        
        /// <summary>
        /// 注册事件回调
        /// </summary>
        private void RegisterEventCallbacks()
        {
            // 曲线类型变更
            curveTypeDropdown?.RegisterValueChangedCallback(evt => {
                var curveType = GetCurveTypeFromDisplayName(evt.newValue);
                curveTool?.SetCurveType(curveType);
            });
            
            // 宽度控制
            widthSlider?.RegisterValueChangedCallback(evt => {
                widthInput?.SetValueWithoutNotify(evt.newValue);
                curveTool?.SetStrokeWidth(evt.newValue);
            });
            
            widthInput?.RegisterValueChangedCallback(evt => {
                var clampedValue = Mathf.Clamp(evt.newValue, 0.1f, 10f);
                widthSlider?.SetValueWithoutNotify(clampedValue);
                curveTool?.SetStrokeWidth(clampedValue);
            });
            
            // 柔和度控制
            softnessSlider?.RegisterValueChangedCallback(evt => {
                softnessInput?.SetValueWithoutNotify(evt.newValue);
                curveTool?.SetEdgeSoftness(evt.newValue);
            });
            
            softnessInput?.RegisterValueChangedCallback(evt => {
                var clampedValue = Mathf.Clamp01(evt.newValue);
                softnessSlider?.SetValueWithoutNotify(clampedValue);
                curveTool?.SetEdgeSoftness(clampedValue);
            });
            
            // 强度控制
            strengthSlider?.RegisterValueChangedCallback(evt => {
                strengthInput?.SetValueWithoutNotify(evt.newValue);
                curveTool?.SetStrength(evt.newValue);
            });
            
            strengthInput?.RegisterValueChangedCallback(evt => {
                var clampedValue = Mathf.Clamp01(evt.newValue);
                strengthSlider?.SetValueWithoutNotify(clampedValue);
                curveTool?.SetStrength(clampedValue);
            });
            
            // 分辨率控制
            resolutionSlider?.RegisterValueChangedCallback(evt => {
                resolutionInput?.SetValueWithoutNotify(evt.newValue);
                // 注意：分辨率改变需要重新生成曲线，通过设置曲线类型触发
                var currentType = curveTool?.CurrentSettings.curveType ?? CurveType.CatmullRom;
                curveTool?.SetCurveType(currentType);
            });
            
            resolutionInput?.RegisterValueChangedCallback(evt => {
                var clampedValue = Mathf.Clamp(evt.newValue, 2f, 50f);
                resolutionSlider?.SetValueWithoutNotify(clampedValue);
                // 同样需要重新生成曲线
                var currentType = curveTool?.CurrentSettings.curveType ?? CurveType.CatmullRom;
                curveTool?.SetCurveType(currentType);
            });

            renderButton?.RegisterCallback<ClickEvent>(evt => 
            {
                curveTool?.ExecuteDraw();
            });
        }
        
        /// <summary>
        /// 初始化材质网格
        /// </summary>
        private void InitializeMaterialGrid()
        {
            if (materialContainer == null || groundTextures == null) return;
            
            materialContainer.Clear();
            materialButtons.Clear();
            
            for (int i = 0; i < groundTextures.Length; i++)
            {
                var texture = groundTextures[i];
                if (texture?.texture == null) continue;
                
                var button = CreateMaterialButton(texture, i);
                materialContainer.Add(button);
                materialButtons.Add(button);
            }
            
            // 默认选中第一个材质并同步到工具
            if (groundTextures.Length > 0)
            {
                curveTool?.SetMaterialIndex(groundTextures[0].textureIndex);
                UpdateMaterialSelection(0);
            }
        }
        
        /// <summary>
        /// 创建材质按钮
        /// </summary>
        private Button CreateMaterialButton(GroundTextureSO texture, int index)
        {
            var button = new Button(() => SelectMaterial(index));
            
            // 设置按钮样式
            button.style.width = 48;
            button.style.height = 48;
            button.style.marginLeft = 2;
            button.style.marginRight = 2;
            button.style.marginTop = 2;
            button.style.marginBottom = 2;
            button.style.borderTopLeftRadius = 4;
            button.style.borderTopRightRadius = 4;
            button.style.borderBottomLeftRadius = 4;
            button.style.borderBottomRightRadius = 4;
            
            // 设置背景图片
            button.style.backgroundImage = new StyleBackground(texture.texture);
            button.style.backgroundSize = new BackgroundSize(BackgroundSizeType.Contain);
            button.style.backgroundRepeat = new BackgroundRepeat(Repeat.NoRepeat, Repeat.NoRepeat);
            
            // 设置工具提示
            button.tooltip = texture.name;
            
            return button;
        }
        
        /// <summary>
        /// 选择材质
        /// </summary>
        private void SelectMaterial(int index)
        {
            // 将按钮序号映射为全局材质索引
            if (index >= 0 && index < groundTextures.Length)
            {
                int globalIndex = groundTextures[index]?.textureIndex ?? -1;
                if (globalIndex >= 0)
                {
                    curveTool?.SetMaterialIndex(globalIndex);
                }
            }
            UpdateMaterialSelection(index);
        }
        
        /// <summary>
        /// 更新材质选择状态
        /// </summary>
        private void UpdateMaterialSelection(int selectedIndex)
        {
            for (int i = 0; i < materialButtons.Count; i++)
            {
                var button = materialButtons[i];
                if (i == selectedIndex)
                {
                    // 选中状态：添加边框
                    button.style.borderLeftColor = Color.yellow;
                    button.style.borderRightColor = Color.yellow;
                    button.style.borderTopColor = Color.yellow;
                    button.style.borderBottomColor = Color.yellow;
                    button.style.borderLeftWidth = 2;
                    button.style.borderRightWidth = 2;
                    button.style.borderTopWidth = 2;
                    button.style.borderBottomWidth = 2;
                }
                else
                {
                    // 未选中状态：移除边框
                    button.style.borderLeftWidth = 0;
                    button.style.borderRightWidth = 0;
                    button.style.borderTopWidth = 0;
                    button.style.borderBottomWidth = 0;
                }
            }
        }
        
        /// <summary>
        /// 获取曲线类型对应的下拉框索引
        /// </summary>
        private string GetCurveTypeDisplayName(CurveType curveType)
        {
            return curveType switch
            {
                CurveType.Line => "直线",
                CurveType.CatmullRom => "Catmull-Rom样条",
                CurveType.Bezier => "贝塞尔曲线",
                _ => "Catmull-Rom样条"
            };
        }
        
        /// <summary>
        /// 从下拉框索引获取曲线类型
        /// </summary>
        private CurveType GetCurveTypeFromDisplayName(string displayName)
        {
            return displayName switch
            {
                "直线" => CurveType.Line,
                "Catmull-Rom样条" => CurveType.CatmullRom,
                "贝塞尔曲线" => CurveType.Bezier,
                _ => CurveType.CatmullRom
            };
        }
        
        /// <summary>
        /// 从工具刷新UI显示
        /// </summary>
        private void RefreshUIFromTool()
        {
            if (curveTool == null) return;
            
            var settings = curveTool.CurrentSettings;
            
            // 更新所有控件的值，但不触发回调
            curveTypeDropdown?.SetValueWithoutNotify(GetCurveTypeDisplayName(settings.curveType));
            widthSlider?.SetValueWithoutNotify(settings.width);
            widthInput?.SetValueWithoutNotify(settings.width);
            softnessSlider?.SetValueWithoutNotify(settings.edgeSoftness);
            softnessInput?.SetValueWithoutNotify(settings.edgeSoftness);
            strengthSlider?.SetValueWithoutNotify(settings.strength);
            strengthInput?.SetValueWithoutNotify(settings.strength);
            resolutionSlider?.SetValueWithoutNotify(settings.resolution);
            resolutionInput?.SetValueWithoutNotify(settings.resolution);
            
            // 更新材质选择
            UpdateMaterialSelection(settings.materialIndex);
        }
        
        /// <summary>
        /// 工具切换事件处理
        /// </summary>
        private void OnToolChanged(ToolChangedEvent evt)
        {
            IsVisible = evt.NewToolId == "CurveTool";
            if (IsVisible)
            {
                RefreshUIFromTool();
            }
        }
        
        public override void UpdatePanel()
        {
            RefreshUIFromTool();
        }
    }
} 