using System;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;

namespace MapEditor.UI
{
    /// <summary>
    /// 颜色选择器对话框
    /// </summary>
    public class ColorPickerDialog : UIPanel
    {
        public override string PanelId => "ColorPickerDialog";
        public override string DisplayName => "颜色选择器";

        // UI控件引用
        private VisualElement colorPreview;
        private VisualElement presetColorsGrid;
        
        // RGB控件
        private Slider redSlider;
        private Slider greenSlider;
        private Slider blueSlider;
        private Slider alphaSlider;
        private IntegerField redField;
        private IntegerField greenField;
        private IntegerField blueField;
        private IntegerField alphaField;
        private TextField hexField;
        
        // 按钮
        private Button confirmButton;
        private Button cancelButton;

        // 数据
        private Color currentColor = Color.white;
        private bool isUpdating = false; // 防止递归更新

        // 预设颜色
        private static readonly Color[] PresetColors = {
            // 基础颜色
            Color.white, Color.black, Color.gray, new Color(0.75f, 0.75f, 0.75f),
            Color.red, Color.green, Color.blue, Color.yellow,
            Color.cyan, Color.magenta, new Color(1f, 0.5f, 0f), new Color(0.5f, 0f, 0.5f),
            
            // 网格常用颜色
            new Color(0.6f, 0.6f, 0.6f, 0.6f),          // 默认网格颜色
            new Color(0.9f, 0.9f, 0.9f, 0.8f),          // 默认主网格颜色
            new Color(0.5f, 0.5f, 0.5f, 0.8f),          // 深灰网格
            new Color(1f, 1f, 1f, 0.5f),                // 半透明白色
            
            // 透明度变化
            new Color(1f, 0f, 0f, 0.5f),                // 半透明红色
            new Color(0f, 1f, 0f, 0.5f),                // 半透明绿色
            new Color(0f, 0f, 1f, 0.5f),                // 半透明蓝色
            new Color(1f, 1f, 0f, 0.5f),                // 半透明黄色
        };

        // 事件
        public Action<Color> OnColorConfirmed;
        public Action OnColorCancelled;

        public ColorPickerDialog(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
            IsVisible = false;
        }

        public override void Initialize()
        {
            // 查找UI控件
            colorPreview = FindElement<VisualElement>("ColorPreview");
            presetColorsGrid = FindElement<VisualElement>("PresetColorsGrid");
            
            // RGB滑块和输入框
            redSlider = FindElement<Slider>("RedSlider");
            greenSlider = FindElement<Slider>("GreenSlider");
            blueSlider = FindElement<Slider>("BlueSlider");
            alphaSlider = FindElement<Slider>("AlphaSlider");
            
            redField = FindElement<IntegerField>("RedField");
            greenField = FindElement<IntegerField>("GreenField");
            blueField = FindElement<IntegerField>("BlueField");
            alphaField = FindElement<IntegerField>("AlphaField");
            
            hexField = FindElement<TextField>("HexField");
            
            // 按钮
            confirmButton = FindElement<Button>("ConfirmButton");
            cancelButton = FindElement<Button>("CancelButton");

            if (colorPreview == null || presetColorsGrid == null ||
                redSlider == null || greenSlider == null || blueSlider == null || alphaSlider == null ||
                confirmButton == null || cancelButton == null)
            {
                Debug.LogError("ColorPickerDialog: 缺少必要的UI控件");
                return;
            }

            // 初始化预设颜色网格
            InitializePresetColors();
            
            // 注册事件
            RegisterEvents();
            
            // 添加ESC键关闭功能
            root.RegisterCallback<KeyDownEvent>(OnKeyDown);
            
            Debug.Log("ColorPickerDialog 初始化完成");
        }

        /// <summary>
        /// 显示颜色选择器
        /// </summary>
        /// <param name="initialColor">初始颜色</param>
        public void ShowDialog(Color initialColor)
        {
            currentColor = initialColor;
            UpdateUI();
            BringToFront();
            IsVisible = true;
        }

        /// <summary>
        /// 显示颜色选择器并设置回调函数
        /// </summary>
        /// <param name="initialColor">初始颜色</param>
        /// <param name="onConfirmed">确认回调</param>
        /// <param name="onCancelled">取消回调</param>
        public void ShowDialog(Color initialColor, Action<Color> onConfirmed, Action onCancelled = null)
        {
            currentColor = initialColor;
            OnColorConfirmed = onConfirmed;
            OnColorCancelled = onCancelled;
            UpdateUI();
            BringToFront();
            IsVisible = true;
        }

        /// <summary>
        /// 初始化预设颜色网格
        /// </summary>
        private void InitializePresetColors()
        {
            if (presetColorsGrid == null) return;

            presetColorsGrid.Clear();

            foreach (var color in PresetColors)
            {
                var colorButton = new Button()
                {
                    style = {
                        backgroundColor = color
                    }
                };
                colorButton.AddToClassList("color-preset-button");

                colorButton.clicked += () => OnPresetColorSelected(color);
                presetColorsGrid.Add(colorButton);
            }
        }

        /// <summary>
        /// 注册事件
        /// </summary>
        private void RegisterEvents()
        {
            // RGB滑块事件
            redSlider.RegisterValueChangedCallback(evt => OnRGBChanged());
            greenSlider.RegisterValueChangedCallback(evt => OnRGBChanged());
            blueSlider.RegisterValueChangedCallback(evt => OnRGBChanged());
            alphaSlider.RegisterValueChangedCallback(evt => OnRGBChanged());

            // RGB输入框事件
            redField.RegisterValueChangedCallback(evt => OnRGBFieldChanged());
            greenField.RegisterValueChangedCallback(evt => OnRGBFieldChanged());
            blueField.RegisterValueChangedCallback(evt => OnRGBFieldChanged());
            alphaField.RegisterValueChangedCallback(evt => OnRGBFieldChanged());

            // 十六进制输入事件
            hexField.RegisterValueChangedCallback(evt => OnHexChanged(evt.newValue));

            // 按钮事件
            confirmButton.clicked += OnConfirmClicked;
            cancelButton.clicked += OnCancelClicked;
        }

        /// <summary>
        /// 预设颜色选择事件
        /// </summary>
        private void OnPresetColorSelected(Color color)
        {
            currentColor = color;
            UpdateUI();
        }

        /// <summary>
        /// RGB滑块改变事件
        /// </summary>
        private void OnRGBChanged()
        {
            if (isUpdating) return;

            float r = redSlider.value / 255f;
            float g = greenSlider.value / 255f;
            float b = blueSlider.value / 255f;
            float a = alphaSlider.value / 255f;

            currentColor = new Color(r, g, b, a);
            UpdateUI(updateSliders: false);
        }

        /// <summary>
        /// RGB输入框改变事件
        /// </summary>
        private void OnRGBFieldChanged()
        {
            if (isUpdating) return;

            // 限制输入范围
            int r = Mathf.Clamp(redField.value, 0, 255);
            int g = Mathf.Clamp(greenField.value, 0, 255);
            int b = Mathf.Clamp(blueField.value, 0, 255);
            int a = Mathf.Clamp(alphaField.value, 0, 255);

            currentColor = new Color(r / 255f, g / 255f, b / 255f, a / 255f);
            UpdateUI(updateFields: false);
        }

        /// <summary>
        /// 十六进制输入改变事件
        /// </summary>
        private void OnHexChanged(string hexValue)
        {
            if (isUpdating) return;

            if (TryParseHexColor(hexValue, out Color color))
            {
                currentColor = color;
                UpdateUI(updateHex: false);
            }
        }

        /// <summary>
        /// 更新UI显示
        /// </summary>
        private void UpdateUI(bool updateSliders = true, bool updateFields = true, bool updateHex = true)
        {
            isUpdating = true;

            // 更新颜色预览
            if (colorPreview != null)
            {
                colorPreview.style.backgroundColor = currentColor;
            }

            // 更新滑块
            if (updateSliders)
            {
                redSlider.value = currentColor.r * 255f;
                greenSlider.value = currentColor.g * 255f;
                blueSlider.value = currentColor.b * 255f;
                alphaSlider.value = currentColor.a * 255f;
            }

            // 更新输入框
            if (updateFields)
            {
                redField.value = Mathf.RoundToInt(currentColor.r * 255f);
                greenField.value = Mathf.RoundToInt(currentColor.g * 255f);
                blueField.value = Mathf.RoundToInt(currentColor.b * 255f);
                alphaField.value = Mathf.RoundToInt(currentColor.a * 255f);
            }

            // 更新十六进制
            if (updateHex && hexField != null)
            {
                hexField.value = ColorToHex(currentColor);
            }

            isUpdating = false;
        }

        /// <summary>
        /// 确认按钮点击事件
        /// </summary>
        private void OnConfirmClicked()
        {
            OnColorConfirmed?.Invoke(currentColor);
            IsVisible = false;
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void OnCancelClicked()
        {
            OnColorCancelled?.Invoke();
            IsVisible = false;
        }

        /// <summary>
        /// 颜色转十六进制字符串
        /// </summary>
        private string ColorToHex(Color color)
        {
            int r = Mathf.RoundToInt(color.r * 255f);
            int g = Mathf.RoundToInt(color.g * 255f);
            int b = Mathf.RoundToInt(color.b * 255f);
            int a = Mathf.RoundToInt(color.a * 255f);

            if (a == 255)
            {
                return $"#{r:X2}{g:X2}{b:X2}";
            }
            else
            {
                return $"#{r:X2}{g:X2}{b:X2}{a:X2}";
            }
        }

        /// <summary>
        /// 尝试解析十六进制颜色字符串
        /// </summary>
        private bool TryParseHexColor(string hex, out Color color)
        {
            color = Color.white;

            if (string.IsNullOrEmpty(hex))
                return false;

            // 移除#前缀
            if (hex.StartsWith("#"))
                hex = hex.Substring(1);

            // 检查长度
            if (hex.Length != 6 && hex.Length != 8)
                return false;

            try
            {
                int r = Convert.ToInt32(hex.Substring(0, 2), 16);
                int g = Convert.ToInt32(hex.Substring(2, 2), 16);
                int b = Convert.ToInt32(hex.Substring(4, 2), 16);
                int a = hex.Length == 8 ? Convert.ToInt32(hex.Substring(6, 2), 16) : 255;

                color = new Color(r / 255f, g / 255f, b / 255f, a / 255f);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 处理键盘事件
        /// </summary>
        private void OnKeyDown(KeyDownEvent evt)
        {
            if (evt.keyCode == KeyCode.Escape && IsVisible)
            {
                OnColorCancelled?.Invoke();
                IsVisible = false;
                evt.StopPropagation();
            }
        }

        public override void UpdatePanel()
        {
            // 颜色选择器不需要定期更新
        }

        /// <summary>
        /// 将对话框置于最前端（同级最后），并设置较高 zIndex。
        /// </summary>
        private void BringToFront()
        {
            if (root != null)
            {
                root.BringToFront();
            }
        }
    }
} 