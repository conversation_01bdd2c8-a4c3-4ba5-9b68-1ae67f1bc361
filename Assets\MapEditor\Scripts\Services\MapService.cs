using MapEditor.Core;
using MapEditor.Manager;
using MapEditor.UI;
using UnityEngine;
using System.Linq;
using System.Collections.Generic;
using MapEditor.Data;
using MapEditor.Event;
using MapEditor.Config;

namespace MapEditor.Services
{
    /// <summary>
    /// 地图服务，处理与地图数据相关的业务逻辑
    /// </summary>
    public class MapService:ServiceBase
    {
        private  IMapDataStore _mapDataManager;
        private GridLayerInfo _gridLayerInfo;

    


        
        public override void Initialize()
        {
            _mapDataManager = core.MapDataStore;
            _gridLayerInfo = new GridLayerInfo(core.SceneRenderer);
        }

        public override void Start()
        {
            RegisterEvent<MapDataChangedEvent>(OnMapDataChanged);
        }

        /// <summary>
        /// 创建一个新地图（使用对话框数据）
        /// </summary>
        /// <param name="mapDialogData">新建地图对话框数据</param>
        /// <returns>创建的地图数据</returns>
        public MapData CreateNewMap(NewMapDialogData mapDialogData)
        {
            // 在创建新地图之前，先清理旧地图留下的渲染器，避免重复渲染
            var layerRenderService = GetService<LayerRenderService>();
            layerRenderService?.ClearAll();

            var mapData = core.MapDataStore.CreateNewMap(mapDialogData.mapName, mapDialogData.mapSize, mapDialogData.chunkSize, mapDialogData.directoryPath);
            var groundLayer = core.LayerStore.CreateLayer("地表层", LayerType.Ground);
            core.LayerStore.ActiveLayer = groundLayer;
            groundLayer.LayerSize = mapDialogData.mapSize;

            layerRenderService?.CreateRenderer(groundLayer);

            // 如果选择了基础纹理，则初始化所有chunk的第一个材质
            if (mapDialogData.baseTextureIndex >= 0)
            {
                InitializeGroundLayerWithBaseTexture(groundLayer, mapDialogData.baseTextureIndex);
            }

            // 更新相机边界
            UpdateCameraBounds(mapDialogData.mapSize);

            return mapData as MapData;
        }

        /// <summary>
        /// 创建一个新地图
        /// </summary>
        /// <param name="mapName">地图名称</param>
        /// <param name="mapSize">地图尺寸</param>
        /// <param name="directoryPath">地图目录路径</param>
        /// <returns>创建的地图数据</returns>
        public MapData CreateNewMap(string mapName, Vector2Int mapSize, string directoryPath, int chunkSize = 256)
        {
            // 在创建新地图之前，先清理旧地图留下的渲染器，避免重复渲染
            var layerRenderService = GetService<LayerRenderService>();
            layerRenderService?.ClearAll();

            var mapData = core.MapDataStore.CreateNewMap(mapName, mapSize, chunkSize, directoryPath);
            var groundLayer = core.LayerStore.CreateLayer("地表层", LayerType.Ground);
            core.LayerStore.ActiveLayer = groundLayer;
            groundLayer.LayerSize = mapSize;

            layerRenderService?.CreateRenderer(groundLayer);

            // 更新相机边界
            UpdateCameraBounds(mapSize);

            return mapData as MapData;
        }



        /// <summary>
        /// 使用基础纹理初始化地表图层的所有chunk
        /// </summary>
        /// <param name="groundLayer">地表图层</param>
        /// <param name="baseTextureIndex">基础纹理的全局索引</param>
        private void InitializeGroundLayerWithBaseTexture(IMapLayer groundLayer, int baseTextureIndex)
        {
            if (!(groundLayer is Data.Layers.TilemapLayer tilemapLayer)) return;

            var layerRenderService = GetService<LayerRenderService>();
            var layerRenderer = layerRenderService?.GetRenderer(groundLayer) as MapEditor.Rendering.Layers.TilemapLayerRenderer;
            
            if (layerRenderer == null)
            {
                Debug.LogError("无法获取TilemapLayerRenderer来初始化基础纹理");
                return;
            }

            int desiredChannel = 0;

            // 计算需要初始化的chunk数量
            Vector2Int realSize = tilemapLayer.RealLayerSize;
            int chunkSize = tilemapLayer.ChunkSize;
            int chunkCountX = Mathf.CeilToInt(realSize.x / (float)chunkSize);
            int chunkCountY = Mathf.CeilToInt(realSize.y / (float)chunkSize);

            Debug.Log($"开始初始化地表图层基础纹理，chunk数量: {chunkCountX} x {chunkCountY}, 纹理索引: {baseTextureIndex}");

            // 为每个chunk设置基础纹理并填充权重为1
            for (int x = 0; x < chunkCountX; x++)
            {
                for (int y = 0; y < chunkCountY; y++)
                {
                    var coord = new ChunkCoord(x, y);
                    // 确保 chunk 存在
                    var chunk = tilemapLayer.GetOrCreateChunk<Data.Chunks.TilemapChunk>(coord);

                    // 为每个 Chunk 分配基础纹理到通道0
                    int assigned = chunk.GetOrAssignChannel(baseTextureIndex);
                    if (assigned != desiredChannel)
                    {
                        Debug.LogWarning($"[MapService] 初始化基础纹理: Chunk({coord.X},{coord.Y}) 通道分配不为0, 实际 {assigned}");
                    }

                    // 初始化该chunk的权重，让基础纹理权重为1
                    InitializeChunkWeights(layerRenderer, coord, desiredChannel);
                }
            }

            // 强制更新所有渲染代理的材质映射
            RefreshAllProxyMaterialMappings(layerRenderer);

            // 触发一次自动保存
            Debug.Log("基础纹理初始化完成，触发自动保存");
        }

        /// <summary>
        /// 强制刷新所有渲染代理的材质映射
        /// </summary>
        /// <param name="layerRenderer">图层渲染器</param>
        private void RefreshAllProxyMaterialMappings(MapEditor.Rendering.Layers.TilemapLayerRenderer layerRenderer)
        {
            // 使用图层渲染器的公共方法刷新材质映射
            layerRenderer.RefreshAllProxyMaterialMappings();
        }

        /// <summary>
        /// 初始化chunk的权重纹理，让指定通道权重为1
        /// </summary>
        /// <param name="layerRenderer">图层渲染器</param>
        /// <param name="coord">chunk坐标</param>
        /// <param name="channel">要设置为1的通道</param>
        private void InitializeChunkWeights(MapEditor.Rendering.Layers.TilemapLayerRenderer layerRenderer, ChunkCoord coord, int channel)
        {
            // 使用新添加的公共方法来获取权重RT
            var rts = layerRenderer.GetOrCreateChunkRTsPublic(coord);
            if (rts != null && rts.Length >= 2)
            {
                // 初始化权重：设置指定通道权重为1，coverage为1
                FillWeightTexturesForBaseTexture(rts, channel);
                
                // 标记为脏，确保会被保存
                layerRenderer.MarkChunkWeightsDirty(coord);
            }
        }

        /// <summary>
        /// 为基础纹理初始化权重纹理：指定通道权重为1，coverage为1，其他通道权重为0
        /// </summary>
        /// <param name="rts">权重RT数组 [Splat0, Splat1]</param>
        /// <param name="channel">要设置为1的通道（0-6）</param>
        private void FillWeightTexturesForBaseTexture(RenderTexture[] rts, int channel)
        {
            if (rts == null || rts.Length < 2) return;

            RenderTexture splat0 = rts[0];
            RenderTexture splat1 = rts[1];

            // === 计算两张贴图的填充颜色 (0-255) ===
            Color32 splat0Color = new Color32(0, 0, 0, 0);
            Color32 splat1Color = new Color32(0, 0, 0, 255); // 默认 coverage = 1

            if (channel < 4)
            {
                byte one = 255;
                switch (channel)
                {
                    case 0: splat0Color = new Color32(one, 0, 0, 0); break;
                    case 1: splat0Color = new Color32(0, one, 0, 0); break;
                    case 2: splat0Color = new Color32(0, 0, one, 0); break;
                    case 3: splat0Color = new Color32(0, 0, 0, one); break;
                }
            }
            else if (channel >= 4 && channel < 7)
            {
                byte one = 255;
                switch (channel)
                {
                    case 4: splat1Color = new Color32(one, 0, 0, 255); break;
                    case 5: splat1Color = new Color32(0, one, 0, 255); break;
                    case 6: splat1Color = new Color32(0, 0, one, 255); break;
                }
            }

            // === 使用 Burst Job 执行填充 ===
            MapEditor.Utility.TextureFillJobs.FillRenderTexture(splat0, splat0Color);
            MapEditor.Utility.TextureFillJobs.FillRenderTexture(splat1, splat1Color);
        }

        public IMapLayer CreateLayer(string layerName, LayerType layerType)
        {
            var layer = core.LayerStore.CreateLayer(layerName, layerType);
            if (layer == null)
            {
                // 图层创建失败（如重名等），直接返回null
                Debug.LogWarning($"图层创建失败: {layerName}");
                return null;
            }
            
            layer.LayerSize= core.MapDataStore.CurrentMap.MapSize;
            // 预创建 Chunk 由对应 Renderer 负责
            var layerRenderService = GetService<LayerRenderService>();
           
            // 直接使用统一的CreateRenderer方法
            layerRenderService?.CreateRenderer(layer);
            
            return layer;
        }

        public IMapLayer GetActiveLayer()
        {
            return core.LayerStore.ActiveLayer;
        }

        public ILayerRenderer GetActiveLayerRenderer()
        {
            var layer = GetActiveLayer();
            Debug.Log($"Active layer: {layer.LayerName}");
            var layerRenderService = GetService<LayerRenderService>();
            var layerRenderer = layerRenderService?.GetRenderer(layer);
            Debug.Log($"Active  layer: {layer.LayerId} layer renderer: {layerRenderer?.ID}");
            return layerRenderer;
        }

        /// <summary>
        /// 保存当前地图
        /// </summary>
        public void SaveMap()
        {
            // 通过事件驱动统一保存流程
            PublishEvent(new SaveRequestEvent(false));
        }

        /// <summary>
        /// 加载指定ID的地图
        /// </summary>
        /// <param name="mapId">地图ID</param>
        public void LoadMap(string mapId)
        {
            var map = _mapDataManager.LoadMap(mapId) as MapData;
            HandleLoadedMap(map);
        }

        public void LoadMapFromFile(string filePath)
        {
            var map = _mapDataManager.LoadMapFromFile(filePath) as MapData;
            HandleLoadedMap(map);
        }

        private void HandleLoadedMap(MapData map)
        {
            if (map == null)
            {
                Debug.LogError("加载地图失败");
                return;
            }

            // 0. 校正旧地图的 SortingOrder，确保落入新的区间
            NormalizeLayerOrders(map);

            // 2. 清理并重建渲染器
            var layerRenderService = GetService<LayerRenderService>();
            layerRenderService?.ClearAll();

            // 3. 按类型为每个图层创建渲染器
            foreach (var layer in map.GetAllLayers())
            {
                layerRenderService?.CreateRenderer(layer);
            }

            // 重新构建各类型图层的渲染代理（贴图/对象 等）
            foreach (var layer in map.GetAllLayers())
            {
                if (layer is MapLayer ml)
                {
                    switch (layer.Type)
                    {
                        case LayerType.Ground:
                            // 地表层：重放 Chunk 贴图
                            var tlr = layerRenderService?.GetRenderer(layer) as MapEditor.Rendering.Layers.TilemapLayerRenderer;
                            tlr?.BuildProxiesFromSavedChunks(ml);
                            break;
                        case LayerType.Object:
                            // 对象层：重建对象实例
                            var olr = layerRenderService?.GetRenderer(layer) as MapEditor.Rendering.Layers.ObjectLayerRenderer;
                            olr?.BuildProxiesFromSavedChunks(ml);
                            break;
                    }
                }
            }

            // 4. 设置活动图层为首个图层
            var firstLayer = map.GetAllLayers().FirstOrDefault();
            if (firstLayer != null)
            {
                core.LayerStore.ActiveLayer = firstLayer;
            }

            // 5. 触发一次渲染刷新
            layerRenderService?.UpdateAll();

            // 6. 更新相机边界
            UpdateCameraBounds(map.MapSize);
        }

        /// <summary>
        /// 获取当前地图数据
        /// </summary>
        public IMapData CurrentMap => _mapDataManager.CurrentMap;

        /// <summary>
        /// 设置活动图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        public void SetActiveLayer(string layerId)
        {
            // 特殊处理网格层
            if (layerId == "__grid__")
            {
                core.LayerStore.ActiveLayer = _gridLayerInfo;
                return;
            }
            
            core.LayerStore.SetActiveLayer(layerId);
        }

        /// <summary>
        /// 获取当前地图的所有图层
        /// </summary>
        /// <returns>图层数组</returns>
        public IMapLayer[] GetAllLayers()
        {
            if (core.MapDataStore.CurrentMap == null)
            {
                return new IMapLayer[0];
            }

            var regularLayers = core.MapDataStore.CurrentMap.GetAllLayers().ToList();
            // 添加网格层到末尾
            regularLayers.Add(_gridLayerInfo);
            return regularLayers.ToArray();
        }

        /// <summary>
        /// 设置图层可见性，同时同步渲染器的显示/隐藏。
        /// </summary>
        /// <param name="layer">要设置的图层</param>
        /// <param name="isVisible">是否可见</param>
        public void SetLayerVisibility(IMapLayer layer, bool isVisible)
        {
            if (layer == null) return;

            // 特殊处理网格层
            if (layer is GridLayerInfo gridLayer)
            {
                gridLayer.IsVisible = isVisible;
                return;
            }

            layer.IsVisible = isVisible;

            var layerRenderService = GetService<LayerRenderService>();
            layerRenderService?.SetRendererVisibility(layer, isVisible);
        }

        public void DeleteLayer(string layerId)
        {
            if (string.IsNullOrEmpty(layerId)) return;

            // 获取待删除图层
            var layerStore = core.LayerStore;
            var targetLayer = layerStore.GetLayerById(layerId);
            if (targetLayer == null)
            {
                Debug.LogWarning($"[MapService] DeleteLayer: 找不到 ID 为 {layerId} 的图层");
                return;
            }

            // 先销毁渲染器（如果存在）
            var layerRenderService = GetService<LayerRenderService>();
            layerRenderService?.RemoveRenderer(targetLayer);

            // 从数据层删除
            layerStore.DeleteLayer(layerId);
        }

        /// <summary>
        /// 重命名图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        /// <param name="newName">新名称</param>
        public void RenameLayer(string layerId, string newName)
        {
            if (string.IsNullOrEmpty(layerId) || string.IsNullOrEmpty(newName)) return;
            
            var layerStore = core.LayerStore;
            layerStore.RenameLayer(layerId, newName);
        }

        public IEnumerable<MapInfo> GetAllSavedMaps()
        {
            return _mapDataManager.ListSavedMaps();
        }

        /// <summary>
        /// 地图数据变更事件处理
        /// </summary>
        /// <param name="evt">事件参数</param>
        private void OnMapDataChanged(MapDataChangedEvent evt)
        {
            Debug.Log($"[MapService] Map data changed: {evt.ChangeType}, Layer: {evt.LayerId}");

            var affectedLayer = GetAffectedRenderLayer(evt.ChangeType);

            // 直接发布渲染更新事件
            PublishEvent(new RenderUpdateEvent
            {
                Layer = affectedLayer,
                UpdateArea = evt.AffectedArea
            });
        }

        /// <summary>
        /// 根据数据变更类型获取受影响的渲染层
        /// </summary>
        private RenderLayer GetAffectedRenderLayer(MapDataChangeType changeType)
        {
            switch (changeType)
            {
                case MapDataChangeType.TerrainModified:
                    return RenderLayer.Ground;
                case MapDataChangeType.ObjectAdded:
                case MapDataChangeType.ObjectRemoved:
                case MapDataChangeType.ObjectModified:
                    return RenderLayer.Object;
                default:
                    return RenderLayer.Ground;
            }
        }

        /// <summary>
        /// 确保所有图层的 Order 落在当前配置的区间内，若冲突则重新分配。
        /// </summary>
        private void NormalizeLayerOrders(MapData map)
        {
            if (map == null) return;

            // Ground 与 Object 两类区间（以后可扩展至更多类型）
            var groundRange = SortingOrderConfig.GroundRange;
            var objectRange = SortingOrderConfig.ObjectRange;

            var groundUsed = new HashSet<int>();
            var objectUsed = new HashSet<int>();

            // 第一轮：收集已使用且合法的 Order
            foreach (var layer in map.GetAllLayers())
            {
                switch (layer.Type)
                {
                    case LayerType.Ground:
                        if (layer.Order >= groundRange.min && layer.Order <= groundRange.max)
                            groundUsed.Add(layer.Order);
                        break;
                    case LayerType.Object:
                        if (layer.Order >= objectRange.min && layer.Order <= objectRange.max)
                            objectUsed.Add(layer.Order);
                        break;
                }
            }

            // 分配器函数
            int Allocate(HashSet<int> used, (int min, int max) range)
            {
                for (int o = range.min; o <= range.max; o++)
                    if (!used.Contains(o)) return o;
                return range.max; // 已满时回退到 max
            }

            // 第二轮：修复不合法的 Order
            foreach (var layer in map.GetAllLayers())
            {
                switch (layer.Type)
                {
                    case LayerType.Ground:
                        if (layer.Order < groundRange.min || layer.Order > groundRange.max)
                        {
                            int newOrder = Allocate(groundUsed, groundRange);
                            groundUsed.Add(newOrder);
                            layer.Order = newOrder;
                        }
                        break;
                    case LayerType.Object:
                        if (layer.Order < objectRange.min || layer.Order > objectRange.max)
                        {
                            int newOrder = Allocate(objectUsed, objectRange);
                            objectUsed.Add(newOrder);
                            layer.Order = newOrder;
                        }
                        break;
                }
            }
        }

        public void SetLayerOrder(string layerId, int newOrder)
        {
            if (string.IsNullOrEmpty(layerId)) return;

            var layerStore = core.LayerStore;
            var targetLayer = layerStore.GetLayerById(layerId);
            if (targetLayer == null)
            {
                Debug.LogWarning($"[MapService] SetLayerOrder: 找不到 ID 为 {layerId} 的图层");
                return;
            }

            // 获取允许的区间
            (int min, int max) range = targetLayer.Type switch
            {
                LayerType.Ground => SortingOrderConfig.GroundRange,
                LayerType.Object => SortingOrderConfig.ObjectRange,
                LayerType.Decoration => SortingOrderConfig.PreviewRange, // 先借用 Preview 区间
                LayerType.Logic => SortingOrderConfig.UIRange,
                LayerType.Grid => (SortingOrderConfig.GridFixedOrder, SortingOrderConfig.GridFixedOrder),
                _ => (0, 0)
            };

            if (newOrder < range.min || newOrder > range.max)
            {
                Debug.LogWarning($"[MapService] SetLayerOrder: 排序值 {newOrder} 不在允许区间 ({range.min}-{range.max})");
                return;
            }

            // 直接覆盖并刷新
            targetLayer.Order = newOrder;

            // 通知渲染器刷新
            var layerRenderService = GetService<LayerRenderService>();
            layerRenderService?.UpdateAll();

            // 广播图层变更事件，供 UI 更新
            PublishEvent(new UI.LayerChangedEvent { Layer = targetLayer });
        }

        private void UpdateCameraBounds(Vector2Int mapSize)
        {
            // 将像素尺寸转换为世界单位，1 世界单位 = PixelsPerUnit 像素
            float unitsPerPixel = 1f / MapEditor.Core.MapEditorConfig.PixelsPerUnit;
            Vector2 boundsMin = Vector2.zero;
            Vector2 boundsMax = new Vector2(mapSize.x * unitsPerPixel, mapSize.y * unitsPerPixel);

            // 获取场景渲染器中的相机
            var camera = core.SceneRenderer?.RenderCamera;
            if (camera == null) return;

            var viewCamera = camera.GetComponent<MapEditor.Manager.MapViewCamera>();
            if (viewCamera == null) return;

            viewCamera.SetBounds(boundsMin, boundsMax);
        }
    }
}