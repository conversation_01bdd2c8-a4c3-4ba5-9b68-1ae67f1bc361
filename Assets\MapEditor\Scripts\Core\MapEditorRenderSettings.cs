using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace MapEditor.Core
{
    /// <summary>
    /// 地图编辑器渲染设置工具类，用于配置URP 2D渲染管线
    /// </summary>
    public static class MapEditorRenderSettings
    {
        /// <summary>
        /// 配置URP 2D渲染管线设置
        /// </summary>
        public static void ConfigureURP2D()
        {
            Debug.Log("Configuring URP 2D Render Pipeline settings...");
            
            // 获取当前的URP资产
            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                Debug.LogError("No Universal Render Pipeline asset found! Please ensure URP is installed and configured.");
                return;
            }
            
            // 配置URP设置
            urpAsset.supportsHDR = false;              // 2D不需要HDR
            urpAsset.msaaSampleCount = 2;              // 使用2x MSAA
            urpAsset.renderScale = 1.0f;               // 渲染比例为1.0
            urpAsset.supportsCameraDepthTexture = false; // 关闭深度纹理
            urpAsset.supportsCameraOpaqueTexture = true; // 开启不透明纹理
            urpAsset.useSRPBatcher = true;             // 使用SRP批处理
            
            Debug.Log("URP 2D configuration completed.");
        }
        
        /// <summary>
        /// 配置排序图层
        /// </summary>
        public static void ConfigureSortingLayers()
        {
            // 注意：Runtime无法创建SortingLayer，需要在编辑器中预先设置
            // 这个方法是为了文档完整性
            Debug.Log("Sorting layers should be configured in the Unity Editor.");
            Debug.Log("Required sorting layers: Ground, Grid, Object, Preview, UI");
        }
    }
} 