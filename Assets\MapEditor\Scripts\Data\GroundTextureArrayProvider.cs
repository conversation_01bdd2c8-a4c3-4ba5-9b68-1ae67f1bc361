using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Data
{
    /// <summary>
    /// 提供 <see cref="Texture2DArray"/> 形式的地表纹理合集，解除单次绑定 7 张贴图的限制。
    /// 在首次访问时会自动扫描 <see cref="GroundTextureSO"/> 并构建 Texture2DArray，
    /// 生成结果只存在于运行时内存，不会写入磁盘 Asset。若需要离线产出可再扩展。
    /// </summary>
    public static class GroundTextureArrayProvider
    {
        private static Texture2DArray _surfaceArray;
        private static Dictionary<int, int> _indexToLayer; // globalIndex -> array layer
        private static Vector2[] _layerUVScales; // 与 layer 对应的 uvScale
        private static bool _initialized;

        /// <summary>
        /// 获取 Texture2DArray。会在第一次调用时自动初始化。
        /// </summary>
        public static Texture2DArray SurfaceArray
        {
            get
            {
                Initialize();
                return _surfaceArray;
            }
        }

        /// <summary>
        /// 将全局材质索引映射到 Texture2DArray 的 layer 索引。
        /// 若不存在返回 -1。
        /// </summary>
        public static int GetLayer(int globalTextureIndex)
        {
            Initialize();
            return _indexToLayer.TryGetValue(globalTextureIndex, out var layer) ? layer : -1;
        }

        /// <summary>
        /// 获取指定全局索引对应的 UV 平铺系数（若不存在返回 Vector2.one）。
        /// </summary>
        public static Vector2 GetUVScale(int globalTextureIndex)
        {
            Initialize();
            if (_indexToLayer != null && _indexToLayer.TryGetValue(globalTextureIndex, out var layer))
            {
                if (_layerUVScales != null && layer >= 0 && layer < _layerUVScales.Length)
                    return _layerUVScales[layer];
            }
            return Vector2.one;
        }

        private static void Initialize()
        {
            if (_initialized) return;

            // 1) 优先尝试加载离线生成的资源
            var prebuiltAsset = Resources.Load<MapEditor.Data.GroundTexturesArrayAsset>("GroundTextures/GroundTexturesArray");
            if (prebuiltAsset != null && prebuiltAsset.surfaceArray != null)
            {
                _surfaceArray = prebuiltAsset.surfaceArray;
                _indexToLayer = new Dictionary<int, int>();
                for (int i = 0; i < prebuiltAsset.globalIndices.Length; i++)
                {
                    int gIdx = prebuiltAsset.globalIndices[i];
                    if (!_indexToLayer.ContainsKey(gIdx))
                        _indexToLayer.Add(gIdx, i);
                }
                _layerUVScales = prebuiltAsset.uvScales;
                _initialized = true;
                return;
            }

            // 2) 否则回退到运行时扫描逻辑
            GroundTextureSO[] allSos = GroundTextureProvider.GetAllTextures();
            if (allSos == null || allSos.Length == 0)
            {
                Debug.LogError("[GroundTextureArrayProvider] 未找到任何 GroundTextureSO，Texture2DArray 初始化失败。");
                _surfaceArray = new Texture2DArray(1, 1, 1, TextureFormat.RGBA32, false);
                _indexToLayer = new Dictionary<int, int>();
                _initialized = true;
                return;
            }

            // 取第一张纹理的尺寸/格式做基准
            Texture2D firstTex = allSos[0].texture;
            if (firstTex == null)
            {
                Debug.LogError("[GroundTextureArrayProvider] 首张 GroundTextureSO 的纹理为空。");
                return;
            }

            int width = firstTex.width;
            int height = firstTex.height;
            // 为保证兼容性直接使用 RGBA32
            TextureFormat format = TextureFormat.RGB24;

            _surfaceArray = new Texture2DArray(width, height, allSos.Length, format, false)
            {
                filterMode = FilterMode.Bilinear,
                wrapMode = TextureWrapMode.Repeat
            };

            _indexToLayer = new Dictionary<int, int>();

            for (int i = 0; i < allSos.Length; i++)
            {
                GroundTextureSO so = allSos[i];
                if (so?.texture == null)
                {
                    Debug.LogWarning($"[GroundTextureArrayProvider] 索引 {i} 的 GroundTextureSO 或其 texture 为空，填充为灰色贴图。");
                    Texture2D gray = Texture2D.grayTexture;
                    CopyTextureSafe(gray, i);
                    continue;
                }

                // 统一使用 CPU CopyPixels 路径，避免 Graphics.CopyTexture 在混合压缩/格式时失败却无异常
                CopyPixelsFallback(so.texture, i, width, height);

                if (!_indexToLayer.ContainsKey(so.textureIndex))
                {
                    _indexToLayer.Add(so.textureIndex, i);
                }
                else
                {
                    Debug.LogWarning($"[GroundTextureArrayProvider] Duplicate textureIndex {so.textureIndex} detected. Only first occurrence kept.");
                }

                // 记录 uvScale
                if (_layerUVScales == null) _layerUVScales = new Vector2[allSos.Length];
                _layerUVScales[i] = so.uvScale;
            }

            _surfaceArray.Apply(false, true);
            _initialized = true;
        }

        /// <summary>
        /// 当源纹理尺寸或格式不匹配时，通过 CPU 像素拷贝的方式写入 TextureArray。
        /// </summary>
        private static void CopyPixelsFallback(Texture2D src, int destLayer, int targetW, int targetH)
        {
            Texture2D readable;

            // 如果源纹理不可读，先通过 RenderTexture 创建一个可读副本
            if (!src.isReadable)
            {
                RenderTexture rt = RenderTexture.GetTemporary(src.width, src.height, 0, RenderTextureFormat.ARGB32);
                Graphics.Blit(src, rt);
                RenderTexture prev = RenderTexture.active;
                RenderTexture.active = rt;
                readable = new Texture2D(src.width, src.height, TextureFormat.RGBA32, false);
                readable.ReadPixels(new Rect(0, 0, src.width, src.height), 0, 0);
                readable.Apply();
                RenderTexture.active = prev;
                RenderTexture.ReleaseTemporary(rt);
            }
            else
            {
                readable = src;
            }

            // 若尺寸不同，缩放到目标尺寸
            if (readable.width != targetW || readable.height != targetH)
            {
                Texture2D scaled = new Texture2D(targetW, targetH, TextureFormat.RGBA32, false);
                // 使用简单缩放：RenderTexture
                RenderTexture rt = RenderTexture.GetTemporary(targetW, targetH, 0, RenderTextureFormat.ARGB32);
                Graphics.Blit(readable, rt);
                RenderTexture prev = RenderTexture.active;
                RenderTexture.active = rt;
                scaled.ReadPixels(new Rect(0, 0, targetW, targetH), 0, 0);
                scaled.Apply();
                RenderTexture.active = prev;
                RenderTexture.ReleaseTemporary(rt);
                if (readable != src && readable != scaled) Object.Destroy(readable);
                readable = scaled;
            }

            _surfaceArray.SetPixels(readable.GetPixels(), destLayer);
            if (readable != src) Object.Destroy(readable);
        }

        /// <summary>
        /// 对于小尺寸或格式兼容的纹理，优先尝试 GPU CopyTexture，若失败则退回到 CPU 路径。
        /// </summary>
        private static void CopyTextureSafe(Texture2D src, int destLayer)
        {
            try
            {
                Graphics.CopyTexture(src, 0, 0, _surfaceArray, destLayer, 0);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[GroundTextureArrayProvider] Graphics.CopyTexture failed: {e.Message}. Fallback to CPU copy.");
                CopyPixelsFallback(src, destLayer, _surfaceArray.width, _surfaceArray.height);
            }
        }
    }
} 