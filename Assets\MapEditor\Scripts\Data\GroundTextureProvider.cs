using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Data
{
    /// <summary>
    /// 提供运行时对 <see cref="GroundTextureSO"/> 的集中访问与索引映射。
    /// </summary>
    public static class GroundTextureProvider
    {
        private static GroundTextureSO[] _allTextures;
        private static Dictionary<int, GroundTextureSO> _indexLookup;
        private static bool _initialized;

        private static void Initialize()
        {
            if (_initialized) return;
            _allTextures = Resources.LoadAll<GroundTextureSO>("GroundTextures");

            // 构建查表字典
            _indexLookup = new Dictionary<int, GroundTextureSO>();
            foreach (var so in _allTextures)
            {
                if (so == null) continue;
                if (!_indexLookup.ContainsKey(so.textureIndex))
                {
                    _indexLookup.Add(so.textureIndex, so);
                }
                else
                {
                    Debug.LogWarning($"[GroundTextureProvider] Duplicate textureIndex {so.textureIndex} detected in asset {so.name}.");
                }
            }

            // 按 textureIndex 升序排序，便于 UI 显示
            System.Array.Sort(_allTextures, (a, b) => a.textureIndex.CompareTo(b.textureIndex));

            _initialized = true;
        }

        /// <summary>
        /// 获取所有纹理（已按 index 排序）。
        /// </summary>
        public static GroundTextureSO[] GetAllTextures()
        {
            Initialize();
            return _allTextures;
        }

        /// <summary>
        /// 根据全局索引返回纹理（若不存在返回 grayTexture）。
        /// </summary>
        public static Texture2D GetTexture(int index)
        {
            Initialize();
            return _indexLookup.TryGetValue(index, out var so) && so.texture != null ? so.texture : Texture2D.grayTexture;
        }

        /// <summary>
        /// 获取 SO 对象本身，若不存在则返回 null。
        /// </summary>
        public static GroundTextureSO GetSO(int index)
        {
            Initialize();
            _indexLookup.TryGetValue(index, out var so);
            return so;
        }
    }
} 