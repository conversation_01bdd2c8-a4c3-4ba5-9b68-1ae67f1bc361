using System;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Services;

namespace MapEditor.UI
{
    /// <summary>
    /// 图层设置对话框，仅提供 SortingOrder 调整。
    /// 右键图层 -> 图层设置… 打开。
    /// </summary>
    public partial class LayerSettingsDialog : UIPanel
    {
        public override string PanelId => "layerSettingsDialog";
        public override string DisplayName => "图层设置";

        private IntegerField orderField;
        private Label rangeLabel;
        private Button confirmButton;
        private Button cancelButton;
        // 扩展设置容器
        private VisualElement extraContainer;

        private IMapLayer targetLayer;
        private MapService _mapService;
        // 复用 GridSettingsPanel
        private GridSettingsPanel _embeddedGridPanel;

        public LayerSettingsDialog(IUIManager uiManager, VisualTreeAsset template) : base(uiManager, template)
        {
            // 初始隐藏
            IsVisible = false;
        }

        public override void Initialize()
        {
            _mapService = MapEditorCore.Instance.GetService<MapService>();

            // 查找模板中的控件
            orderField   = root.Q<IntegerField>("OrderField");
            rangeLabel   = root.Q<Label>("RangeLabel");
            confirmButton= root.Q<Button>("ConfirmButton");
            cancelButton = root.Q<Button>("CancelButton");
            extraContainer = root.Q<VisualElement>("ExtraContainer");

            if (confirmButton != null) confirmButton.clicked += OnConfirmClicked;
            if (cancelButton != null)  cancelButton.clicked += () => { IsVisible = false; };

            // 清理缓存字段
            _embeddedGridPanel = null;
        }

        /// <summary>
        /// 打开对话框并绑定目标图层
        /// </summary>
        public void ShowDialog(IMapLayer layer)
        {
            if (layer == null) return;
            targetLayer = layer;

            orderField.value = layer.Order;

            // 根据图层类型动态加载扩展模板
            LoadExtraTemplate(layer);

            (int min, int max) range = layer.Type switch
            {
                LayerType.Ground => Config.SortingOrderConfig.GroundRange,
                LayerType.Object => Config.SortingOrderConfig.ObjectRange,
                LayerType.Decoration => Config.SortingOrderConfig.PreviewRange,
                LayerType.Logic => Config.SortingOrderConfig.UIRange,
                LayerType.Grid => (Config.SortingOrderConfig.GridFixedOrder, Config.SortingOrderConfig.GridFixedOrder),
                _ => (0, 0)
            };
            rangeLabel.text = $"允许范围: {range.min} - {range.max}";

            IsVisible = true;
            orderField.Focus();
        }

        /// <summary>
        /// 加载并绑定扩展模板到容器
        /// </summary>
        private void LoadExtraTemplate(IMapLayer layer)
        {
            if (extraContainer == null) return;

            // 清空旧内容
            extraContainer.Clear();

            // Grid 图层直接复用 GridSettingsPanel，无需加载额外模板
            if (layer.Type == LayerType.Grid)
            {
                BindExtraControls(layer, extraContainer);
                return;
            }

            // 约定资源路径：Resources/UI/LayerSettings_{LayerType}.uxml
            string tplPath = $"UI/LayerSettings_{layer.Type}";
            var tpl = Resources.Load<VisualTreeAsset>(tplPath);
            if (tpl == null)
            {
                // 无专用模板则保持为空
                return;
            }
            var inst = tpl.Instantiate();
            extraContainer.Add(inst);
            // 如有需要，在此绑定数据到控件
            BindExtraControls(layer, inst);
        }

        /// <summary>
        /// 绑定扩展控件的初始值（按需实现）
        /// </summary>
        private void BindExtraControls(IMapLayer layer, VisualElement root)
        {
            if (layer.Type != LayerType.Grid) return;

            // 复用现有 GridSettingsPanel
            var gridPanel = UIManager.Instance.GetPanel("GridSettings") as GridSettingsPanel;
            if (gridPanel == null)
            {
                var tpl = Resources.Load<VisualTreeAsset>("UI/GridSettingsPanel");
                gridPanel = new GridSettingsPanel(UIManager.Instance, tpl);
                UIManager.Instance.RegisterPanel(gridPanel);
            }

            // 将其根元素移动到 ExtraContainer
            if (gridPanel.Root.parent != null)
            {
                gridPanel.Root.parent.Remove(gridPanel.Root);
            }

            gridPanel.IsVisible = true;
            root.Add(gridPanel.Root);

            _embeddedGridPanel = gridPanel;
        }

        private void OnConfirmClicked()
        {
            if (targetLayer == null) { IsVisible = false; return; }

            int newOrder = orderField.value;
            _mapService?.SetLayerOrder(targetLayer.LayerId, newOrder);

            // 应用扩展控件修改
            ApplyExtraControls(targetLayer);

            IsVisible = false;
        }

        /// <summary>
        /// 从扩展控件读取值写回数据层（按需实现）
        /// </summary>
        private void ApplyExtraControls(IMapLayer layer)
        {
            // 网格层设置通过 GridSettingsPanel 的事件实时生效，无需额外 Apply。
            return;
        }
    }
} 