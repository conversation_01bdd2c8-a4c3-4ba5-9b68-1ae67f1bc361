using System;
using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Data.Chunks
{
    /// <summary>
    /// TilemapChunk 的材质索引表扩展。
    /// </summary>
    public partial class TilemapChunk
    {
        // 长度固定 7 的表，记录 通道 → TextureArray layer。-1 表示空槽
        [SerializeField] private int[] materialIndexTable = new int[7] { -1, -1, -1, -1, -1, -1, -1 };

        /// <summary>
        /// 只读访问素材索引表。
        /// </summary>
        public IReadOnlyList<int> MaterialIndexTable => materialIndexTable;

        /// <summary>
        /// 在当前 Chunk 中获取或分配某个全局材质索引的通道号。
        /// 若通道已满返回 -1。
        /// </summary>
        /// <param name="globalMaterialIndex">全局材质索引</param>
        /// <returns>通道号 0-6，失败返回 -1</returns>
        public int GetOrAssignChannel(int globalMaterialIndex)
        {
            // 将全局索引转换为 TextureArray 的 layer 索引
            int layerIndex = MapEditor.Data.GroundTextureArrayProvider.GetLayer(globalMaterialIndex);
            if (layerIndex < 0)
            {
                Debug.LogError($"[TilemapChunk] 未找到全局材质索引 {globalMaterialIndex} 对应的 TextureArray Layer");
                return -1;
            }

            // 1) 若已存在，直接返回
            for (int i = 0; i < 7; i++)
            {
                if (materialIndexTable[i] == layerIndex)
                {
                    return i;
                }
            }

            // 2) 查找空槽位
            for (int i = 0; i < 7; i++)
            {
                if (materialIndexTable[i] == -1)
                {
                    materialIndexTable[i] = layerIndex;

                    // 标记脏并通知数据层已修改
                    MarkDirty();
                    var ds = MapEditor.Core.MapEditorCore.Instance?.MapDataStore as MapEditor.Data.MapDataStore;
                    ds?.MarkMapChanged();
                    return i;
                }
            }

            // TODO: 后续可实现 LRU 或最小权重替换策略
            Debug.LogWarning($"[TilemapChunk] Chunk 通道已满，无法插入新的材质(layerIdx={layerIndex})。");
            return -1;
        }

        /// <summary>
        /// 清空指定通道的材质映射。
        /// </summary>
        /// <param name="channel">0-6</param>
        public void ClearChannel(int channel)
        {
            if (channel < 0 || channel >= 7) return;
            if (materialIndexTable[channel] == -1) return; // already empty
            materialIndexTable[channel] = -1;
            MarkDirty();
        }
    }
} 