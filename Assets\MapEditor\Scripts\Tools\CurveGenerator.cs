using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Tools
{
    /// <summary>
    /// 曲线生成器，负责从控制点生成平滑曲线段
    /// </summary>
    public class CurveGenerator
    {
        /// <summary>
        /// 根据控制点和设置生成曲线段
        /// </summary>
        /// <param name="controlPoints">控制点列表</param>
        /// <param name="settings">曲线设置</param>
        /// <returns>生成的曲线段列表</returns>
        public List<CurveSegment> GenerateCurve(IReadOnlyList<Vector2> controlPoints, CurveStrokeSettings settings)
        {
            if (controlPoints.Count < 2)
            {
                return new List<CurveSegment>();
            }
            
            switch (settings.curveType)
            {
                case CurveType.Line:
                    return GenerateLinearCurve(controlPoints, settings.resolution);
                    
                case CurveType.CatmullRom:
                    return GenerateCatmullRomSpline(controlPoints, settings.resolution);
                    
                case CurveType.Bezier:
                    return GenerateBezierCurve(controlPoints, settings.resolution);
                    
                default:
                    return GenerateLinearCurve(controlPoints, settings.resolution);
            }
        }
        
        /// <summary>
        /// 生成直线曲线
        /// </summary>
        private List<CurveSegment> GenerateLinearCurve(IReadOnlyList<Vector2> controlPoints, float resolution)
        {
            var segments = new List<CurveSegment>();
            float totalDistance = 0f;
            
            for (int i = 0; i < controlPoints.Count - 1; i++)
            {
                Vector2 start = controlPoints[i];
                Vector2 end = controlPoints[i + 1];
                Vector2 direction = (end - start).normalized;
                Vector2 normal = new Vector2(-direction.y, direction.x); // 垂直向量
                
                float segmentLength = Vector2.Distance(start, end);
                int sampleCount = Mathf.Max(2, Mathf.CeilToInt(segmentLength * resolution));
                
                for (int j = 0; j < sampleCount; j++)
                {
                    float t = j / (float)(sampleCount - 1);
                    Vector2 position = Vector2.Lerp(start, end, t);
                    float distance = totalDistance + t * segmentLength;
                    
                    segments.Add(new CurveSegment(position, direction, normal, distance));
                }
                
                totalDistance += segmentLength;
            }
            
            return segments;
        }
        
        /// <summary>
        /// 生成Catmull-Rom样条曲线
        /// </summary>
        private List<CurveSegment> GenerateCatmullRomSpline(IReadOnlyList<Vector2> controlPoints, float resolution)
        {
            var segments = new List<CurveSegment>();
            
            // 为首尾添加虚拟控制点
            var extendedPoints = new List<Vector2>();
            
            // 首端虚拟点
            Vector2 firstVirtual = controlPoints[0] - (controlPoints[1] - controlPoints[0]);
            extendedPoints.Add(firstVirtual);
            
            // 实际控制点
            for (int i = 0; i < controlPoints.Count; i++)
            {
                extendedPoints.Add(controlPoints[i]);
            }
            
            // 尾端虚拟点
            Vector2 lastVirtual = controlPoints[controlPoints.Count - 1] + 
                                 (controlPoints[controlPoints.Count - 1] - controlPoints[controlPoints.Count - 2]);
            extendedPoints.Add(lastVirtual);
            
            float totalDistance = 0f;
            
            // 在每个控制点段之间生成曲线
            for (int i = 1; i < extendedPoints.Count - 2; i++)
            {
                Vector2 p0 = extendedPoints[i - 1];
                Vector2 p1 = extendedPoints[i];
                Vector2 p2 = extendedPoints[i + 1];
                Vector2 p3 = extendedPoints[i + 2];
                
                float segmentLength = EstimateSegmentLength(p0, p1, p2, p3);
                int sampleCount = Mathf.Max(2, Mathf.CeilToInt(segmentLength * resolution));
                
                for (int j = 0; j < sampleCount; j++)
                {
                    float t = j / (float)(sampleCount - 1);
                    
                    Vector2 position = CatmullRomPoint(p0, p1, p2, p3, t);
                    Vector2 tangent = CatmullRomTangent(p0, p1, p2, p3, t).normalized;
                    Vector2 normal = new Vector2(-tangent.y, tangent.x);
                    float distance = totalDistance + t * segmentLength;
                    
                    segments.Add(new CurveSegment(position, tangent, normal, distance));
                }
                
                totalDistance += segmentLength;
            }
            
            return segments;
        }
        
        /// <summary>
        /// 生成贝塞尔曲线
        /// </summary>
        private List<CurveSegment> GenerateBezierCurve(IReadOnlyList<Vector2> controlPoints, float resolution)
        {
            var segments = new List<CurveSegment>();
            
            if (controlPoints.Count < 3)
            {
                // 如果控制点少于3个，回退到直线模式
                return GenerateLinearCurve(controlPoints, resolution);
            }
            
            // 生成贝塞尔控制点
            var bezierControlPoints = GenerateBezierControlPoints(controlPoints);
            float totalDistance = 0f;
            
            // 每组4个点生成一段三次贝塞尔曲线
            for (int i = 0; i < bezierControlPoints.Count - 3; i += 3)
            {
                Vector2 p0 = bezierControlPoints[i];
                Vector2 p1 = bezierControlPoints[i + 1];
                Vector2 p2 = bezierControlPoints[i + 2];
                Vector2 p3 = bezierControlPoints[i + 3];
                
                float segmentLength = EstimateBezierLength(p0, p1, p2, p3);
                int sampleCount = Mathf.Max(2, Mathf.CeilToInt(segmentLength * resolution));
                
                for (int j = 0; j < sampleCount; j++)
                {
                    float t = j / (float)(sampleCount - 1);
                    
                    Vector2 position = CubicBezierPoint(p0, p1, p2, p3, t);
                    Vector2 tangent = CubicBezierTangent(p0, p1, p2, p3, t).normalized;
                    Vector2 normal = new Vector2(-tangent.y, tangent.x);
                    float distance = totalDistance + t * segmentLength;
                    
                    segments.Add(new CurveSegment(position, tangent, normal, distance));
                }
                
                totalDistance += segmentLength;
            }
            
            return segments;
        }
        
        #region Catmull-Rom样条计算
        
        /// <summary>
        /// 计算Catmull-Rom样条点
        /// </summary>
        private Vector2 CatmullRomPoint(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            float t2 = t * t;
            float t3 = t2 * t;
            
            return 0.5f * (
                (2f * p1) +
                (-p0 + p2) * t +
                (2f * p0 - 5f * p1 + 4f * p2 - p3) * t2 +
                (-p0 + 3f * p1 - 3f * p2 + p3) * t3
            );
        }
        
        /// <summary>
        /// 计算Catmull-Rom样条切线
        /// </summary>
        private Vector2 CatmullRomTangent(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            float t2 = t * t;
            
            return 0.5f * (
                (-p0 + p2) +
                (2f * p0 - 5f * p1 + 4f * p2 - p3) * 2f * t +
                (-p0 + 3f * p1 - 3f * p2 + p3) * 3f * t2
            );
        }
        
        /// <summary>
        /// 估算Catmull-Rom段长度
        /// </summary>
        private float EstimateSegmentLength(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3)
        {
            // 简化估算：使用控制多边形长度的一部分
            float chordLength = Vector2.Distance(p1, p2);
            float netLength = Vector2.Distance(p0, p1) + Vector2.Distance(p1, p2) + Vector2.Distance(p2, p3);
            return (chordLength + netLength) * 0.5f;
        }
        
        #endregion
        
        #region 贝塞尔曲线计算
        
        /// <summary>
        /// 从控制点生成贝塞尔控制点
        /// </summary>
        private List<Vector2> GenerateBezierControlPoints(IReadOnlyList<Vector2> controlPoints)
        {
            var bezierPoints = new List<Vector2>();
            
            if (controlPoints.Count == 2)
            {
                // 两点直线
                bezierPoints.Add(controlPoints[0]);
                bezierPoints.Add(Vector2.Lerp(controlPoints[0], controlPoints[1], 1f/3f));
                bezierPoints.Add(Vector2.Lerp(controlPoints[0], controlPoints[1], 2f/3f));
                bezierPoints.Add(controlPoints[1]);
                return bezierPoints;
            }
            
            // 多点贝塞尔拟合
            bezierPoints.Add(controlPoints[0]);
            
            for (int i = 0; i < controlPoints.Count - 1; i++)
            {
                Vector2 current = controlPoints[i];
                Vector2 next = controlPoints[i + 1];
                
                Vector2 tangent = Vector2.zero;
                
                if (i > 0 && i < controlPoints.Count - 1)
                {
                    // 中间点：使用前后点计算切线
                    Vector2 prev = controlPoints[i - 1];
                    Vector2 nextNext = i + 2 < controlPoints.Count ? controlPoints[i + 2] : next;
                    tangent = (nextNext - prev).normalized;
                }
                else if (i == 0)
                {
                    // 起始点：使用前向差分
                    tangent = (next - current).normalized;
                }
                else
                {
                    // 结束点：使用后向差分
                    Vector2 prev = controlPoints[i - 1];
                    tangent = (current - prev).normalized;
                }
                
                float distance = Vector2.Distance(current, next);
                float controlPointDistance = distance * 0.333f; // 控制点距离为线段长度的1/3
                
                if (i < controlPoints.Count - 1)
                {
                    bezierPoints.Add(current + tangent * controlPointDistance);
                    bezierPoints.Add(next - tangent * controlPointDistance);
                }
            }
            
            bezierPoints.Add(controlPoints[controlPoints.Count - 1]);
            return bezierPoints;
        }
        
        /// <summary>
        /// 计算三次贝塞尔曲线点
        /// </summary>
        private Vector2 CubicBezierPoint(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            float u = 1f - t;
            float u2 = u * u;
            float u3 = u2 * u;
            float t2 = t * t;
            float t3 = t2 * t;
            
            return u3 * p0 + 3f * u2 * t * p1 + 3f * u * t2 * p2 + t3 * p3;
        }
        
        /// <summary>
        /// 计算三次贝塞尔曲线切线
        /// </summary>
        private Vector2 CubicBezierTangent(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3, float t)
        {
            float u = 1f - t;
            float u2 = u * u;
            float t2 = t * t;
            
            return 3f * u2 * (p1 - p0) + 6f * u * t * (p2 - p1) + 3f * t2 * (p3 - p2);
        }
        
        /// <summary>
        /// 估算贝塞尔曲线长度
        /// </summary>
        private float EstimateBezierLength(Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3)
        {
            // 使用控制多边形长度作为上界估算
            float chordLength = Vector2.Distance(p0, p3);
            float netLength = Vector2.Distance(p0, p1) + Vector2.Distance(p1, p2) + Vector2.Distance(p2, p3);
            return (chordLength + netLength) * 0.5f;
        }
        
        #endregion
    }
} 