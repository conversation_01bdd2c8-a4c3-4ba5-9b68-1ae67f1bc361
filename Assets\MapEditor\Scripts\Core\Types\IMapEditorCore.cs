using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 地图编辑器核心接口，管理所有子系统并协调它们之间的交互
    /// </summary>
    public interface IMapEditorCore
    {
        /// <summary>
        /// 场景渲染器，负责所有编辑器内容的可视化
        /// </summary>
        ISceneRenderer SceneRenderer { get; }
        
  
        /// <summary>
        /// 地图数据管理器，负责地图数据的加载、保存和修改
        /// </summary>
        IMapDataStore MapDataStore { get; }
        
        
        /// <summary>
        /// 事件系统，用于系统间通信
        /// </summary>
        IEventSystem EventSystem { get; }
        
        /// <summary>
        /// 图层管理器，负责管理当前活动图层和图层操作
        /// </summary>
        ILayerDataStore LayerStore { get; }

        
        /// <summary>
        /// 初始化编辑器核心系统
        /// </summary>
        void Initialize(IEventSystem eventSystem, ISceneRenderer sceneRenderer, IMapDataStore mapDataManager, ILayerDataStore layerManager);
        void RegisterService<TService>(TService service) where TService : IService;
        void UnregisterService<TService>() where TService : IService;
        TService GetService<TService>() where TService : IService;



        /// <summary>
        /// 关闭编辑器并清理资源
        /// </summary>
        void Shutdown();
    }
}