# Unity 2022 URP 2D 项目 .gitignore

# Unity 生成的文件夹
[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
[Bb]uilds/
[Ll]ogs/
UserSettings/
MemoryCaptures/
*.stacktrace

# Rider
.idea/
*.sln
*.csproj
*.unityproj
*.suo
*.user
*.userprefs
*.pidb
*.booproj
*.svd

# VSCode
.vscode/

# Mac 系统文件
.DS_Store

# Windows 系统文件
Thumbs.db
Desktop.ini

# Unity 本地缓存和锁文件
*.pidb.meta
*.pdb
*.mdb
*.opendb
*.VC.db
*.pid
*.log
*.bak
*.tmp
*.temp
*.old

# Unity Shader 缓存
*.shadercache
*.ShaderCache.db

# Unity Collaborate
.collabignore

# Crash/错误日志
sysinfo.txt
error.log

# 构建输出
*.apk
*.aab
*.xcodeproj
*.xcworkspace
*.xcuserstate
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcsettings
*.xcuserdatad/

# Unity Asset Store 工具
AssetStoreTools*

# 包缓存
Packages/manifest.json.lock
Packages/packages-lock.json

# 其他临时文件
*.swp
*.orig
*.rej
*.diff
*.patch

# 不要忽略 Assets、ProjectSettings、Packages、Doc
!Assets/
!Packages/
!Doc/


# 其他
*.vs/
Library/

# 忽略编辑成生成的地图纹理
TilemapTextures
TilemapTextures.meta
Trees/
Trees.meta
Test1/
Test1.meta
ObjectPrefabs/
ObjectPrefabs.meta
GroundTexturesArray.asset

