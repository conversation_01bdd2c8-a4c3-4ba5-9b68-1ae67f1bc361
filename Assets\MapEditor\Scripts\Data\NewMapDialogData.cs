using System;
using UnityEngine;

namespace MapEditor.Data
{
    /// <summary>
    /// 新建地图对话框数据结构
    /// </summary>
    [Serializable]
    public class NewMapDialogData
    {
        /// <summary>
        /// 地图名称
        /// </summary>
        public string mapName;
        
        /// <summary>
        /// 地图尺寸
        /// </summary>
        public Vector2Int mapSize;
        
        /// <summary>
        /// 基础纹理ID（可选，为兼容性保留）
        /// </summary>
        public string baseTextureId;
        
        /// <summary>
        /// 基础纹理全局索引
        /// </summary>
        public int baseTextureIndex = -1;
        
        /// <summary>
        /// 基础纹理（可选）
        /// </summary>
        public Texture2D baseTexture;
        
        /// <summary>
        /// 用户选择的 ChunkSize
        /// </summary>
        public int chunkSize = 256;
        
        /// <summary>
        /// 地图目录（由文件对话框选择）
        /// </summary>
        public string directoryPath;
        
        public NewMapDialogData()
        {
            mapName = "新地图";
            mapSize = new Vector2Int(1024, 1024);
            baseTextureId = string.Empty;
            baseTextureIndex = -1;
            baseTexture = null;
            directoryPath = string.Empty;
            chunkSize = 256;
        }
        
        public NewMapDialogData(string name, Vector2Int size, int chunkSize = 256)
        {
            mapName = name;
            mapSize = size;
            baseTextureId = string.Empty;
            baseTextureIndex = -1;
            baseTexture = null;
            directoryPath = string.Empty;
            this.chunkSize = chunkSize;
        }
        
        public NewMapDialogData(string name, Vector2Int size, int textureIndex, Texture2D texture, int chunkSize = 256)
        {
            mapName = name;
            mapSize = size;
            baseTextureId = textureIndex.ToString();
            baseTextureIndex = textureIndex;
            baseTexture = texture;
            directoryPath = string.Empty;
            this.chunkSize = chunkSize;
        }
    }
}