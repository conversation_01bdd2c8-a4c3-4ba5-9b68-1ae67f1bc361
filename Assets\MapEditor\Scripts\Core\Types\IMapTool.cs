using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Core
{
    /// <summary>
    /// 输入上下文，包含当前输入状态
    /// </summary>
    public class InputContext
    {
        /// <summary>
        /// 鼠标在世界空间的位置
        /// </summary>
        public Vector2 WorldPosition { get; set; }
        
        /// <summary>
        /// 鼠标在屏幕空间的位置
        /// </summary>
        public Vector2 ScreenPosition { get; set; }
        
        /// <summary>
        /// 是否按下鼠标左键
        /// </summary>
        public bool IsLeftMouseDown { get; set; }
        
        /// <summary>
        /// 是否按下鼠标右键
        /// </summary>
        public bool IsRightMouseDown { get; set; }
        
        /// <summary>
        /// 是否按下鼠标中键
        /// </summary>
        public bool IsMiddleMouseDown { get; set; }
        
        /// <summary>
        /// 是否按下Shift键
        /// </summary>
        public bool IsShiftDown { get; set; }
        
        /// <summary>
        /// 是否按下Ctrl键
        /// </summary>
        public bool IsCtrlDown { get; set; }
        
        /// <summary>
        /// 是否按下Alt键
        /// </summary>
        public bool IsAltDown { get; set; }
        
        /// <summary>
        /// 鼠标滚轮增量
        /// </summary>
        public float ScrollDelta { get; set; }
        
        /// <summary>
        /// 是否按下Esc键
        /// </summary>
        public bool IsEscapeDown { get; set; }
        
        /// <summary>
        /// 是否按下Delete键
        /// </summary>
        public bool IsDeleteDown { get; set; }
        
        /// <summary>
        /// 查询指定键是否在本帧按下（目前仅支持 Escape 与 Delete）
        /// </summary>
        /// <param name="keyCode">要查询的 KeyCode</param>
        /// <returns>若在此帧按下返回 true</returns>
        public bool IsKeyDown(KeyCode keyCode)
        {
            switch (keyCode)
            {
                case KeyCode.Escape:
                    return IsEscapeDown;
                case KeyCode.Delete:
                    return IsDeleteDown;
                default:
                    return false;
            }
        }
    }
    
    /// <summary>
    /// 地图工具接口，所有编辑工具需实现此接口
    /// </summary>
    public interface IMapTool: IUpdate
    {
        /// <summary>
        /// 工具唯一标识符
        /// </summary>
        string ToolId { get; }
        
        /// <summary>
        /// 工具显示名称
        /// </summary>
        string DisplayName { get; }
        
        /// <summary>
        /// 获取此工具支持的图层类型列表
        /// </summary>
        LayerType[] SupportedLayerTypes { get; }
        
        /// <summary>
        /// 检查工具是否支持指定的图层类型
        /// </summary>
        /// <param name="layerType">图层类型</param>
        /// <returns>如果支持返回true，否则返回false</returns>
        bool SupportsLayerType(LayerType layerType);
        
        /// <summary>
        /// 工具激活时调用
        /// </summary>
        void OnActivate();
        
        /// <summary>
        /// 工具停用时调用
        /// </summary>
        void OnDeactivate();
        
        /// <summary>
        /// 处理场景输入
        /// </summary>
        /// <param name="context">输入上下文</param>
        void OnSceneInput(InputContext context);
        
        /// <summary>
        /// 更新工具预览
        /// </summary>
        void UpdatePreview();
        
    }
    
} 