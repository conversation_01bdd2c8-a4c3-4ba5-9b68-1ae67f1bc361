本项目是一个完全在游戏运行时(Runtime)工作的地图编辑器,而非Unity Editor扩展。

## 核心目标
• 游戏内编辑:在游戏运行时(Runtime)提供完整的 2D 地图编辑功能,玩家可直接使用
• URP 2D 渲染:所有渲染功能基于 Universal Render Pipeline 2D Renderer
• 高性能:支持大型地图(16384*16384)编辑。
• 场景渲染:地图内容、网格、画笔预览等均在场景中渲染,UI仅用于控制面板。
## 功能范围
• 场景内编辑:所有编辑操作直接作用于场景中的游戏对象
• 地表绘制:基于URP 2D的多纹理混合系统,支持实时画笔预览
• 对象操作:在场景中直接操作Prefab实例
• 网格系统:在场景中渲染的对齐网格。
• Runtime UI:使用UI Toolkit构建的游戏内控制面板和工具栏
• 数据持久化:支持在运行时保存/加载地图数据
## 非目标
• Editor功能:不是Unity Editor扩展,不使用Editor API
• 3D功能:不支持3D地形或高度图
• Editor UI:不使用EditorGUILayout或自定义Editor窗口
## 技术栈与规范
Unity版本: 2022.3.34f1 LTS
渲染管线: Universal Render Pipeline 14.0.11 + 2D Renderer (必须)
性能优化:
  - Burst: 1.8.15 (用于数据处理)
  - Mathematics: 1.2.6
  - Collections: 1.2.4
  - Jobs: 用于纹理处理和批量操作
着色器: HLSL (URP 2D)
目标平台: Windows
## 其他
- 代码请使用中文注释。
- 在新建Class、，interface、enum等内容前，需要调查是否项目中已有类似可复用内容
- 编码前，请先对现有代码进行调查，再编码。
- 在任何破坏性修改时，都需要谨慎，除非用户明确改变原有功能，否则编码时，必须考虑维持原有功能的完整性和逻辑正确性。