using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using MapEditor.Rendering.Layers;
using MapEditor.Event;

namespace MapEditor.Services
{
    /// <summary>
    /// 对象服务，提供对象管理相关功能
    /// </summary>
    public class ObjectService:ServiceBase
    {

        private  MapService mapService;

        public override void Initialize()
        {

        }
        
        public override void Start()
        {
            this.mapService = GetService<MapService>();
        }
        
        /// <summary>
        /// 获取当前活动对象层的所有对象
        /// </summary>
        public List<ObjectInstance> GetAllObjectsInCurrentLayer()
        {
            var objectRenderer = GetActiveObjectRenderer();
            return objectRenderer?.GetAllObjects() ?? new List<ObjectInstance>();
        }
        
        /// <summary>
        /// 获取指定层的所有对象
        /// </summary>
        public List<ObjectInstance> GetObjectsInLayer(string layerId)
        {
            var layer = core.LayerStore?.GetLayerById(layerId);
            if (layer == null || (layer.Type != LayerType.Object && layer.Type != LayerType.Decoration))
                return new List<ObjectInstance>();
                
            var layerRenderService = GetService<LayerRenderService>();
            var renderer = layerRenderService?.GetRenderer(layer) as ObjectLayerRenderer;
            return renderer?.GetAllObjects() ?? new List<ObjectInstance>();
        }
        
        /// <summary>
        /// 重命名对象
        /// </summary>
        public void RenameObject(ObjectInstance obj, string newName)
        {
            if (obj == null) return;
            
            obj.DisplayName = newName;
            
            // 标记所在的chunk为脏
            var objectRenderer = GetActiveObjectRenderer();
            objectRenderer?.MarkObjectChunkDirty(obj);
            
            // 发布事件通知UI更新
            PublishEvent(new ObjectRenamedEvent 
            { 
                Instance = obj,
                NewName = newName
            });
        }
        
        /// <summary>
        /// 删除对象
        /// </summary>
        public bool DeleteObject(ObjectInstance obj)
        {
            if (obj == null) return false;
            
            var objectRenderer = GetActiveObjectRenderer();
            if (objectRenderer != null)
            {
                bool result = objectRenderer.RemoveObject(obj);
                
                if (result)
                {
                    // 发布事件
                    PublishEvent(new MapDataChangedEvent
                    {
                        ChangeType = MapDataChangeType.ObjectRemoved,
                        LayerId = mapService?.GetActiveLayer()?.LayerId ?? "",
                        AffectedArea = new Rect(obj.Position, Vector2.one)
                    });
                }
                
                return result;
            }
            
            return false;
        }
        
        /// <summary>
        /// 批量删除对象
        /// </summary>
        public void DeleteObjects(List<ObjectInstance> objects)
        {
            if (objects == null || objects.Count == 0) return;
            
            foreach (var obj in objects)
            {
                DeleteObject(obj);
            }
        }
        
        /// <summary>
        /// 获取当前活动的对象层渲染器
        /// </summary>
        private ObjectLayerRenderer GetActiveObjectRenderer()
        {
            var activeLayer = mapService?.GetActiveLayer();
            if (activeLayer?.Type == LayerType.Object || activeLayer?.Type == LayerType.Decoration)
            {
                var layerRenderService = GetService<LayerRenderService>();
                var renderer = layerRenderService?.GetRenderer(activeLayer);
                return renderer as ObjectLayerRenderer;
            }
            return null;
        }
        
        /// <summary>
        /// 移动相机到对象位置
        /// </summary>
        public void FocusOnObject(ObjectInstance obj)
        {
            if (obj == null) return;
            
            var mainCamera = Camera.main;
            if (mainCamera != null)
            {
                var mapViewCamera = mainCamera.GetComponent<Manager.MapViewCamera>();
                if (mapViewCamera != null)
                {
                    mapViewCamera.SetPosition(obj.Position);
                    Debug.Log($"相机移动到对象 {obj.GetDisplayName()} 的位置: {obj.Position}");
                }
            }
        }
    }
    
    /// <summary>
    /// 对象重命名事件
    /// </summary>
    public class ObjectRenamedEvent
    {
        public ObjectInstance Instance { get; set; }
        public string NewName { get; set; }
    }
} 