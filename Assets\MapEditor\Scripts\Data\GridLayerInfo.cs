using UnityEngine;
using MapEditor.Core;
using System.Collections.Generic;

namespace MapEditor.Data
{
    /// <summary>
    /// 网格层信息，作为系统内置图层在LayerPanel中显示
    /// </summary>
    public class GridLayerInfo : IMapLayer
    {
        private readonly ISceneRenderer sceneRenderer;
        private bool isVisible = true;
        
        public GridLayerInfo(ISceneRenderer sceneRenderer)
        {
            this.sceneRenderer = sceneRenderer;
        }
        
        public string LayerId 
        { 
            get => "__grid__"; 
            set { /* Grid layer ID is fixed */ } 
        }
        
        public string LayerName { get; set; } = "网格";
        public LayerType Type => LayerType.Grid;
        
        public bool IsVisible 
        { 
            get => isVisible;
            set 
            { 
                isVisible = value;
                sceneRenderer?.SetLayerVisibility(RenderLayer.Grid, value);
            } 
        }
        
        public bool IsLocked 
        { 
            get => false; 
            set { /* Grid layer cannot be locked */ } 
        }
        
        public int Order 
        { 
            get => MapEditor.Config.SortingOrderConfig.GridFixedOrder; 
            set { /* Grid layer order is fixed */ } 
        }
        
        public int ChunkSize => 256; // Grid layer uses a default chunk size
        
        public Vector2Int LayerSize 
        { 
            get => Vector2Int.zero; 
            set { /* Grid layer size is managed by grid service */ } 
        }
        
        public IEnumerable<ChunkBase> GetVisibleChunks(Bounds viewBounds)
        {
            // Grid layer doesn't have chunks, return empty collection
            return new ChunkBase[0];
        }
    }
} 