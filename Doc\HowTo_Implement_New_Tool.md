# 如何实现一个新的图层工具

本文档将指导您如何在 MapEditor项目中创建一个新的图层编辑工具。整个流程遵循项目现有的架构规范，确保新工具能够无缝集成。

## 1. 核心概念

- **`IMapTool` 接口**: 所有编辑工具都必须实现的接口。它定义了工具的生命周期（激活/停用）、输入处理和属性（ID、名称、支持的图层类型）。
- **`MapToolBase` 基类**: 一个实现了 `IMapTool` 接口部分通用功能的抽象基类，建议所有新工具都继承自它，以简化开发。
- **`ToolService`**: 负责在编辑器启动时注册所有默认工具。新的核心工具需要在此服务中进行注册。
- **`ToolManager`**: 负责管理所有已注册工具的生命周期，包括激活和停用工具。
- **`InputContext`**: 一个数据结构，封装了当前帧的输入状态（如鼠标位置、按键状态等），传递给工具的输入处理方法。
- **UI 面板**: 如果工具需要自定义的设置界面，需要创建一个对应的 `UIPanel`，并将其与工具的生命周期关联起来。

## 2. 开发流程

以下是创建一个新的“高度笔刷工具” (`HeightBrushTool`) 的完整流程，您可以此为模板。

### 第 1 步：创建工具类

在 `Assets/MapEditor/Scripts/Tools/` 目录下创建一个新的 C# 脚本。让这个类继承自 `MapToolBase`。

```csharp
// In Assets/MapEditor/Scripts/Tools/HeightBrushTool.cs
using MapEditor.Core;
using UnityEngine;

namespace MapEditor.Tools
{
    public class HeightBrushTool : MapToolBase
    {
        // 构造函数：定义工具的 ID、显示名称和支持的图层类型
        public HeightBrushTool(IMapEditorCore editorCore)
            : base("HeightBrushTool", "高度笔刷", editorCore, new[] { LayerType.Height })
        {
            // 初始化工具所需的其他服务或资源
        }

        // 工具被激活时调用
        public override void OnActivate()
        {
            base.OnActivate();
            // 创建或显示工具的预览渲染器、UI 面板等
        }

        // 工具被停用时调用
        public override void OnDeactivate()
        {
            base.OnDeactivate();
            // 隐藏或销毁预览渲染器、UI 面板等
        }

        // 每帧处理输入
        public override void OnSceneInput(InputContext context)
        {
            // 在这里实现工具的核心逻辑，例如：
            // 1. 根据 context.WorldPosition 更新预览
            // 2. 响应 context.IsLeftMouseDown 开始绘制
            // 3. 在绘制时，调用相关服务修改图层数据
        }

        // 更新工具的预览（例如，笔刷范围指示器）
        public override void UpdatePreview()
        {
            // 更新预览渲染器的状态
        }
    }
}
```

**关键点**:

-   **构造函数**: 调用基类构造函数，传入唯一的 `ToolId`、用户可见的 `DisplayName` 以及一个 `LayerType` 数组，指明该工具能在哪些类型的图层上使用。
-   **`OnActivate`/`OnDeactivate`**: 管理工具所需的资源，如预览用的 `GameObject` 或 UI 面板的可见性。
-   **`OnSceneInput`**: 这是工具的核心。`InputHandler` 服务会每帧调用此方法，传入当前的输入状态。您需要在这里实现工具与场景的交互逻辑。

### 第 2 步：实现工具逻辑

在 `OnSceneInput` 方法中，您需要将用户的输入（如鼠标点击和移动）转化为对地图数据的修改。**重要提示**：工具本身不应直接修改数据。正确的做法是调用相应的服务（如 `MapService` 或特定图层的 `Renderer` 提供的接口）来执行操作。

以 `HeightBrushTool` 为例，它会调用 `HeightLayerRenderer` 提供的 `DrawBrushGPU` 方法来修改高度图数据。

```csharp
// In HeightBrushTool.cs
private void DrawGPU(Vector2 pos)
{
    // 1. 获取当前活动图层的渲染器
    var renderer = _mapService.GetActiveLayerRenderer() as MapEditor.Rendering.Layers.HeightLayerRenderer;
    if (renderer == null) return;

    // 2. 调用渲染器提供的接口来执行绘制操作
    renderer.DrawBrushGPU(pos, currentSettings);
}
```

### 第 3 步：注册工具

为了让编辑器知道您的新工具，需要在 `ToolService` 中注册它。打开 `Assets/MapEditor/Scripts/Services/ToolService.cs`。

在 `RegisterDefaultTools` 方法中，添加以下代码：

```csharp
// In ToolService.cs, inside RegisterDefaultTools()

// ... 其他工具注册代码 ...

// 注册高度笔刷工具
var heightBrushTool = new HeightBrushTool(core);
toolManager.RegisterTool(heightBrushTool);
```

完成这一步后，当编辑器启动时，您的工具就会被 `ToolManager` 管理，并可以被激活。

### 第 4 步：（可选）创建设置面板

如果您的工具需要一个设置面板（如调整笔刷大小、强度等），您需要创建一个新的 `UIPanel`。

1.  **创建 UXML 文件**: 在 `Assets/MapEditor/Resources/UI/` 目录下创建一个新的 UI Toolkit UXML 文件，用于定义面板的布局。
2.  **创建面板类**: 在 `Assets/MapEditor/Scripts/UI/` 目录下创建一个新的 C# 脚本，继承自 `UIPanel`。

    ```csharp
    // In Assets/MapEditor/Scripts/UI/HeightBrushSettingsPanel.cs
    public class HeightBrushSettingsPanel : UIPanel
    {
        private HeightBrushTool heightBrushTool;

        public override void Initialize()
        {
            // 获取工具实例
            var toolService = MapEditorCore.Instance.GetService<ToolService>();
            heightBrushTool = toolService?.GetTool("HeightBrushTool") as HeightBrushTool;

            // 查找 UI 控件并注册回调
            var sizeSlider = FindElement<Slider>("SizeSlider");
            sizeSlider.RegisterValueChangedCallback(evt => {
                heightBrushTool.SetBrushSize(evt.newValue);
            });

            // 监听工具切换事件，以自动显示/隐藏面板
            MapEditorCore.Instance.EventSystem?.Subscribe<ToolChangedEvent>(OnToolChanged);
        }

        private void OnToolChanged(ToolChangedEvent e)
        {
            IsVisible = e.NewToolId == "HeightBrushTool";
        }
    }
    ```
3.  **在 `UIManager` 中注册面板**: 在 `UIManager.InitializeUI` 中加载 UXML 模板并注册您的新面板。

## 5. 总结

创建一个新工具的流程可以归纳为：

1.  **创建** 一个继承自 `MapToolBase` 的工具类。
2.  **实现** `OnSceneInput` 方法来定义工具的核心行为，通过调用服务来修改数据。
3.  **注册** 新工具到 `ToolService` 中。
4.  （可选）**创建** 并关联一个 `UIPanel` 来提供工具设置。
