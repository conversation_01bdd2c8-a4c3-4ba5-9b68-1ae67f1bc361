# 《MapEditor - Unity Editor Extension》设计文档

**版本**: 2.0
**更新日期**: 2025-07-01

**文档目的**: 本文档为 `MapEditor` 项目提供准确的技术描述、架构设计和功能纲要。它旨在帮助新成员快速理解项目，并作为未来开发的指导。

---

## 1. 项目定位与核心理念

### 1.1 产品定位

**MapEditor** 是一个专为 Unity 开发的 **2D 地图编辑器扩展 (Editor Extension)**。它深度集成于 Unity 编辑器中，提供一套完整、高效的工作流，用于创建包含丰富纹理混合和大量对象的 2D 游戏地图。

与运行时编辑器不同，本项目的所有核心功能均在 **Unity Editor 环境**下工作，旨在最大化利用编辑器的强大功能，为地图设计师提供最佳的创作体验。

### 1.2 核心设计理念

- **工作流优先**: 通过批量处理工具和直观的编辑器 UI，简化资源导入和地图创作流程。
- **数据驱动**: 广泛使用 `ScriptableObject` 定义画笔、纹理、对象等资源，实现配置与逻辑的分离，易于扩展和管理。
- **性能与规模**: 采用地图分块 (Chunking) 和优化的渲染技���，支持创建和管理大规模 2D 场景。
- **模块化与扩展性**: 基于服务化和事件驱动的松耦合架构，使各个子系统（如地表、对象、UI）易于独立维护和功能扩展。

---

## 2. 系统架构

### 2.1 整体架构

项目采用清晰的分层和服务化架构。

```
┌─────────────────────────────────────────────────────────┐
│                  Editor UI Layer (IMGUI/UI Toolkit)      │
│ (EditorWindow, Custom Inspectors, Scene GUI for Tools)  │
├─────────────────────────────────────────────────────────┤
│                     Services Layer                       │
│ (ToolManager, MapService, GridService, SaveService etc.)│
├─────────────────────────────────────────────────────────┤
│                       Core Layer                         │
│ (EventBus, MapData, Chunk System, Layer System, Tools)  │
├───────────────────────────────────────���─────────────────┤
│                      Data/Asset Layer                    │
│ (ScriptableObjects, Prefabs, Textures, Materials)       │
└─────────────────────────────────────────────────────────┘
```

### 2.2 核心模块职责

| 模块 (部分示例) | 职责 | 关键实现 |
|---|---|---|
| **Editor Tools** | 提供批量创建资源的编辑器窗口。 | `BrushShapeBatchCreator`, `ObjectPrefabBatchCreator` |
| **Services** | 管理核心逻辑，如工具切换、数据操作、存盘等。 | `ToolManager.cs`, `MapService.cs`, `SaveService.cs` |
| **EventBus** | 负责系统内各模块的解耦通信。 | `EventBus.cs`, `*Events.cs` |
| **Map/Chunk System** | 管理地图数据结构，实现分块加载和管理。 | `MapData.cs`, `ChunkBase.cs`, `ObjectChunk.cs` |
| **Rendering** | 处理地表、网格和对象的渲染逻辑。 | `SplatMix2x4Lit.shader`, `ShaderGridRenderer.cs` |
| **Data Assets** | 定义编辑器所使用的资源（画笔、纹理等）。 | `BrushShapeSO.cs`, `GroundTextureSO.cs` |
| **Tools** | 实现具体的编辑工具逻辑（画笔、选择等）。 | `BrushTool.cs`, `SelectionTool.cs` |

---

## 3. 核心功能设计

### 3.1 地表纹理绘制系统 (Ground Texture Painting)

这是编辑器的核心功能之一，允许设计师像在 Photoshop 中一样在 2D 平面上绘制丰富的地表纹理。

- **技术实现**:
    1.  **Splat Map**: 使用两张 RGBA 格式的控制贴图 (`_Splat0`, `_Splat1`)，在单个 2D 平面 (Quad Mesh) 上最多可控制 **7** 种不同地表纹理的混合权重（第 8 个通道 `w1.a` 用于控制整体覆盖度）。这种技术避免了传统 Tilemap 的网格限制，可以实现像素级的平滑过渡和混合。
    2.  **`SplatMix2x4Lit.shader`**: 核心的地表渲染着色器。它对 7 张地表纹理进行采样，并根据 Splat Map 的权重值进行混合，最终输出地表颜色。
    3.  **`SplatPaint.shader`**: 笔刷绘制着色器。当用户使用地表笔刷时，该 Shader 会被用于动态修改 Splat Map 的像素值，从而改变地表纹理的混合。
- **工作流程**:
    1.  用户在 **Ground Texture 面板**中选择一个地表纹理。
    2.  用户在 **Brush 面板**中选择笔刷形状、大小和强度。
    3.  在 Scene 视图中，按住鼠标在 2D 地图平面上拖动。
    4.  `BrushTool` 捕捉输入，并调用渲染服务，使用 `SplatPaint.shader` 将笔刷操作“烘焙”到对应区块 (Chunk) 的 Splat Map 上。
    5.  地表平面的 `MeshRenderer` 使用的材��（搭载 `SplatMix2x4Lit.shader`）会立即读取更新后的 Splat Map，实时显示绘制效果。

### 3.2 对象放置系统 (Object Placement)

- **功能**: 支持在 2D 地图上精确放置、选择、移动、旋转和缩放游戏对象 (Prefabs)。
- **资源工作流**:
    - `ObjectPrefabBatchCreator`: 提供一个编辑器窗口，允许设计师将一批 Texture2D 文件拖入，自动生成带有 `SpriteRenderer` 和可选 `BoxCollider2D` 的 Prefab。这极大地加速了 2D 对象的资源准备。
- **工具**:
    - **Object Paint Tool**: 允许像笔刷一样在地图上快速“绘制”对象，支持随机旋转、缩放等设置。
    - **Selection Tool**: 标准的选择和变换工具，集成在 Scene 视图中，提供类似 Unity 原生操作的体验。

### 3.3 笔刷系统 (Brush System)

- **数据定义**:
    - **`BrushShapeSO.cs`**: 使用 `ScriptableObject` 定义笔刷的形状。每个 SO 引用一个灰度图作为 Alpha 蒙版。
    - **`BrushShapeBatchCreator`**: 用于从一批灰度纹理批量生成 `BrushShapeSO` 资产。
- **功能**:
    - 支持自定义笔刷形状。
    - 支持调整笔刷大小、强度/不透明度。
    - 实时在 Scene 视图中显示笔刷预览。

### 3.4 数据管理与持久化

- **地图分块 (Chunking)**:
    - 为了支持大规模地图，���个地图在逻辑上被划分为固定大小的区块 (Chunk)。
    - 每个区块独立管理其范围内的地表 Splat Map、对象列表等数据。
    - 系统未来可以实现动态加载和卸载区块，以优化内存和性能。
- **数据存储**:
    - 地图的元数据、图层信息和区块数据被序列化后保存为自定义格式的文件（可能是 JSON 或二进制）。
    - `SaveService.cs` 负责处理地图的保存和加载逻辑。
    - `SimpleFileBrowser` 插件用于提供“打开”和“保存”的系统文件对话框。

---

## 4. 编辑器 UI (Editor UI)

MapEditor 的用户界面完全构建在 Unity Editor 内部，通过自定义窗口、面板和 Scene 视图 Gizmos 实现。

- **主工具栏 (Toolbar)**: 可能是一个独立的 `EditorWindow`，提供不同编辑模式（地表绘制、对象放置、网格设置等）的切换入口。
- **属性/设置面板 (Inspector Panels)**:
    - **Brush Settings**: 当选择地表或对象笔刷时，会显示一个面板用于调整笔刷的各种属性。
    - **Layer Panel**: 管理地图图层（如地表层、对象层），控制其可见性、锁定状态等。
    - **Inspector Panel**: 当选中一个或多个地图对象时，显示其详细属性，并允许修改。
- **Scene 视图集成**:
    - **笔刷预览**: 鼠标悬停在可编辑表面上时，实时绘制笔刷的范围和形状。
    - **自定义 Gizmos**: `ObjectColliderAdapterEditor` 脚本为自定义的多边形碰撞体在 Scene 视图中提供了可交互的顶点编辑手柄。

---

## 5. 扩展性设计

- **自定义工具**: `IMapTool` 接口和 `ToolManager` 的设计允许开发者通过实现该接口来添加新的编辑工具。
- **自定义图层**: `MapLayer` 基类和 `LayerType` 枚举的设计，为添加新型图层（如水体层、道路层）提供了基础。
- **自定义资源**: 通过创建新的 `ScriptableObject` 类型和对应的批量创建工具，可以轻松扩展编辑器支持的资源类型。