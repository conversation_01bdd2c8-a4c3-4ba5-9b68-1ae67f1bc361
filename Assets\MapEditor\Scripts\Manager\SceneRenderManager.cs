using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Rendering;
using MapEditor.Rendering.Layers;
using MapEditor.Event;
using MapEditor.Config;

namespace MapEditor.Manager
{
    /// <summary>
    /// 统一的场景渲染管理器，合并了原 SceneRenderer 和 RenderLayerManager 的功能
    /// </summary>
    public class SceneRenderManager : MonoBehaviour, ISceneRenderer, IRenderLayerManager
    {
        #region SceneRenderer 原有字段
        
        [Header("相机设置")]
        [SerializeField] private Camera renderCamera;
        
        [Header("渲染设置")]
        [SerializeField] private bool[] layerVisibility = new bool[5] { true, true, true, true, true };
        
        // 渲染对象集合（按层级和优先级排序）
        private readonly Dictionary<RenderLayer, List<IRenderable>> renderables = new();
        
        #endregion

        #region RenderLayerManager 原有字段
        
        [Header("地表层设置")]
        [SerializeField] private Transform groundLayerContainer;
        
        [Header("对象层设置")]
        [SerializeField] private Transform objectLayerContainer;
    
        
        [Header("预览层设置")]
        [SerializeField] private Transform previewLayerContainer;
      
        
        // 存储不同类型渲染器的字典
        private Dictionary<RenderLayer, List<LayerRenderer>> layerRenderers = new();
        
        #endregion

        #region 共享字段
        
        // 事件系统引用
        private IEventSystem eventSystem;
        
        #endregion

        #region 公共属性
        
        /// <summary>
        /// 获取渲染相机
        /// </summary>
        public Camera RenderCamera => renderCamera;
        
        #endregion

        #region 初始化和生命周期

        /// <summary>
        /// 初始化渲染管理器
        /// </summary>
        public void Initialize(IEventSystem eventSystem)
        {
            this.eventSystem = eventSystem;
            
            // 注册渲染更新事件处理
            eventSystem?.Subscribe<RenderUpdateEvent>(OnRenderUpdate);
            // 订阅图层渲染请求事件
            eventSystem?.Subscribe<LayerRenderRequestEvent>(OnLayerRenderRequest);
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void Awake()
        {
            // SceneRenderer 原有的初始化逻辑
            InitializeRenderables();
            InitializeCamera();
            
            // RenderLayerManager 原有的初始化逻辑
            InitializeLayerContainers();
            InitializeLayerRenderers();
        }

        /// <summary>
        /// 初始化渲染对象集合
        /// </summary>
        private void InitializeRenderables()
        {
            foreach (RenderLayer layer in System.Enum.GetValues(typeof(RenderLayer)))
            {
                renderables[layer] = new List<IRenderable>();
            }
        }

        /// <summary>
        /// 初始化相机
        /// </summary>
        private void InitializeCamera()
        {
            // 获取场景中的主相机
            if (renderCamera == null)
            {
                renderCamera = Camera.main;
                if (renderCamera == null)
                {
                    renderCamera = GameObject.FindObjectOfType<Camera>();
                }

                if (renderCamera == null)
                {
                    Debug.LogError("No camera found for SceneRenderManager. Please ensure there is a camera in the scene.");
                    return;
                }
            }
            
            // 确保相机上挂载有 MapViewCamera 控制脚本
            if (renderCamera != null && renderCamera.GetComponent<MapViewCamera>() == null)
            {
                renderCamera.gameObject.AddComponent<MapViewCamera>();
                Debug.Log("Added MapViewCamera component to the main camera");
            }
        }

        /// <summary>
        /// 初始化层级容器
        /// </summary>
        private void InitializeLayerContainers()
        {
            // 创建层级容器
            if (groundLayerContainer == null)
            {
                GameObject groundContainer = new("GroundLayer");
                groundContainer.transform.SetParent(transform);
                groundLayerContainer = groundContainer.transform;
            }
            
            if (objectLayerContainer == null)
            {
                GameObject objectContainer = new("ObjectLayer");
                objectContainer.transform.SetParent(transform);
                objectLayerContainer = objectContainer.transform;
            }
            
            if (previewLayerContainer == null)
            {
                GameObject previewContainer = new("PreviewLayer");
                previewContainer.transform.SetParent(transform);
                previewLayerContainer = previewContainer.transform;
            }
        }

        /// <summary>
        /// 初始化渲染器集合
        /// </summary>
        private void InitializeLayerRenderers()
        {
            foreach (RenderLayer layer in System.Enum.GetValues(typeof(RenderLayer)))
            {
                layerRenderers[layer] = new List<LayerRenderer>();
            }
        }

        /// <summary>
        /// 组件销毁时的处理
        /// </summary>
        private void OnDestroy()
        {
            // 取消事件订阅
            eventSystem?.Unsubscribe<RenderUpdateEvent>(OnRenderUpdate);
            eventSystem?.Unsubscribe<LayerRenderRequestEvent>(OnLayerRenderRequest);
        }

        /// <summary>
        /// 停用组件时的处理
        /// </summary>
        private void OnDisable()
        {
            // 注销事件
            if (eventSystem != null)
            {
                eventSystem.Unsubscribe<RenderUpdateEvent>(OnRenderUpdate);
            }
        }

        #endregion

        #region ISceneRenderer 接口实现

        /// <summary>
        /// 注册一个可渲染对象
        /// </summary>
        public void RegisterRenderable(IRenderable renderable)
        {
            if (renderable == null)
            {
                Debug.LogError("Cannot register null renderable");
                return;
            }
            
            if (!renderables.ContainsKey(renderable.Layer))
            {
                renderables[renderable.Layer] = new List<IRenderable>();
            }
            
            if (!renderables[renderable.Layer].Contains(renderable))
            {
                renderables[renderable.Layer].Add(renderable);
                
                // 按渲染优先级排序
                renderables[renderable.Layer].Sort((a, b) => a.RenderOrder.CompareTo(b.RenderOrder));
                
                // 应用层级可见性
                renderable.IsVisible = layerVisibility[(int)renderable.Layer];
                
                Debug.Log($"Registered renderable to layer {renderable.Layer}");
            }
        }

        /// <summary>
        /// 注销一个可渲染对象
        /// </summary>
        public void UnregisterRenderable(IRenderable renderable)
        {
            if (renderable == null)
            {
                return;
            }
            
            if (renderables.TryGetValue(renderable.Layer, out var layerRenderables))
            {
                if (layerRenderables.Remove(renderable))
                {
                    Debug.Log($"Unregistered renderable from layer {renderable.Layer}");
                }
            }
        }

        /// <summary>
        /// 设置渲染层级的可见性
        /// </summary>
        public void SetLayerVisibility(RenderLayer layer, bool isVisible)
        {
            // 更新层级可见性状态
            layerVisibility[(int)layer] = isVisible;
            
            // 应用到所有该层级的渲染对象
            if (renderables.TryGetValue(layer, out var layerRenderables))
            {
                foreach (var renderable in layerRenderables)
                {
                    renderable.IsVisible = isVisible;
                }
            }
            
            Debug.Log($"Set layer {layer} visibility to {isVisible}");
        }

        /// <summary>
        /// 获取渲染层级的可见性
        /// </summary>
        public bool GetLayerVisibility(RenderLayer layer)
        {
            return layerVisibility[(int)layer];
        }

        /// <summary>
        /// 强制更新所有渲染对象
        /// </summary>
        public void RefreshRender()
        {
            foreach (var layer in renderables.Keys)
            {
                foreach (var renderable in renderables[layer])
                {
                    renderable.UpdateRender();
                }
            }
        }

        /// <summary>
        /// 创建网格渲染器
        /// </summary>
        public IGridRenderer CreateGridRenderer(GridType gridType = GridType.Square)
        {
            GameObject gridObj = new($"{gridType}GridRenderer");
            gridObj.transform.SetParent(transform);
            
            IGridRenderer gridRenderer;
            
            switch (gridType)
            {
                case GridType.Diamond:
                    // 未实现的菱形网格，暂时使用方形网格代替
                    Debug.LogWarning("Diamond grid not implemented yet, using square grid");
                    gridRenderer = gridObj.AddComponent<ShaderGridRenderer>();
                    break;
                
                case GridType.Square:
                default:
                    gridRenderer = gridObj.AddComponent<ShaderGridRenderer>();
                    break;
            }
            
            return gridRenderer;
        }

        #endregion

        #region IRenderLayerManager 接口实现

        /// <summary>
        /// 发布渲染更新事件
        /// </summary>
        public void PublishRenderUpdate(RenderLayer layer, Rect updateArea)
        {
            eventSystem?.Publish(new RenderUpdateEvent
            {
                Layer = layer,
                UpdateArea = updateArea
            });
        }

        #endregion

        #region RenderLayerManager 原有功能

        /// <summary>
        /// 获取指定层级的容器
        /// </summary>
        public Transform GetLayerContainer(RenderLayer layer)
        {
            switch (layer)
            {
                case RenderLayer.Ground:
                    return groundLayerContainer;
                case RenderLayer.Object:
                    return objectLayerContainer;
                case RenderLayer.Preview:
                    return previewLayerContainer;
                default:
                    return transform;
            }
        }

        /// <summary>
        /// 获取指定层级的排序顺序
        /// </summary>
        public int GetLayerSortingOrder(RenderLayer layer, int sublayerOffset = 0)
        {
            return layer switch
            {
                RenderLayer.Ground  => SortingOrderConfig.GroundRange.min  + sublayerOffset,
                RenderLayer.Grid    => SortingOrderConfig.GridFixedOrder   + sublayerOffset,
                RenderLayer.Object  => SortingOrderConfig.ObjectRange.min  + sublayerOffset,
                RenderLayer.Preview => SortingOrderConfig.PreviewRange.min + sublayerOffset,
                RenderLayer.UI      => SortingOrderConfig.UIRange.min      + sublayerOffset,
                _                   => 0
            };
        }

        /// <summary>
        /// 创建预览渲染器
        /// </summary>
        public void CreatePreviewRenderer()
        {
            // 实际项目中，这里会创建预览渲染器
            Debug.Log("Preview renderer would be created here");
        }

        #endregion

        #region 事件驱动处理

        /// <summary>
        /// 处理 LayerRenderService 发出的渲染请求
        /// </summary>
        private void OnLayerRenderRequest(LayerRenderRequestEvent evt)
        {
            switch (evt.Action)
            {
                case LayerRenderAction.Create:
                    if (evt.Layer != null)
                    {
                        if (evt.Layer.Type == LayerType.Ground) CreateGroundRendererInternal(evt.Layer);
                        else if (evt.Layer.Type == LayerType.Object) CreateObjectRendererInternal(evt.Layer);

                        var renderer = GetLayerRendererInternal(evt.Layer);
                        eventSystem.Publish(new LayerRendererRegisteredEvent { Layer = evt.Layer, Renderer = renderer });
                    }
                    break;
                case LayerRenderAction.Remove:
                    if (evt.Layer != null) RemoveLayerRendererInternal(evt.Layer);
                    break;
                case LayerRenderAction.ClearAll:
                    ClearAllRenderersInternal();
                    break;
                case LayerRenderAction.UpdateVisibility:
                    if (evt.Layer != null)
                    {
                        var r = GetLayerRendererInternal(evt.Layer) as ILayerRenderer;
                        r?.SetVisible(evt.Visibility);
                    }
                    break;
                case LayerRenderAction.UpdateAll:
                    UpdateAllLayersInternal();
                    break;
            }
        }

        #endregion

        #region 内部实现方法

        /// <summary>
        /// 创建地表渲染器的内部实现
        /// </summary>
        private void CreateGroundRendererInternal(IMapLayer mapLayer)
        {
            // 确保容器已存在
            if (groundLayerContainer == null) InitializeLayerContainers();

            var go = new GameObject(mapLayer.LayerName);
            var groundRenderer = go.AddComponent<TilemapLayerRenderer>();

            groundRenderer.Initialize(mapLayer);
  
            go.transform.SetParent(groundLayerContainer);
            layerRenderers[RenderLayer.Ground].Add(groundRenderer);

            Debug.Log($"Ground renderer created for: {mapLayer.LayerName}");
        }

        /// <summary>
        /// 创建对象渲染器的内部实现
        /// </summary>
        private void CreateObjectRendererInternal(IMapLayer mapLayer)
        {
            // 确保容器已存在
            if (objectLayerContainer == null) InitializeLayerContainers();

            var go = new GameObject(mapLayer.LayerName);
            var objectRenderer = go.AddComponent<MapEditor.Rendering.Layers.ObjectLayerRenderer>();

            objectRenderer.Initialize(mapLayer);

            go.transform.SetParent(objectLayerContainer);
            layerRenderers[RenderLayer.Object].Add(objectRenderer);

            Debug.Log($"Object renderer created for: {mapLayer.LayerName}");
        }

        /// <summary>
        /// 获取图层渲染器的内部实现
        /// </summary>
        public LayerRenderer GetLayerRendererInternal(IMapLayer mapLayer)
        {
            var layer = mapLayer.Type;
            if (layer == LayerType.Ground)
            {
                var layers = layerRenderers[RenderLayer.Ground];
                return layers.Find(p => p.ID == mapLayer.LayerId);
            }
            else if (layer == LayerType.Object)
            {
                var layers = layerRenderers[RenderLayer.Object];
                return layers.Find(p => p.ID == mapLayer.LayerId);
            }
           
            return null;
        }

        /// <summary>
        /// 清理所有渲染器的内部实现
        /// </summary>
        private void ClearAllRenderersInternal()
        {
            // 遍历所有渲染层列表
            foreach (var kv in layerRenderers)
            {
                var rendererList = kv.Value;
                // 逐个销毁 GameObject
                foreach (var renderer in rendererList)
                {
                    if (renderer != null)
                    {
                        Destroy(renderer.gameObject);
                    }
                }
                // 清空列表
                rendererList.Clear();
            }

            // 同时清空各容器下的所有子物体，以防有遗漏
            void ClearChildren(Transform parent)
            {
                if (parent == null) return;
                for (int i = parent.childCount - 1; i >= 0; i--)
                {
                    Destroy(parent.GetChild(i).gameObject);
                }
            }

            ClearChildren(groundLayerContainer);
            ClearChildren(objectLayerContainer);
            ClearChildren(previewLayerContainer);

            Debug.Log("[SceneRenderManager] 所有 LayerRenderer 已清理");
        }

        /// <summary>
        /// 移除图层渲染器的内部实现
        /// </summary>
        private void RemoveLayerRendererInternal(IMapLayer mapLayer)
        {
            if (mapLayer == null) return;

            // 获取对应列表
            List<LayerRenderer> list = null;
            switch (mapLayer.Type)
            {
                case LayerType.Ground:
                    list = layerRenderers[RenderLayer.Ground];
                    break;
                case LayerType.Object:
                    list = layerRenderers[RenderLayer.Object];
                    break;
                case LayerType.Decoration:
                    list = layerRenderers[RenderLayer.Preview];
                    break;
                default:
                    list = null;
                    break;
            }

            if (list == null) return;

            var renderer = list.Find(r => r != null && r.ID == mapLayer.LayerId);
            if (renderer != null)
            {
                // 调用图层自定义清理逻辑，删除未保存的临时资源等
                renderer.OnLayerRemoved();

                list.Remove(renderer);
                if (renderer.gameObject != null)
                {
                    Destroy(renderer.gameObject);
                }
            }
        }

        /// <summary>
        /// 更新所有图层的内部实现
        /// </summary>
        private void UpdateAllLayersInternal()
        {
            // 刷新所有 LayerRenderer
            foreach (var list in layerRenderers.Values)
            {
                foreach (var lr in list)
                {
                    // 可以调用 RefreshChunks 或其他更新逻辑
                    // lr.RefreshChunks(...);
                }
            }
            
            // 刷新所有 IRenderable
            RefreshRender();
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 渲染更新事件处理
        /// </summary>
        private void OnRenderUpdate(RenderUpdateEvent evt)
        {
            if (renderables.TryGetValue(evt.Layer, out var layerRenderables))
            {
                foreach (var renderable in layerRenderables)
                {
                    renderable.UpdateRender();
                }
            }
        }

        #endregion
    }
} 