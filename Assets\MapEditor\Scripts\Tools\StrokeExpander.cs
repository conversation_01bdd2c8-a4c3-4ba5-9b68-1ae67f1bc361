using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Tools
{
    /// <summary>
    /// 描边扩展器，负责将曲线扩展为描边区域
    /// 支持各种转角连接方式和自相交处理
    /// </summary>
    public static class StrokeExpander
    {
        /// <summary>
        /// 转角连接类型
        /// </summary>
        public enum JoinType
        {
            Miter,      // 尖角连接
            Round,      // 圆角连接
            Bevel       // 斜角连接
        }
        
        /// <summary>
        /// 端点类型
        /// </summary>
        public enum CapType
        {
            Butt,       // 平端
            Round,      // 圆端
            Square      // 方端
        }
        
        /// <summary>
        /// 描边参数
        /// </summary>
        public struct StrokeParameters
        {
            public float width;
            public JoinType joinType;
            public CapType capType;
            public float miterLimit;    // 尖角限制倍数
            public int roundSteps;      // 圆角细分步数
            
            public StrokeParameters(float width, JoinType joinType = JoinType.Round, 
                CapType capType = CapType.Round, float miterLimit = 4f, int roundSteps = 8)
            {
                this.width = width;
                this.joinType = joinType;
                this.capType = capType;
                this.miterLimit = miterLimit;
                this.roundSteps = Mathf.Max(3, roundSteps);
            }
        }
        
        /// <summary>
        /// 将曲线段扩展为描边轮廓
        /// </summary>
        /// <param name="curveSegments">曲线段列表</param>
        /// <param name="parameters">描边参数</param>
        /// <returns>描边轮廓点列表</returns>
        public static List<Vector2> ExpandToStroke(IReadOnlyList<CurveSegment> curveSegments, StrokeParameters parameters)
        {
            if (curveSegments.Count < 2)
            {
                return new List<Vector2>();
            }
            
            var strokePoints = new List<Vector2>();
            float halfWidth = parameters.width * 0.5f;
            
            // 生成左侧边界
            var leftBoundary = GenerateBoundary(curveSegments, halfWidth, true, parameters);
            strokePoints.AddRange(leftBoundary);
            
            // 添加尾部端点
            AddEndCap(strokePoints, curveSegments[curveSegments.Count - 1], halfWidth, parameters.capType, false);
            
            // 生成右侧边界（逆序）
            var rightBoundary = GenerateBoundary(curveSegments, halfWidth, false, parameters);
            rightBoundary.Reverse();
            strokePoints.AddRange(rightBoundary);
            
            // 添加头部端点
            AddEndCap(strokePoints, curveSegments[0], halfWidth, parameters.capType, true);
            
            return strokePoints;
        }
        
        /// <summary>
        /// 生成描边边界
        /// </summary>
        private static List<Vector2> GenerateBoundary(IReadOnlyList<CurveSegment> curveSegments, 
            float halfWidth, bool isLeft, StrokeParameters parameters)
        {
            var boundary = new List<Vector2>();
            float side = isLeft ? 1f : -1f;
            
            for (int i = 0; i < curveSegments.Count; i++)
            {
                var segment = curveSegments[i];
                Vector2 offset = segment.normal * (halfWidth * side);
                Vector2 boundaryPoint = segment.position + offset;
                
                if (i == 0)
                {
                    // 第一个点，直接添加
                    boundary.Add(boundaryPoint);
                }
                else
                {
                    // 计算转角处理
                    var prevSegment = curveSegments[i - 1];
                    Vector2 prevOffset = prevSegment.normal * (halfWidth * side);
                    Vector2 prevBoundaryPoint = prevSegment.position + prevOffset;
                    
                    // 检查是否需要添加转角连接
                    var joinPoints = CalculateJoin(prevSegment, segment, halfWidth * side, parameters);
                    boundary.AddRange(joinPoints);
                    
                    boundary.Add(boundaryPoint);
                }
            }
            
            return boundary;
        }
        
        /// <summary>
        /// 计算转角连接点
        /// </summary>
        private static List<Vector2> CalculateJoin(CurveSegment segment1, CurveSegment segment2, 
            float offsetDistance, StrokeParameters parameters)
        {
            var joinPoints = new List<Vector2>();
            
            Vector2 offset1 = segment1.normal * offsetDistance;
            Vector2 offset2 = segment2.normal * offsetDistance;
            Vector2 point1 = segment1.position + offset1;
            Vector2 point2 = segment2.position + offset2;
            
            // 计算角度变化
            float angle = Vector2.SignedAngle(segment1.tangent, segment2.tangent);
            bool isConvex = (offsetDistance > 0 && angle > 0) || (offsetDistance < 0 && angle < 0);
            
            if (Mathf.Abs(angle) < 5f) // 接近直线，不需要特殊处理
            {
                return joinPoints;
            }
            
            switch (parameters.joinType)
            {
                case JoinType.Miter:
                    CalculateMiterJoin(joinPoints, point1, point2, segment1, segment2, 
                        offsetDistance, parameters.miterLimit);
                    break;
                    
                case JoinType.Round:
                    CalculateRoundJoin(joinPoints, segment1.position, offset1, offset2, 
                        isConvex, parameters.roundSteps);
                    break;
                    
                case JoinType.Bevel:
                    CalculateBevelJoin(joinPoints, point1, point2);
                    break;
            }
            
            return joinPoints;
        }
        
        /// <summary>
        /// 计算尖角连接
        /// </summary>
        private static void CalculateMiterJoin(List<Vector2> joinPoints, Vector2 point1, Vector2 point2,
            CurveSegment segment1, CurveSegment segment2, float offsetDistance, float miterLimit)
        {
            // 计算两条平行线的交点
            Vector2 dir1 = segment1.tangent;
            Vector2 dir2 = segment2.tangent;
            
            float denominator = Vector2.Dot(dir1, Vector2.Perpendicular(dir2));
            if (Mathf.Abs(denominator) < 0.001f)
            {
                // 平行线，使用斜角连接
                CalculateBevelJoin(joinPoints, point1, point2);
                return;
            }
            
            float t = Vector2.Dot(point2 - point1, Vector2.Perpendicular(dir2)) / denominator;
            Vector2 intersection = point1 + dir1 * t;
            
            // 检查尖角长度是否超过限制
            float miterLength = Vector2.Distance(intersection, segment1.position);
            float maxMiterLength = Mathf.Abs(offsetDistance) * miterLimit;
            
            if (miterLength <= maxMiterLength)
            {
                joinPoints.Add(intersection);
            }
            else
            {
                // 超过限制，使用斜角连接
                CalculateBevelJoin(joinPoints, point1, point2);
            }
        }
        
        /// <summary>
        /// 计算圆角连接
        /// </summary>
        private static void CalculateRoundJoin(List<Vector2> joinPoints, Vector2 center, 
            Vector2 offset1, Vector2 offset2, bool isConvex, int steps)
        {
            if (!isConvex)
            {
                // 凹角不需要添加点
                return;
            }
            
            float startAngle = Mathf.Atan2(offset1.y, offset1.x);
            float endAngle = Mathf.Atan2(offset2.y, offset2.x);
            float radius = offset1.magnitude;
            
            // 确保角度差在正确范围内
            float angleDiff = endAngle - startAngle;
            if (angleDiff > Mathf.PI) angleDiff -= 2 * Mathf.PI;
            if (angleDiff < -Mathf.PI) angleDiff += 2 * Mathf.PI;
            
            // 生成圆弧点
            for (int i = 1; i < steps; i++)
            {
                float t = i / (float)steps;
                float angle = startAngle + angleDiff * t;
                Vector2 point = center + new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * radius;
                joinPoints.Add(point);
            }
        }
        
        /// <summary>
        /// 计算斜角连接
        /// </summary>
        private static void CalculateBevelJoin(List<Vector2> joinPoints, Vector2 point1, Vector2 point2)
        {
            // 斜角连接直接连接两个端点，不需要添加额外点
            // 这里可以添加一个中间点来形成斜角，如果需要的话
        }
        
        /// <summary>
        /// 添加端点形状
        /// </summary>
        private static void AddEndCap(List<Vector2> strokePoints, CurveSegment endSegment, 
            float halfWidth, CapType capType, bool isStart)
        {
            Vector2 center = endSegment.position;
            Vector2 direction = isStart ? -endSegment.tangent : endSegment.tangent;
            Vector2 normal = new Vector2(-direction.y, direction.x);
            
            switch (capType)
            {
                case CapType.Butt:
                    // 平端不需要添加额外点
                    break;
                    
                case CapType.Square:
                    // 方端：在端点处添加矩形
                    Vector2 squareOffset = direction * halfWidth;
                    strokePoints.Add(center + normal * halfWidth + squareOffset);
                    strokePoints.Add(center - normal * halfWidth + squareOffset);
                    break;
                    
                case CapType.Round:
                    // 圆端：添加半圆
                    int steps = 8;
                    float startAngle = Mathf.Atan2(normal.y, normal.x);
                    float endAngle = startAngle + Mathf.PI;
                    
                    for (int i = 1; i < steps; i++)
                    {
                        float t = i / (float)steps;
                        float angle = Mathf.Lerp(startAngle, endAngle, t);
                        Vector2 point = center + new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * halfWidth;
                        strokePoints.Add(point);
                    }
                    break;
            }
        }
        
        /// <summary>
        /// 检测并处理自相交
        /// </summary>
        public static List<Vector2> RemoveSelfIntersections(List<Vector2> strokePoints)
        {
            // 简化的自相交检测和移除
            // 在实际应用中，可能需要更复杂的算法
            var cleanedPoints = new List<Vector2>();
            
            for (int i = 0; i < strokePoints.Count; i++)
            {
                bool shouldAdd = true;
                
                // 检查是否与之前的线段相交
                for (int j = 0; j < cleanedPoints.Count - 1; j++)
                {
                    if (i > 0 && LineIntersection(strokePoints[i - 1], strokePoints[i], 
                        cleanedPoints[j], cleanedPoints[j + 1], out Vector2 intersection))
                    {
                        // 发现相交，需要处理
                        shouldAdd = false;
                        break;
                    }
                }
                
                if (shouldAdd)
                {
                    cleanedPoints.Add(strokePoints[i]);
                }
            }
            
            return cleanedPoints;
        }
        
        /// <summary>
        /// 计算两条线段的交点
        /// </summary>
        private static bool LineIntersection(Vector2 a1, Vector2 a2, Vector2 b1, Vector2 b2, out Vector2 intersection)
        {
            intersection = Vector2.zero;
            
            Vector2 dir1 = a2 - a1;
            Vector2 dir2 = b2 - b1;
            
            float denominator = dir1.x * dir2.y - dir1.y * dir2.x;
            if (Mathf.Abs(denominator) < 0.001f)
            {
                return false; // 平行线
            }
            
            Vector2 diff = b1 - a1;
            float t1 = (diff.x * dir2.y - diff.y * dir2.x) / denominator;
            float t2 = (diff.x * dir1.y - diff.y * dir1.x) / denominator;
            
            if (t1 >= 0f && t1 <= 1f && t2 >= 0f && t2 <= 1f)
            {
                intersection = a1 + dir1 * t1;
                return true;
            }
            
            return false;
        }
    }
} 