using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Services;
using MapEditor.Utility;
using System.Threading.Tasks;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// 工具栏面板，显示编辑工具按钮
    /// </summary>
    public class ToolbarPanel : UIPanel
    {
        public override string PanelId => "toolbar";
        public override string DisplayName => "工具栏";
        
        private VisualElement toolButtonContainer;
        private ToolService _toolService;
        private MapService _mapService;
        private readonly Dictionary<string, ToolButton> toolButtons = new();
        
        public ToolbarPanel(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
        }
        
        public override void Initialize()
        {
            _toolService = MapEditorCore.Instance.GetService<ToolService>();
            _mapService = MapEditorCore.Instance.GetService<MapService>();
            _eventSystem = MapEditorCore.Instance.EventSystem;

            toolButtonContainer = FindElement<VisualElement>("ToolButtonContainer");
            
            if (toolButtonContainer == null)
            {
                Debug.LogError("ToolButtonContainer not found in toolbar template");
                return;
            }

            // 注册工具变更事件
            _eventSystem?.Subscribe<ToolChangedEvent>(OnToolChanged);

            // 注册图层切换事件
            _eventSystem?.Subscribe<ActiveLayerChangedEvent>(OnActiveLayerChanged);
            
            // 创建工具按钮
            CreateToolButtons();
            
            // 注册其他控制按钮事件
            RegisterButtonClick("NewMapButton", OnNewMapButtonClicked);
            RegisterButtonClick("SaveButton", OnSaveButtonClicked);
            RegisterButtonClick("LoadButton", OnLoadButtonClicked);
            RegisterButtonClick("SettingsButton", OnSettingsButtonClicked);
        }
        
        private void CreateToolButtons()
        {
            if (_toolService == null) return;
            
            var toolIds = _toolService.GetAllToolIds();
            var toolIdsList = new List<string>(toolIds);
            
            for (int i = 0; i < toolIdsList.Count; i++)
            {
                var toolId = toolIdsList[i];
                var tool = _toolService.GetTool(toolId);
                if (tool == null) continue;
                
                // 创建一个闭包来捕获toolId
                string capturedToolId = toolId;
                var button = new ToolButton(tool, i + 1, () => TryActivateTool(capturedToolId));
                
                // 如果是最后一个按钮，移除右边距
                if (i == toolIdsList.Count - 1)
                {
                    button.style.marginRight = 0;
                }
                
                toolButtonContainer.Add(button);
                toolButtons[toolId] = button;
            }
            
            // 初始化时更新工具按钮的可用性
            UpdateToolButtonAvailability();
        }
        
        private void TryActivateTool(string toolId)
        {
            // 检查工具是否在当前图层可用
            var availableToolIds = _toolService.GetAvailableToolIds();
            if (!System.Linq.Enumerable.Contains(availableToolIds, toolId))
            {
                var tool = _toolService.GetTool(toolId);
                var activeLayer = MapEditorCore.Instance.LayerStore?.ActiveLayer;
                
                string layerTypeName = activeLayer?.Type.ToString() ?? "当前";
                string message = $"工具 '{tool?.DisplayName ?? toolId}' 不能在{layerTypeName}图层上使用";
                
                Debug.LogWarning(message);
                
                // 显示用户提示
                RequestShowMessage("工具不可用", message, MessageType.Warning);
                
                return;
            }
            
            _toolService.ActivateTool(toolId);
        }
        
        private void OnToolChanged(ToolChangedEvent evt)
        {
            // 更新按钮状态
            foreach (var kvp in toolButtons)
            {
                kvp.Value.SetActive(kvp.Key == evt.NewToolId);
            }
        }
        
        private void OnActiveLayerChanged(ActiveLayerChangedEvent evt)
        {
            // 更新工具按钮的可用性
            UpdateToolButtonAvailability();
        }
        
        private void UpdateToolButtonAvailability()
        {
            if (_toolService == null) return;
            
            var availableToolIds = _toolService.GetAvailableToolIds();
            
            foreach (var kvp in toolButtons)
            {
                var isAvailable = System.Linq.Enumerable.Contains(availableToolIds, kvp.Key);
                kvp.Value.SetEnabled(isAvailable);
            }
        }
        
        private void OnSaveButtonClicked()
        {
            _mapService.SaveMap();
            RequestShowMessage("保存成功", "地图数据已保存", MessageType.Success);
        }
        
        private async void OnLoadButtonClicked()
        {
            // 使用文件选择框选择 json 文件
            string filePath = await FileDialogHelper.SelectJsonFileAsync("选择地图 JSON 文件");
            if (string.IsNullOrEmpty(filePath))
            {
                Debug.Log("用户取消加载地图");
                return;
            }

            _mapService.LoadMapFromFile(filePath);
            RequestShowMessage("加载完成", "地图已加载", MessageType.Success);
        }
        
        private void OnNewMapButtonClicked()
        {
            // 首先尝试从已注册面板获取（通过依赖容器解析 IUIManager 接口）
            var existingUIManager = UIManager.Instance as IUIManager;
            var newMapDialog = existingUIManager?.GetPanel("newMapDialog") as NewMapDialog;
            // 如果面板不存在，则创建并发送注册请求
            if (newMapDialog == null)
            {
                // 如果对话框不存在，创建并注册
                var template = Resources.Load<VisualTreeAsset>("UI/NewMapDialog");
                if (template != null)
                {
                    newMapDialog = new NewMapDialog(null, template);
                    RequestRegisterPanel(newMapDialog);
                }
                else
                {
                    Debug.LogError("NewMapDialog template not found");
                    return;
                }
            }
            
            // 注册事件
            newMapDialog.OnMapCreated += OnNewMapCreated;
            newMapDialog.OnCancelled += OnNewMapCancelled;
            
            // 显示对话框
            newMapDialog.ShowDialog();
        }
        
        private void OnNewMapCreated(NewMapDialogData mapData)
        {
            // 创建新地图
            try
            {
                // 使用新的重载版本，传递完整的对话框数据
                var newMap = _mapService.CreateNewMap(mapData);
                if (newMap != null)
                {
                    string textureInfo = mapData.baseTextureIndex >= 0 ? 
                        $"，基础纹理: {mapData.baseTextureIndex}" : "";
                    RequestShowMessage("成功", $"地图 '{mapData.mapName}' 创建成功{textureInfo}", MessageType.Success);
                    Debug.Log($"新地图创建成功: {mapData.mapName} ({mapData.mapSize.x}×{mapData.mapSize.y}){textureInfo}");
                }
                else
                {
                    RequestShowMessage("错误", "地图创建失败", MessageType.Error);
                }
            }
            catch (System.Exception ex)
            {
                RequestShowMessage("错误", $"地图创建失败: {ex.Message}", MessageType.Error);
                Debug.LogError($"地图创建异常: {ex}");
            }
        }
        
        private void OnNewMapCancelled()
        {
            Debug.Log("用户取消创建新地图");
        }
        
        private void OnSettingsButtonClicked()
        {
            // 显示设置面板
            RequestShowPanel("settings");
        }
        
        // 工具按钮自定义控件
        private class ToolButton : VisualElement
        {
            private readonly Button button;
            
            public ToolButton(IMapTool tool, int shortcutIndex, System.Action clickCallback)
            {
                // 创建按钮布局
                this.AddToClassList("tool-button");
                
                button = new Button(clickCallback);
                button.text = $"{tool.DisplayName} ({shortcutIndex})";
                button.AddToClassList("tool-button-text");
                
                this.Add(button);
            }
            
            public void SetActive(bool active)
            {
                if (active)
                {
                    this.AddToClassList("active");
                    button.AddToClassList("active");
                }
                else
                {
                    this.RemoveFromClassList("active");
                    button.RemoveFromClassList("active");
                }
            }
            
            public new void SetEnabled(bool enabled)
            {
                style.display = enabled ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }
    }
}