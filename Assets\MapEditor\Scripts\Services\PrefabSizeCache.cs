using System.Collections.Generic;
using UnityEngine;
using MapEditor.Core;

namespace MapEditor.Services
{
    /// <summary>
    /// Prefab尺寸缓存服务，用于缓存已加载Prefab的实际尺寸信息
    /// </summary>
    public class PrefabSizeCache : ServiceBase
    {
        /// <summary>
        /// 缓存的Prefab尺寸信息
        /// </summary>
        private readonly Dictionary<string, Vector2> sizeCache = new Dictionary<string, Vector2>();
        
        /// <summary>
        /// 默认尺寸，用于无法获取实际尺寸时的降级处理
        /// </summary>
        private readonly Vector2 defaultSize = new Vector2(1f, 1f);

        public override void Initialize()
        {
            // 初始化时清空缓存
            sizeCache.Clear();
        }

        public override void Start()
        {
            // Start阶段暂无特殊逻辑
        }

        /// <summary>
        /// 获取Prefab的实际尺寸
        /// </summary>
        /// <param name="prefabGuid">Prefab的GUID/路径</param>
        /// <returns>Prefab的实际尺寸</returns>
        public Vector2 GetPrefabSize(string prefabGuid)
        {
            if (string.IsNullOrEmpty(prefabGuid))
            {
                return defaultSize;
            }

            // 先检查缓存
            if (sizeCache.TryGetValue(prefabGuid, out Vector2 cachedSize))
            {
                return cachedSize;
            }

            // 从Prefab资源计算尺寸并缓存
            Vector2 actualSize = CalculatePrefabSize(prefabGuid);
            sizeCache[prefabGuid] = actualSize;
            
            Debug.Log($"PrefabSizeCache: 缓存了 {prefabGuid} 的尺寸 {actualSize}");
            return actualSize;
        }

        /// <summary>
        /// 从Prefab资源计算实际尺寸
        /// </summary>
        /// <param name="prefabGuid">Prefab的GUID/路径</param>
        /// <returns>计算得到的尺寸</returns>
        private Vector2 CalculatePrefabSize(string prefabGuid)
        {
            try
            {
                // 尝试加载Prefab
                GameObject prefab = Resources.Load<GameObject>(prefabGuid);
                if (prefab == null)
                {
                    Debug.LogWarning($"PrefabSizeCache: 无法加载Prefab {prefabGuid}，使用默认尺寸");
                    return defaultSize;
                }

                // 计算Prefab的包围框
                Vector2 size = CalculateGameObjectBounds(prefab);
                
                // 如果计算出的尺寸过小，使用默认尺寸
                if (size.x < 0.01f || size.y < 0.01f)
                {
                    Debug.LogWarning($"PrefabSizeCache: Prefab {prefabGuid} 计算出的尺寸过小 {size}，使用默认尺寸");
                    return defaultSize;
                }

                return size;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"PrefabSizeCache: 计算Prefab尺寸时发生错误 {prefabGuid}: {ex.Message}");
                return defaultSize;
            }
        }

        /// <summary>
        /// 计算GameObject的包围框尺寸
        /// </summary>
        /// <param name="gameObject">要计算的GameObject</param>
        /// <returns>包围框尺寸</returns>
        private Vector2 CalculateGameObjectBounds(GameObject gameObject)
        {
            // 优先使用SpriteRenderer（2D项目主要使用）
            SpriteRenderer spriteRenderer = gameObject.GetComponent<SpriteRenderer>();
            if (spriteRenderer != null && spriteRenderer.sprite != null)
            {
                Vector2 spriteSize = spriteRenderer.sprite.bounds.size;
                Debug.Log($"PrefabSizeCache: 从SpriteRenderer获取尺寸 {spriteSize}");
                return spriteSize;
            }

            // 尝试使用Renderer
            Renderer renderer = gameObject.GetComponent<Renderer>();
            if (renderer != null)
            {
                Vector3 boundsSize = renderer.bounds.size;
                Vector2 size2D = new Vector2(boundsSize.x, boundsSize.y);
                Debug.Log($"PrefabSizeCache: 从Renderer获取尺寸 {size2D}");
                return size2D;
            }

            // 检查子对象的Renderer
            Renderer[] childRenderers = gameObject.GetComponentsInChildren<Renderer>();
            if (childRenderers.Length > 0)
            {
                Bounds combinedBounds = childRenderers[0].bounds;
                for (int i = 1; i < childRenderers.Length; i++)
                {
                    combinedBounds.Encapsulate(childRenderers[i].bounds);
                }
                Vector2 size2D = new Vector2(combinedBounds.size.x, combinedBounds.size.y);
                Debug.Log($"PrefabSizeCache: 从子对象Renderer获取尺寸 {size2D}");
                return size2D;
            }

            // 尝试使用Collider2D
            Collider2D collider2D = gameObject.GetComponent<Collider2D>();
            if (collider2D != null)
            {
                Vector2 colliderSize = collider2D.bounds.size;
                Debug.Log($"PrefabSizeCache: 从Collider2D获取尺寸 {colliderSize}");
                return colliderSize;
            }

            // 检查子对象的Collider2D
            Collider2D[] childColliders = gameObject.GetComponentsInChildren<Collider2D>();
            if (childColliders.Length > 0)
            {
                Bounds combinedBounds = childColliders[0].bounds;
                for (int i = 1; i < childColliders.Length; i++)
                {
                    combinedBounds.Encapsulate(childColliders[i].bounds);
                }
                Vector2 size2D = new Vector2(combinedBounds.size.x, combinedBounds.size.y);
                Debug.Log($"PrefabSizeCache: 从子对象Collider2D获取尺寸 {size2D}");
                return size2D;
            }

            Debug.LogWarning($"PrefabSizeCache: 无法获取GameObject的有效尺寸信息，使用默认尺寸");
            return defaultSize;
        }

        /// <summary>
        /// 清除指定Prefab的缓存
        /// </summary>
        /// <param name="prefabGuid">要清除的Prefab GUID</param>
        public void ClearCache(string prefabGuid)
        {
            if (sizeCache.ContainsKey(prefabGuid))
            {
                sizeCache.Remove(prefabGuid);
                Debug.Log($"PrefabSizeCache: 清除了 {prefabGuid} 的缓存");
            }
        }

        /// <summary>
        /// 清除所有缓存
        /// </summary>
        public void ClearAllCache()
        {
            int cacheCount = sizeCache.Count;
            sizeCache.Clear();
            Debug.Log($"PrefabSizeCache: 清除了所有缓存，共 {cacheCount} 项");
        }

        /// <summary>
        /// 获取当前缓存的项目数量
        /// </summary>
        public int CacheCount => sizeCache.Count;
    }
} 