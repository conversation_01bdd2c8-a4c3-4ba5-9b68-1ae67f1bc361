using System;

namespace MapEditor.Core
{
    public abstract class ServiceBase : IService
    {
        private IMapEditorCore mapEditor;
        private IEventSystem eventSystem;
        private bool isStarted = false;
        public bool IsStarted { get => isStarted;  set => isStarted = value; }

        protected IMapEditorCore core => mapEditor;
     
    

        public void SetUp(IMapEditorCore mapEditor,IEventSystem eventSystem){
            this.mapEditor = mapEditor;
            this.eventSystem = eventSystem;
        }

        #region LifeCycle
        public abstract void Initialize();
        public abstract void Start();
        public virtual void OnDestroy(){}
        #endregion LifeCycle

        #region Event
        protected void RegisterEvent<TEvent>(Action<TEvent> action, int priority = 0)
        {
            eventSystem.Subscribe(action, priority);
        }
        protected void UnregisterEvent<TEvent>(Action<TEvent> action) 
        {
            eventSystem.Unsubscribe(action);
        }
        protected void PublishEvent<TEvent>(TEvent eventData)
        {
            eventSystem.Publish(eventData);
        }

        protected void PublishAsyncEvent<TEvent>(TEvent eventData)
        {
            eventSystem.PublishAsync(eventData);
        }
        #endregion Event


        #region Service

        protected TService GetService<TService>() where TService : IService
        {
            return mapEditor.GetService<TService>();
        }

        protected void RegisterService<TService>(TService service) where TService : IService
        {
            mapEditor.RegisterService(service);
        }
        protected void UnregisterService<TService>() where TService : IService
        {
            mapEditor.UnregisterService<TService>();
        }
        #endregion Service
        
    }
}