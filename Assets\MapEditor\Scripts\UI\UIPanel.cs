using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// UI面板基类，实现IUIPanel接口的基本功能
    /// </summary>
    public abstract class UIPanel : IUIPanel
    {
        protected VisualElement root;
        protected IUIManager uiManager;
        protected bool isVisible = true;

        protected IEventSystem _eventSystem;
        
        public abstract string PanelId { get; }
        public abstract string DisplayName { get; }
        
        public VisualElement Root => root;
        
        public bool IsVisible
        {
            get => isVisible;
            set
            {
                if (isVisible != value)
                {
                    isVisible = value;
                    if (root != null)
                    {
                        root.style.display = isVisible ? DisplayStyle.Flex : DisplayStyle.None;
                    }
                }
            }
        }
        
        public UIPanel(IUIManager uiManager, VisualTreeAsset template)
        {
            this.uiManager = uiManager;
            
            if (template != null)
            {
                root = template.Instantiate();
                root.name = PanelId;
                
                // 默认状态
                root.style.display = isVisible ? DisplayStyle.Flex : DisplayStyle.None;
            }
            else
            {
                root = new VisualElement
                {
                    name = PanelId
                };
            }
        }
        
        /// <summary>
        /// 设置面板的容器元素
        /// 这允许面板使用UI布局中已经存在的元素，而不是创建新的元素
        /// </summary>
        /// <param name="container">要使用的容器元素</param>
        public void SetContainer(VisualElement container)
        {
            if (container != null)
            {
                // 保存容器引用为根元素
                root = container;
                root.name = PanelId;
            }
        }
        
        public virtual void Initialize()
        {
            // 子类实现具体初始化逻辑
        }
        
        /// <summary>
        /// 设置依赖容器
        /// </summary>
        public void SetContainer()
        {
            // 缓存 EventService 以便快速发布事件
            if (_eventSystem == null)
            {
                _eventSystem = MapEditorCore.Instance.EventSystem;
            }
        }
        
        public virtual void UpdatePanel()
        {
            // 子类实现面板更新逻辑
        }
        
        public virtual void ClosePanel()
        {
            IsVisible = false;
        }
        
        /// <summary>
        /// 查找子元素
        /// </summary>
        protected T FindElement<T>(string name) where T : VisualElement
        {
            return root.Q<T>(name);
        }
        
        /// <summary>
        /// 注册按钮点击事件
        /// </summary>
        protected void RegisterButtonClick(string buttonName, System.Action callback)
        {
            var button = FindElement<Button>(buttonName);
            if (button != null)
            {
                button.clicked += callback;
                Debug.Log($"Registered button click event for '{buttonName}' in panel {DisplayName}");
            }
            else
            {
                Debug.LogWarning($"Button '{buttonName}' not found in panel {DisplayName}");
            }
        }

        #region Helper Methods for UI Requests
        /// <summary>
        /// 发送显示消息框请求
        /// </summary>
        protected void RequestShowMessage(string title, string message, MessageType type = MessageType.Info)
        {
            _eventSystem?.Publish(new RequestShowMessageEvent(title, message, type));
        }

        /// <summary>
        /// 发送显示面板请求
        /// </summary>
        protected void RequestShowPanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId)) return;
            _eventSystem?.Publish(new RequestShowPanelEvent(panelId));
        }

        /// <summary>
        /// 发送隐藏面板请求
        /// </summary>
        protected void RequestHidePanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId)) return;
            _eventSystem?.Publish(new RequestHidePanelEvent(panelId));
        }

        /// <summary>
        /// 发送注册面板请求
        /// </summary>
        protected void RequestRegisterPanel(IUIPanel panel)
        {
            if (panel == null) return;
            _eventSystem?.Publish(new RequestRegisterPanelEvent(panel));
        }

        /// <summary>
        /// 发送注销面板请求
        /// </summary>
        protected void RequestUnregisterPanel(string panelId)
        {
            if (string.IsNullOrEmpty(panelId)) return;
            _eventSystem?.Publish(new RequestUnregisterPanelEvent(panelId));
        }
        #endregion
    }
} 