# 曲线绘制工具设计文档

## 文档信息
- **创建日期**: 2024-12-19
- **版本**: 1.0
- **作者**: MapEditor Team
- **文档类型**: 功能设计文档

---

## 1. 需求和功能描述

### 1.1 背景
当前地图编辑器提供了画笔工具（BrushTool）进行自由绘制，但缺乏精确的线性绘制能力。用户经常需要绘制道路、河流、边界等线性地形特征，使用画笔工具难以保证线条的平滑性和一致性。

### 1.2 功能目标
开发一个基于曲线的地形绘制工具，允许用户：
1. 通过控制点创建和编辑平滑曲线
2. 设置曲线的宽度和边缘柔和度
3. 选择材质并沿曲线进行精确绘制
4. 支持多种曲线类型和编辑操作

### 1.3 核心功能
- **曲线编辑**: 支持添加、移动、删除控制点
- **实时预览**: 显示曲线路径和绘制区域预览
- **参数调节**: 宽度、柔和度、材质、强度等参数
- **多种曲线类型**: 直线、样条曲线、贝塞尔曲线
- **高质量渲染**: 平滑的边缘处理和材质混合

### 1.4 用户体验流程
1. 激活曲线工具
2. 点击放置控制点，形成基础路径
3. 调整控制点位置优化曲线形状
4. 设置绘制参数（宽度、材质等）
5. 预览效果并确认绘制
6. 清除曲线数据，准备下一次编辑

---

## 2. 需求拆解

### 2.1 核心模块

#### 2.1.1 曲线数据管理
- **控制点管理**: 增删改控制点
- **曲线类型**: 支持多种数学曲线
- **数据序列化**: 支持保存和加载曲线
- **撤销重做**: 编辑操作的历史管理

#### 2.1.2 曲线几何计算
- **曲线生成**: 从控制点生成平滑曲线
- **路径扩展**: 将曲线扩展为带状区域
- **法向量计算**: 计算曲线各点的法向量
- **距离场生成**: 计算像素点到曲线的距离

#### 2.1.3 交互系统
- **控制点编辑**: 鼠标拖拽控制点
- **曲线预览**: 实时显示曲线形状
- **参数调节**: UI面板参数控制
- **快捷键支持**: 常用操作的键盘快捷键

#### 2.1.4 渲染集成
- **GPU绘制**: 集成到现有渲染管线
- **Mask生成**: 生成曲线绘制蒙版
- **材质应用**: 复用现有材质系统
- **性能优化**: 分块处理和LOD

### 2.2 技术约束
- **运行时编辑**: 必须在游戏运行时工作，不依赖Editor API
- **性能要求**: 支持大地图实时编辑，流畅的交互体验
- **兼容性**: 与现有TilemapLayer和BrushTool系统兼容
- **平台支持**: Windows平台，URP 2D渲染管线

### 2.3 质量要求
- **视觉质量**: 平滑的曲线和柔和的边缘 
- **精度**: 控制点定位精确到像素级别
- **稳定性**: 边界情况处理，避免崩溃和错误
- **用户体验**: 直观的操作流程，清晰的视觉反馈

---

## 3. 技术实现思路

### 3.1 系统架构

#### 3.1.1 总体设计
```
CurveTool (MapToolBase)
├── CurveEditor (曲线编辑器)
│   ├── ControlPointManager (控制点管理)
│   ├── CurveGenerator (曲线生成)
│   └── CurvePreview (预览渲染)
├── CurveRenderer (曲线渲染器)
│   ├── StrokeExpander (描边扩展)
│   ├── DistanceFieldGenerator (距离场生成)
│   └── GPUIntegration (GPU集成)
└── CurveUI (用户界面)
    ├── ParameterPanel (参数面板)
    └── ControlPointGizmos (控制点辅助显示)
```

#### 3.1.2 与现有系统集成
- **继承MapToolBase**: 复用工具框架
- **使用TilemapLayerRenderer**: 复用GPU绘制管线
- **集成ToolService**: 工具管理和切换
- **使用UI Toolkit**: 保持UI风格一致

### 3.2 核心数据结构

#### 3.2.1 曲线工具主类
```csharp
public class CurveTool : MapToolBase
{
    // 曲线数据
    private List<Vector2> controlPoints;
    private CurveType curveType;
    private bool isEditing;
    
    // 绘制参数
    private CurveStrokeSettings strokeSettings;
    
    // 子系统
    private CurveEditor curveEditor;
    private CurveRenderer curveRenderer;
    private CurvePreview curvePreview;
}
```

#### 3.2.2 描边设置
```csharp
public struct CurveStrokeSettings
{
    public float width;              // 描边宽度 (世界单位)
    public float edgeSoftness;       // 边缘柔和度 (0-1)
    public int materialIndex;        // 材质索引
    public float strength;           // 绘制强度 (0-1)
    public CurveType curveType;      // 曲线类型
    public float resolution;         // 曲线分辨率
}
```

#### 3.2.3 曲线段数据
```csharp
public struct CurveSegment
{
    public Vector2 position;         // 曲线点位置
    public Vector2 tangent;          // 切线方向
    public Vector2 normal;           // 法向量
    public float distance;           // 沿曲线距离
    public float width;              // 局部宽度
}
```

### 3.3 核心算法

#### 3.3.1 曲线生成算法
```csharp
// Catmull-Rom样条曲线生成
public static List<Vector2> GenerateCatmullRomSpline(
    List<Vector2> controlPoints, 
    float resolution)
{
    // 1. 为首尾添加虚拟控制点
    // 2. 分段计算样条参数
    // 3. 按分辨率采样曲线点
    // 4. 返回密集的曲线点列表
}

// 贝塞尔曲线生成
public static List<Vector2> GenerateBezierCurve(
    List<Vector2> controlPoints, 
    float resolution)
{
    // 1. 计算贝塞尔控制点
    // 2. 德卡斯特里奥算法递归计算
    // 3. 自适应细分优化
}
```

#### 3.3.2 描边扩展算法
```csharp
public static List<Vector2> ExpandCurveToStroke(
    List<CurveSegment> curveSegments, 
    float width)
{
    // 1. 计算每个段的左右边界点
    // 2. 处理转角连接 (miter/round/bevel)
    // 3. 生成闭合多边形
    // 4. 处理自相交情况
}
```

#### 3.3.3 距离场计算
```csharp
public static float CalculateDistanceToStroke(
    Vector2 worldPos, 
    List<CurveSegment> segments,
    CurveStrokeSettings settings)
{
    // 1. 找到最近的曲线段
    // 2. 计算点到线段的最短距离
    // 3. 基于宽度和柔和度计算权重
    // 4. 应用边缘渐变函数
}
```

### 3.4 GPU渲染管线

#### 3.4.1 新增Compute Shader
```hlsl
// CurveStroke.compute
#pragma kernel CSCurveStroke

RWTexture2D<float> _StrokeMask;
StructuredBuffer<CurveSegment> _CurveSegments;
float2 _ChunkOffset;
float _StrokeWidth;
float _EdgeSoftness;
int _SegmentCount;

[numthreads(8,8,1)]
void CSCurveStroke(uint3 id : SV_DispatchThreadID)
{
    float2 worldPos = _ChunkOffset + id.xy / PixelsPerUnit;
    float distance = CalculateDistanceToStroke(worldPos);
    float weight = SoftEdgeFunction(distance, _StrokeWidth, _EdgeSoftness);
    _StrokeMask[id.xy] = weight;
}
```

#### 3.4.2 渲染器扩展
```csharp
// TilemapLayerRenderer新增方法
public void DrawCurveStroke(
    List<CurveSegment> segments, 
    CurveStrokeSettings settings)
{
    // 1. 计算影响的Chunk范围
    // 2. 为每个Chunk生成描边Mask
    // 3. 调用现有的GPU绘制管线
    // 4. 处理跨Chunk边界的连续性
}
```

### 3.5 交互系统设计

#### 3.5.1 编辑状态机
```csharp
public enum CurveEditState
{
    Idle,               // 空闲状态
    PlacingPoints,      // 放置控制点
    EditingPoint,       // 编辑控制点
    AdjustingParams,    // 调整参数
    Previewing,         // 预览绘制
    Confirming          // 确认绘制
}
```

#### 3.5.2 输入处理
```csharp
public override void OnSceneInput(InputContext context)
{
    switch (currentState)
    {
        case CurveEditState.PlacingPoints:
            HandlePointPlacement(context);
            break;
        case CurveEditState.EditingPoint:
            HandlePointDragging(context);
            break;
        // ... 其他状态处理
    }
}
```

### 3.6 预览系统

#### 3.6.1 实时预览组件
```csharp
public class CurvePreview : MonoBehaviour
{
    private LineRenderer curveLineRenderer;      // 曲线中心线
    private LineRenderer strokeLineRenderer;     // 描边边界
    private MeshRenderer fillRenderer;           // 填充区域
    
    public void UpdatePreview(
        List<Vector2> controlPoints, 
        CurveStrokeSettings settings)
    {
        // 更新曲线显示
        // 更新描边边界
        // 更新填充区域
    }
}
```

#### 3.6.2 控制点可视化
```csharp
public class ControlPointGizmo : MonoBehaviour
{
    private SpriteRenderer spriteRenderer;
    private bool isSelected;
    private bool isHovered;
    
    public void SetState(ControlPointState state)
    {
        // 根据状态更新视觉表现
        // 支持选中、悬停、普通状态
    }
}
```

---

## 4. 任务拆分

### 4.1 第一阶段：基础框架 (预计3-4天)

#### 4.1.1 Task 1.1: 创建工具基础结构
- **估时**: 1天
- **描述**: 创建CurveTool主类，继承MapToolBase
- **交付物**: 
  - `CurveTool.cs` - 主工具类
  - 工具注册到ToolService
  - 基础激活/停用逻辑
- **验收标准**: 能够切换到曲线工具，显示基本UI

#### 4.1.2 Task 1.2: 控制点管理系统
- **估时**: 1天
- **描述**: 实现控制点的增删改功能
- **交付物**:
  - `ControlPointManager.cs` - 控制点管理
  - `ControlPointGizmo.cs` - 控制点可视化
  - 鼠标交互逻辑
- **验收标准**: 能够点击添加控制点，拖拽移动控制点

#### 4.1.3 Task 1.3: 基础曲线生成
- **估时**: 1天
- **描述**: 实现直线和简单样条曲线生成
- **交付物**:
  - `CurveGenerator.cs` - 曲线生成算法
  - `CurveSegment.cs` - 曲线段数据结构
  - 支持直线和Catmull-Rom样条
- **验收标准**: 控制点变化时能实时生成平滑曲线

#### 4.1.4 Task 1.4: 预览系统基础
- **估时**: 1天
- **描述**: 实现曲线的实时预览显示
- **交付物**:
  - `CurvePreview.cs` - 预览渲染组件
  - LineRenderer集成
  - 实时更新逻辑
- **验收标准**: 能够实时显示曲线路径和控制点

### 4.2 第二阶段：描边算法 (预计4-5天)

#### 4.2.1 Task 2.1: 描边扩展算法
- **估时**: 2天
- **描述**: 实现曲线到描边区域的扩展算法
- **交付物**:
  - `StrokeExpander.cs` - 描边扩展算法
  - 法向量计算
  - 转角处理 (miter join)
- **验收标准**: 能够根据宽度参数生成描边边界

#### 4.2.2 Task 2.2: 距离场生成
- **估时**: 2天
- **描述**: 实现高质量的距离场计算算法
- **交付物**:
  - `DistanceFieldGenerator.cs` - 距离场计算
  - 边缘柔和度处理
  - 性能优化
- **验收标准**: 生成平滑的边缘过渡效果

#### 4.2.3 Task 2.3: 预览扩展
- **估时**: 1天
- **描述**: 扩展预览系统显示描边区域
- **交付物**:
  - 描边边界预览
  - 填充区域预览
  - 参数实时调节预览
- **验收标准**: 能够预览最终的绘制效果

### 4.3 第三阶段：GPU集成 (预计3-4天)

#### 4.3.1 Task 3.1: Compute Shader开发
- **估时**: 2天
- **描述**: 开发曲线描边的GPU计算着色器
- **交付物**:
  - `CurveStroke.compute` - GPU计算着色器
  - StructuredBuffer数据传递
  - 并行距离计算优化
- **验收标准**: GPU着色器能够正确计算描边mask

#### 4.3.2 Task 3.2: 渲染器集成
- **估时**: 1天
- **描述**: 集成到现有TilemapLayerRenderer
- **交付物**:
  - `TilemapLayerRenderer.DrawCurveStroke()` 方法
  - Chunk分块处理
  - 材质系统集成
- **验收标准**: 能够在地图上实际绘制曲线描边

#### 4.3.3 Task 3.3: 性能优化
- **估时**: 1天
- **描述**: 优化大曲线和复杂形状的性能
- **交付物**:
  - LOD系统
  - 可见性裁剪
  - 批处理优化
- **验收标准**: 大型曲线绘制流畅，无明显卡顿

### 4.4 第四阶段：用户界面 (预计2-3天)

#### 4.4.1 Task 4.1: 参数控制面板
- **估时**: 1天
- **描述**: 创建曲线工具的UI控制面板
- **交付物**:
  - `CurveToolPanel.uxml` - UI布局
  - `CurveToolPanel.cs` - UI逻辑
  - 参数绑定和事件处理
- **验收标准**: 能够通过UI调节所有曲线参数

#### 4.4.2 Task 4.2: 快捷键和交互优化
- **估时**: 1天
- **描述**: 实现快捷键和高级交互功能
- **交付物**:
  - 快捷键配置
  - 右键菜单
  - 撤销重做支持
- **验收标准**: 提供完整的用户交互体验

#### 4.4.3 Task 4.3: 帮助和引导
- **估时**: 0.5天
- **描述**: 添加工具提示和使用说明
- **交付物**:
  - 工具提示文本
  - 操作指引
  - 状态栏信息
- **验收标准**: 用户能够轻松理解和使用工具

### 4.5 第五阶段：测试和优化 (预计2-3天)

#### 4.5.1 Task 5.1: 功能测试
- **估时**: 1天
- **描述**: 全面测试各种使用场景
- **交付物**:
  - 测试用例列表
  - Bug修复
  - 边界情况处理
- **验收标准**: 所有核心功能正常工作

#### 4.5.2 Task 5.2: 性能测试和优化
- **估时**: 1天
- **描述**: 测试大地图和复杂曲线的性能
- **交付物**:
  - 性能基准测试
  - 内存优化
  - 帧率优化
- **验收标准**: 满足性能要求，无内存泄漏

#### 4.5.3 Task 5.3: 用户体验优化
- **估时**: 1天
- **描述**: 优化交互体验和视觉效果
- **交付物**:
  - 交互流程优化
  - 视觉效果调优
  - 用户反馈处理
- **验收标准**: 提供流畅直观的用户体验

### 4.6 风险评估和缓解

#### 4.6.1 技术风险
- **算法复杂度**: 曲线扩展和距离场计算可能比预期复杂
  - **缓解策略**: 先实现简化版本，逐步优化
- **GPU性能**: Compute Shader可能存在性能瓶颈
  - **缓解策略**: 提供CPU fallback方案
- **边界情况**: 复杂曲线形状可能产生异常
  - **缓解策略**: 充分的边界测试和错误处理

#### 4.6.2 进度风险
- **算法研发时间**: 核心算法可能需要更多时间
  - **缓解策略**: 预留缓冲时间，分阶段验收
- **集成复杂度**: 与现有系统集成可能遇到问题
  - **缓解策略**: 早期原型验证，及时调整设计

### 4.7 总体时间线

- **总估时**: 14-19天
- **里程碑1**: 第一阶段完成 - 基础交互可用
- **里程碑2**: 第三阶段完成 - 核心功能完成
- **里程碑3**: 第五阶段完成 - 正式发布

---

## 5. 附录

### 5.1 参考资料
- Unity URP 2D官方文档
- 计算几何算法参考
- 现有BrushTool实现分析

### 5.2 技术决策记录
- 选择Catmull-Rom样条而非B样条：更适合交互式编辑
- 使用Compute Shader而非CPU计算：性能优先原则
- 集成现有渲染管线而非独立实现：保持架构一致性

### 5.3 后续扩展计划
- 变宽度曲线支持
- 纹理方向控制
- 曲线模板库
- 高程地形集成 