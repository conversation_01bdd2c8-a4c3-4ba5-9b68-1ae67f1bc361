using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

namespace MapEditor.Utility
{
    /// <summary>
    /// 使用 Burst Job 高效填充 RenderTexture 的工具。
    /// 仅用于权重贴图初始化阶段，功能与原有逐像素循环完全一致，但具备更高并行效率。
    /// </summary>
    public static class TextureFillJobs
    {
        [BurstCompile]
        private struct FillColorJob : IJobParallelFor
        {
            public NativeArray<Color32> Pixels;
            public Color32 Color;

            public void Execute(int index)
            {
                Pixels[index] = Color;
            }
        }

        /// <summary>
        /// 以指定颜色填充 RenderTexture（RGBA32）。
        /// 注意：此函数在内部创建临时 Texture2D 及 NativeArray，调用后会立即释放。
        /// </summary>
        /// <param name="rt">目标 RenderTexture</param>
        /// <param name="fillColor">填充颜色（Color32，0-255）</param>
        public static void FillRenderTexture(RenderTexture rt, Color32 fillColor)
        {
            if (rt == null) return;

            int pixelCount = rt.width * rt.height;
            if (pixelCount <= 0) return;

            // 1. 批量写入像素数据（CPU 多线程 + Burst SIMD）
            var pixels = new NativeArray<Color32>(pixelCount, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
            var job = new FillColorJob { Pixels = pixels, Color = fillColor };
            // 批大小 1024 经测试在桌面 CPU 上表现较好
            job.Schedule(pixelCount, 1024).Complete();

            // 2. 将数据推送到 GPU
            var tex = new Texture2D(rt.width, rt.height, TextureFormat.RGBA32, false);
            tex.SetPixelData(pixels, 0);
            tex.Apply();

            Graphics.Blit(tex, rt);

            // 3. 资源回收
            Object.Destroy(tex);
            pixels.Dispose();
        }

        /// <summary>
        /// 将权重 RenderTexture 指定通道值清零（RGBA 通道索引 0-3）。
        /// 读取像素到 CPU 修改后写回，适用于 256×256 级别纹理开销可接受。
        /// </summary>
        /// <param name="rt">目标 RenderTexture</param>
        /// <param name="channelIndex">0:R 1:G 2:B 3:A</param>
        public static void ClearChannel(RenderTexture rt, int channelIndex)
        {
            if (rt == null || channelIndex < 0 || channelIndex > 3) return;

            RenderTexture prev = RenderTexture.active;
            RenderTexture.active = rt;

            Texture2D tex = new Texture2D(rt.width, rt.height, TextureFormat.RGBA32, false);
            tex.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
            tex.Apply();

            var pixels = tex.GetPixels32();
            int length = pixels.Length;
            for (int i = 0; i < length; i++)
            {
                Color32 c = pixels[i];
                switch (channelIndex)
                {
                    case 0: c.r = 0; break;
                    case 1: c.g = 0; break;
                    case 2: c.b = 0; break;
                    case 3: c.a = 0; break;
                }
                pixels[i] = c;
            }

            tex.SetPixels32(pixels);
            tex.Apply();

            Graphics.Blit(tex, rt);

            Object.Destroy(tex);
            RenderTexture.active = prev;
        }
    }
} 