using UnityEngine;
using UnityEngine.UIElements;

namespace MapEditor.Core
{
    /// <summary>
    /// UI面板接口，所有UI面板需实现此接口
    /// </summary>
    public interface IUIPanel
    {
        /// <summary>
        /// 面板唯一标识符
        /// </summary>
        string PanelId { get; }
        
        /// <summary>
        /// 面板显示名称
        /// </summary>
        string DisplayName { get; }
        
        /// <summary>
        /// 面板根元素
        /// </summary>
        VisualElement Root { get; }
        
        /// <summary>
        /// 面板是否可见
        /// </summary>
        bool IsVisible { get; set; }
        
        /// <summary>
        /// 初始化面板
        /// </summary>
        void Initialize();
        
        /// <summary>
        /// 更新面板
        /// </summary>
        void UpdatePanel();
        
        /// <summary>
        /// 关闭面板
        /// </summary>
        void ClosePanel();
    }
    
    /// <summary>
    /// UI管理器接口，负责管理所有UI面板和控件
    /// </summary>
    public interface IUIManager
    {
        /// <summary>
        /// UI根文档
        /// </summary>
        UIDocument RootDocument { get; }
        
        /// <summary>
        /// 获取UI根元素
        /// </summary>
        VisualElement Root { get; }
        
        /// <summary>
        /// 注册UI面板
        /// </summary>
        void RegisterPanel(IUIPanel panel);
        
        /// <summary>
        /// 注销UI面板
        /// </summary>
        void UnregisterPanel(string panelId);
        
        /// <summary>
        /// 显示指定ID的面板
        /// </summary>
        void ShowPanel(string panelId);
        
        /// <summary>
        /// 隐藏指定ID的面板
        /// </summary>
        void HidePanel(string panelId);
        
        /// <summary>
        /// 获取指定ID的面板
        /// </summary>
        IUIPanel GetPanel(string panelId);
        
        /// <summary>
        /// 显示消息弹窗
        /// </summary>
        void ShowMessage(string title, string message, MessageType type = MessageType.Info);
        
        /// <summary>
        /// 显示确认弹窗
        /// </summary>
        void ShowConfirmDialog(string title, string message, System.Action onConfirm, System.Action onCancel = null);
    }
    
    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        Info,
        Warning,
        Error,
        Success
    }
} 