using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using MapEditor.Data;

namespace MapEditor.EditorTools
{
    /// <summary>
    /// 离线生成 Texture2DArray 及其元数据的工具。
    /// </summary>
    public class GroundTexturesArrayBuilder : EditorWindow
    {
        private const string DefaultSourceFolder = "Assets/MapEditor/Resources/GroundTextures";
        private const string DefaultSavePath = "Assets/MapEditor/Resources/GroundTexturesArray.asset";

        private string _sourceFolder = DefaultSourceFolder;
        private string _savePath = DefaultSavePath;

        private Vector2Int _size = new Vector2Int(512, 512);
        private TextureFormat _format = TextureFormat.RGBA32;

        [MenuItem("Tools/MapEditor/Ground Textures Array Builder", priority = 201)]
        private static void ShowWindow()
        {
            var win = GetWindow<GroundTexturesArrayBuilder>(true, "Ground Textures Array Builder");
            win.minSize = new Vector2(420, 260);
        }

        private void OnEnable()
        {
            // 如果 GroundTextures 目录下有纹理，自动用第一张作为默认尺寸
            var guids = AssetDatabase.FindAssets("t:GroundTextureSO", new[] { DefaultSourceFolder });
            if (guids.Length > 0)
            {
                var so = AssetDatabase.LoadAssetAtPath<GroundTextureSO>(AssetDatabase.GUIDToAssetPath(guids[0]));
                if (so?.texture != null)
                {
                    _size = new Vector2Int(so.texture.width, so.texture.height);
                }
            }
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Ground Textures Array Builder", EditorStyles.boldLabel);
            EditorGUILayout.Space(6);

            _sourceFolder = EditorGUILayout.TextField("Source Folder", _sourceFolder);
            _savePath = EditorGUILayout.TextField("Save Path", _savePath);

            _size = EditorGUILayout.Vector2IntField("Output Size", _size);
            _format = (TextureFormat)EditorGUILayout.EnumPopup("Texture Format", _format);

            EditorGUILayout.HelpBox("建议保持尺寸与源纹理一致；压缩格式需 Unity 支持 Texture2DArray 的类型。", MessageType.Info);

            if (GUILayout.Button("Build / Rebuild"))
            {
                BuildArray();
            }
        }

        private void BuildArray()
        {
            if (!Directory.Exists(_sourceFolder))
            {
                EditorUtility.DisplayDialog("Folder Not Found", $"Source folder {_sourceFolder} does not exist.", "OK");
                return;
            }

            string[] guids = AssetDatabase.FindAssets("t:GroundTextureSO", new[] { _sourceFolder });
            if (guids.Length == 0)
            {
                EditorUtility.DisplayDialog("No GroundTextureSO Found", "指定目录下未找到 GroundTextureSO 资产。", "OK");
                return;
            }

            List<GroundTextureSO> soList = guids
                .Select(guid => AssetDatabase.LoadAssetAtPath<GroundTextureSO>(AssetDatabase.GUIDToAssetPath(guid)))
                .Where(so => so != null)
                .OrderBy(so => so.textureIndex)
                .ToList();

            int count = soList.Count;
            Texture2DArray texArray = new Texture2DArray(_size.x, _size.y, count, _format, false)
            {
                filterMode = FilterMode.Bilinear,
                wrapMode = TextureWrapMode.Repeat
            };

            int[] globalIndices = new int[count];
            Vector2[] uvScales = new Vector2[count];

            for (int layer = 0; layer < count; layer++)
            {
                GroundTextureSO so = soList[layer];
                globalIndices[layer] = so.textureIndex;
                uvScales[layer] = so.uvScale;

                CopyTextureToArray(so.texture, texArray, layer);
            }

            texArray.Apply(false, true);

            // 创建或更新资产
            GroundTexturesArrayAsset asset = AssetDatabase.LoadAssetAtPath<GroundTexturesArrayAsset>(_savePath);
            if (asset == null)
            {
                asset = ScriptableObject.CreateInstance<GroundTexturesArrayAsset>();
                AssetDatabase.CreateAsset(asset, _savePath);
            }

            // 移除旧 Texture2DArray 子资产
            if (asset.surfaceArray != null)
            {
                DestroyImmediate(asset.surfaceArray, true);
            }

            asset.surfaceArray = texArray;
            AssetDatabase.AddObjectToAsset(texArray, asset);

            asset.globalIndices = globalIndices;
            asset.uvScales = uvScales;
            asset.width = _size.x;
            asset.height = _size.y;
            asset.textureFormat = _format;

            EditorUtility.SetDirty(asset);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            EditorUtility.DisplayDialog("Success", "GroundTexturesArray.asset has been built successfully.", "OK");
        }

        /// <summary>
        /// 高容错拷贝：支持尺寸/格式不一致与不可读纹理。
        /// </summary>
        private void CopyTextureToArray(Texture2D src, Texture2DArray destArray, int layer)
        {
            if (src == null)
            {
                Debug.LogWarning($"[GroundTexturesArrayBuilder] Source texture is null for layer {layer}. Use gray texture.");
                src = Texture2D.grayTexture;
            }

            // 若尺寸匹配且格式一致，优先尝试 CopyTexture
            if (src.width == destArray.width && src.height == destArray.height)
            {
                try
                {
                    Graphics.CopyTexture(src, 0, 0, destArray, layer, 0);
                    return;
                }
                catch { /* fall back */ }
            }

            // 退回 CPU 路径：获取可读 & 缩放
            Texture2D readable = src;
            if (!src.isReadable)
            {
                RenderTexture rt = RenderTexture.GetTemporary(src.width, src.height, 0, RenderTextureFormat.ARGB32);
                Graphics.Blit(src, rt);
                RenderTexture prev = RenderTexture.active;
                RenderTexture.active = rt;
                readable = new Texture2D(src.width, src.height, TextureFormat.RGBA32, false);
                readable.ReadPixels(new Rect(0, 0, src.width, src.height), 0, 0);
                readable.Apply();
                RenderTexture.active = prev;
                RenderTexture.ReleaseTemporary(rt);
            }

            // 尺寸不匹配需缩放
            if (readable.width != destArray.width || readable.height != destArray.height)
            {
                Texture2D scaled = new Texture2D(destArray.width, destArray.height, TextureFormat.RGBA32, false);
                RenderTexture rt = RenderTexture.GetTemporary(destArray.width, destArray.height, 0, RenderTextureFormat.ARGB32);
                Graphics.Blit(readable, rt);
                RenderTexture prev = RenderTexture.active;
                RenderTexture.active = rt;
                scaled.ReadPixels(new Rect(0, 0, destArray.width, destArray.height), 0, 0);
                scaled.Apply();
                RenderTexture.active = prev;
                RenderTexture.ReleaseTemporary(rt);
                if (readable != src) DestroyImmediate(readable);
                readable = scaled;
            }

            destArray.SetPixels(readable.GetPixels(), layer);
            if (readable != src) DestroyImmediate(readable);
        }
    }
} 