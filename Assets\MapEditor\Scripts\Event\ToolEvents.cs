namespace MapEditor.Event
{
    /// <summary>
    /// 工具改变事件
    /// </summary>
    public struct ToolChangedEvent
    {
        /// <summary>
        /// 旧工具ID
        /// </summary>
        public string OldToolId;

        /// <summary>
        /// 新工具ID
        /// </summary>
        public string NewToolId;
    }

    /// <summary>
    /// 对象创建事件
    /// </summary>
    public struct ObjectCreatedEvent
    {
        public Data.Chunks.ObjectInstance Instance;
        public string LayerId;
    }
} 