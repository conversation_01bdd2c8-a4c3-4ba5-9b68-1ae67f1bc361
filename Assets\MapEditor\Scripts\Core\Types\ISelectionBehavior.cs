using System.Collections.Generic;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 选择行为接口，定义图层特定的选择逻辑
    /// </summary>
    public interface ISelectionBehavior
    {
        /// <summary>
        /// 获取支持的图层类型
        /// </summary>
        LayerType SupportedLayerType { get; }
        
        /// <summary>
        /// 处理点击选择
        /// </summary>
        /// <param name="worldPosition">世界坐标</param>
        /// <param name="isMultiSelect">是否多选模式</param>
        /// <param name="layer">当前图层</param>
        /// <returns>选中的对象列表</returns>
        List<ISelectable> HandlePointSelection(Vector2 worldPosition, bool isMultiSelect, IMapLayer layer);
        
        /// <summary>
        /// 处理框选
        /// </summary>
        /// <param name="selectionRect">选择框矩形</param>
        /// <param name="isMultiSelect">是否多选模式</param>
        /// <param name="layer">当前图层</param>
        /// <returns>选中的对象列表</returns>
        List<ISelectable> HandleRectSelection(Rect selectionRect, bool isMultiSelect, IMapLayer layer);
        
        /// <summary>
        /// 开始拖拽选中的对象
        /// </summary>
        /// <param name="selectedObjects">选中的对象</param>
        /// <param name="startPosition">开始位置</param>
        void StartDrag(List<ISelectable> selectedObjects, Vector2 startPosition);
        
        /// <summary>
        /// 更新拖拽
        /// </summary>
        /// <param name="currentPosition">当前位置</param>
        void UpdateDrag(Vector2 currentPosition);
        
        /// <summary>
        /// 结束拖拽
        /// </summary>
        void EndDrag();
        
        /// <summary>
        /// 开始缩放
        /// </summary>
        /// <param name="selectedObjects">选中的对象</param>
        /// <param name="handleIndex">缩放手柄索引</param>
        /// <param name="startPosition">开始位置</param>
        void StartScale(List<ISelectable> selectedObjects, int handleIndex, Vector2 startPosition);
        
        /// <summary>
        /// 更新缩放
        /// </summary>
        /// <param name="currentPosition">当前位置</param>
        void UpdateScale(Vector2 currentPosition);
        
        /// <summary>
        /// 结束缩放
        /// </summary>
        void EndScale();
        
        /// <summary>
        /// 开始旋转
        /// </summary>
        /// <param name="selectedObjects">选中的对象</param>
        /// <param name="startPosition">开始位置</param>
        void StartRotate(List<ISelectable> selectedObjects, Vector2 startPosition);
        
        /// <summary>
        /// 更新旋转
        /// </summary>
        /// <param name="currentPosition">当前位置</param>
        void UpdateRotate(Vector2 currentPosition);
        
        /// <summary>
        /// 结束旋转
        /// </summary>
        void EndRotate();
        
        /// <summary>
        /// 删除选中的对象
        /// </summary>
        /// <param name="selectedObjects">选中的对象</param>
        void DeleteSelected(List<ISelectable> selectedObjects);
        
        /// <summary>
        /// 是否支持拖拽
        /// </summary>
        bool SupportsDragging { get; }
        
        /// <summary>
        /// 是否支持缩放
        /// </summary>
        bool SupportsScaling { get; }
        
        /// <summary>
        /// 是否支持旋转
        /// </summary>
        bool SupportsRotation { get; }
        
        /// <summary>
        /// 是否支持删除
        /// </summary>
        bool SupportsDeletion { get; }
    }
} 