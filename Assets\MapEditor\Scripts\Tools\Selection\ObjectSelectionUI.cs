using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.UI;
using MapEditor.Services;
using MapEditor.Data.Chunks;
using MapEditor.Event;

namespace MapEditor.Tools.Selection
{
    /// <summary>
    /// 对象层的选择UI实现
    /// </summary>
    public class ObjectSelectionUI : ISelectionUI
    {
        private IUIManager uiManager;
        private VisualElement currentContainer;
        private VisualElement selectionPanel;

        
        // UI元素
        private Label selectionInfoLabel;
        private VisualElement propertiesContainer;
        private FloatField positionX, positionY;
        private FloatField rotationField;
        private FloatField scaleX, scaleY;
        private TextField nameField;
        private VisualElement previewContainer;
        private VisualElement iconElement;
        private Label nameLabel;
        private Button applyButton;
        private Button deleteButton;
        
        // 当前选中的对象
        private List<ISelectable> currentSelection;
        
        public LayerType SupportedLayerType => LayerType.Object;
        
        public void SetUIManager(IUIManager uiManager)
        {
            this.uiManager = uiManager;
        }
        

        public void CreateToolOptions(VisualElement container)
        {
            // 工具选项面板（可以为空或添加一些工具设置）
        }
        
        public void CreateSelectionPanel(VisualElement container, List<ISelectable> selectedObjects)
        {
            currentContainer = container;
            currentSelection = selectedObjects;
            
            // 清理旧内容
            ClearSelectionPanel();
            
            // 1. 尝试从UXML模板实例化面板
            VisualTreeAsset tpl = Resources.Load<VisualTreeAsset>("UI/ObjectSelectionPanel");

            if (tpl != null)
            {
                selectionPanel = tpl.Instantiate();

                // 获取模板中的关键元素引用
                previewContainer      = selectionPanel.Q<VisualElement>("PreviewContainer");
                iconElement           = selectionPanel.Q<VisualElement>("ObjectIcon");
                nameLabel             = selectionPanel.Q<Label>("ObjectName");
                selectionInfoLabel    = selectionPanel.Q<Label>("SelectionInfo");
                propertiesContainer   = selectionPanel.Q<VisualElement>("PropertiesContainer");

                // 兜底: 如果容器不存在则创建
                if (propertiesContainer == null)
                {
                    propertiesContainer = new VisualElement();
                    propertiesContainer.name = "PropertiesContainer";
                    selectionPanel.Add(propertiesContainer);
                }
            }
            else
            {
                // 若模板缺失, 退回旧的纯代码构建方式
                selectionPanel = new VisualElement();
                selectionPanel.name = "ObjectSelectionPanel";
                selectionPanel.AddToClassList("selection-panel");

                // 预览/标题区域
                BuildPreviewHeader();

                // 属性容器
                propertiesContainer = new VisualElement();
                propertiesContainer.AddToClassList("properties-container");
            }
            
            if (selectedObjects.Count == 1)
            {
                CreateSingleSelectionUI(selectedObjects[0]);
            }
            else if (selectedObjects.Count > 1)
            {
                CreateMultiSelectionUI(selectedObjects);
            }
            
            // 若模板已经包含 propertiesContainer, 则无需手动添加
            if (tpl == null)
            {
                selectionPanel.Add(propertiesContainer);
            }

            container.Add(selectionPanel);
        }
        
        public void UpdateSelectionPanel(List<ISelectable> selectedObjects)
        {
            if (currentContainer == null) return;
            
            currentSelection = selectedObjects;
            
            // 如果已有面板，更新内容
            if (selectionPanel != null)
            {
                UpdateSelectionInfo();
                
                // 清理属性容器
                propertiesContainer.Clear();
                
                if (selectedObjects.Count == 1)
                {
                    CreateSingleSelectionUI(selectedObjects[0]);
                }
                else if (selectedObjects.Count > 1)
                {
                    CreateMultiSelectionUI(selectedObjects);
                }
            }
            else
            {
                // 创建新面板
                CreateSelectionPanel(currentContainer, selectedObjects);
            }
        }
        
        public void ClearSelectionPanel()
        {
            if (selectionPanel != null && selectionPanel.parent != null)
            {
                selectionPanel.parent.Remove(selectionPanel);
            }
            selectionPanel = null;
            currentContainer = null;
            currentSelection = null;
        }
        
        private void UpdateSelectionInfo()
        {
            if (selectionInfoLabel == null) return;
            
            if (currentSelection == null || currentSelection.Count == 0)
            {
                selectionInfoLabel.text = "无选择";
            }
            else if (currentSelection.Count == 1)
            {
                selectionInfoLabel.text = $"选中对象: {currentSelection[0].DisplayName}";
            }
            else
            {
                selectionInfoLabel.text = $"选中 {currentSelection.Count} 个对象";
            }
        }
        
        private void BuildPreviewHeader()
        {
            previewContainer = new VisualElement();
            previewContainer.name = "PreviewContainer";
            previewContainer.AddToClassList("object-preview-container");
            previewContainer.style.flexDirection = FlexDirection.Row;
            previewContainer.style.alignItems = Align.Center;
            previewContainer.style.marginBottom = 12;
            previewContainer.style.paddingTop = 8;
            previewContainer.style.paddingBottom = 8;
            previewContainer.style.paddingLeft = 8;
            previewContainer.style.paddingRight = 8;

            // 图标
            iconElement = new VisualElement();
            iconElement.name = "ObjectIcon";
            iconElement.AddToClassList("object-preview-icon");
            iconElement.style.width = 48;
            iconElement.style.height = 48;
            iconElement.style.marginRight = 12;
            iconElement.style.backgroundColor = new StyleColor(new Color(0.8f, 0.4f, 0.2f));
            iconElement.style.borderTopLeftRadius = 4;
            iconElement.style.borderTopRightRadius = 4;
            iconElement.style.borderBottomLeftRadius = 4;
            iconElement.style.borderBottomRightRadius = 4;

            // 名称/类型信息
            var infoContainer = new VisualElement();
            infoContainer.style.flexDirection = FlexDirection.Column;
            infoContainer.style.flexGrow = 1;

            nameLabel = new Label();
            nameLabel.AddToClassList("object-name-label");
            nameLabel.style.fontSize = 16;
            nameLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            nameLabel.style.color = new StyleColor(Color.white);
            nameLabel.style.marginBottom = 4;

            selectionInfoLabel = new Label();
            selectionInfoLabel.name = "SelectionInfo";
            selectionInfoLabel.style.fontSize = 12;
            selectionInfoLabel.style.color = new StyleColor(new Color(0.7f,0.7f,0.7f));

            infoContainer.Add(nameLabel);
            infoContainer.Add(selectionInfoLabel);

            previewContainer.Add(iconElement);
            previewContainer.Add(infoContainer);

            selectionPanel.Add(previewContainer);
        }
        
        private void CreateSingleSelectionUI(ISelectable obj)
        {
            // 更新头部信息
            nameLabel.text = obj.DisplayName;
            selectionInfoLabel.text = $"ID: {obj.Id.Substring(0,8)}...";

            // 设置图标（颜色/贴图）
            if (obj is SelectableObjectInstance soi)
            {
                SetObjectIcon(iconElement, soi.ObjectInstance.PrefabGuid);
            }

            // 尝试使用属性模板
            VisualTreeAsset propTpl = Resources.Load<VisualTreeAsset>("UI/ObjectSelectionSingle");

            if (propTpl != null)
            {
                var propRoot = propTpl.Instantiate();
                propertiesContainer.Add(propRoot);

                nameField     = propRoot.Q<TextField>("NameField");
                positionX     = propRoot.Q<FloatField>("PositionX");
                positionY     = propRoot.Q<FloatField>("PositionY");
                rotationField = propRoot.Q<FloatField>("RotationField");
                scaleX        = propRoot.Q<FloatField>("ScaleX");
                scaleY        = propRoot.Q<FloatField>("ScaleY");
                applyButton   = propRoot.Q<Button>("ApplyButton");
                deleteButton  = propRoot.Q<Button>("DeleteButton");

                // 赋初值
                nameField.value     = obj.DisplayName;
                positionX.value     = obj.Position.x;
                positionY.value     = obj.Position.y;
                rotationField.value = obj.Rotation;
                scaleX.value        = obj.Scale.x;
                scaleY.value        = obj.Scale.y;

                // 绑定事件
                if (applyButton != null) applyButton.clicked += () => ApplyChanges();
                if (deleteButton != null) deleteButton.clicked += () => DeleteSelected();
            }
            else
            {
                // 保留旧的代码式构建作为降级方案

                // 对象名称可编辑
                nameField = new TextField("名称");
                nameField.value = obj.DisplayName;
                nameField.style.width = Length.Percent(100);
                propertiesContainer.Add(nameField);

                // 位置
                var positionGroup = new VisualElement();
                positionGroup.AddToClassList("property-group");
                positionGroup.style.flexDirection = FlexDirection.Row;

                var positionLabel = new Label("位置");
                positionLabel.style.width = 60;
                positionGroup.Add(positionLabel);

                positionX = new FloatField();
                positionX.value = obj.Position.x;
                positionX.style.flexGrow = 1;
                positionGroup.Add(positionX);

                positionY = new FloatField();
                positionY.value = obj.Position.y;
                positionY.style.flexGrow = 1;
                positionGroup.Add(positionY);

                propertiesContainer.Add(positionGroup);

                // 旋转
                var rotationGroup = new VisualElement();
                rotationGroup.AddToClassList("property-group");
                rotationGroup.style.flexDirection = FlexDirection.Row;

                var rotationLabel = new Label("旋转");
                rotationLabel.style.width = 60;
                rotationGroup.Add(rotationLabel);

                rotationField = new FloatField();
                rotationField.value = obj.Rotation;
                rotationField.style.flexGrow = 1;
                rotationGroup.Add(rotationField);

                propertiesContainer.Add(rotationGroup);

                // 缩放
                var scaleGroup = new VisualElement();
                scaleGroup.AddToClassList("property-group");
                scaleGroup.style.flexDirection = FlexDirection.Row;

                var scaleLabel = new Label("缩放");
                scaleLabel.style.width = 60;
                scaleGroup.Add(scaleLabel);

                scaleX = new FloatField();
                scaleX.value = obj.Scale.x;
                scaleX.style.flexGrow = 1;
                scaleGroup.Add(scaleX);

                scaleY = new FloatField();
                scaleY.value = obj.Scale.y;
                scaleY.style.flexGrow = 1;
                scaleGroup.Add(scaleY);

                propertiesContainer.Add(scaleGroup);

                // 按钮组
                var buttonGroup = new VisualElement();
                buttonGroup.AddToClassList("button-group");
                buttonGroup.style.flexDirection = FlexDirection.Row;
                buttonGroup.style.marginTop = 10;

                applyButton = new Button(() => ApplyChanges());
                applyButton.text = "应用";
                applyButton.style.flexGrow = 1;
                buttonGroup.Add(applyButton);

                deleteButton = new Button(() => DeleteSelected());
                deleteButton.text = "删除";
                deleteButton.style.flexGrow = 1;
                deleteButton.AddToClassList("danger-button");
                buttonGroup.Add(deleteButton);

                propertiesContainer.Add(buttonGroup);
            }
        }
        
        private void CreateMultiSelectionUI(List<ISelectable> objects)
        {
            // 更新头部
            nameLabel.text = $"{objects.Count} 个对象";
            selectionInfoLabel.text = "多选对象";
            SetObjectIcon(iconElement, "", true);

            // 显示选中对象列表
            var listContainer = new ScrollView();
            listContainer.style.maxHeight = 200;
            
            foreach (var obj in objects)
            {
                var item = new Label(obj.DisplayName);
                item.AddToClassList("selection-list-item");
                listContainer.Add(item);
            }
            
            propertiesContainer.Add(listContainer);
            
            // 批量操作按钮
            var buttonGroup = new VisualElement();
            buttonGroup.AddToClassList("button-group");
            buttonGroup.style.flexDirection = FlexDirection.Row;
            buttonGroup.style.marginTop = 10;
            
            deleteButton = new Button(() => DeleteSelected());
            deleteButton.text = "删除全部";
            deleteButton.AddToClassList("danger-button");
            buttonGroup.Add(deleteButton);
            
            propertiesContainer.Add(buttonGroup);
        }
        
        private void ApplyChanges()
        {
            if (currentSelection == null || currentSelection.Count != 1) return;
            
            var obj = currentSelection[0];
            
            if (positionX != null && positionY != null)
            {
                obj.Position = new Vector2(positionX.value, positionY.value);
            }
            
            if (rotationField != null)
            {
                obj.Rotation = rotationField.value;
            }
            
            if (scaleX != null && scaleY != null)
            {
                obj.Scale = new Vector2(scaleX.value, scaleY.value);
            }
            
            // 名称
            if (nameField != null)
            {
                // 对象实例支持重命名
                if (obj is SelectableObjectInstance soi)
                {
                    soi.ObjectInstance.DisplayName = nameField.value;
                }
            }
            
            // 通知更新
            if ( currentSelection != null && currentSelection.Count > 0)
            {
                var eventService = MapEditorCore.Instance.EventSystem;
                if (eventService != null)
                {
                    var evt = new SelectionChangedEvent
                    {
                        SourceToolId = "SelectionTool"
                    };
                    
                    // 如果选中的是对象实例
                    if (currentSelection[0] is SelectableObjectInstance)
                    {
                        evt.SelectedObjectInstances = currentSelection
                            .OfType<SelectableObjectInstance>()
                            .Select(s => s.ObjectInstance)
                            .ToList();
                    }
                    
                    eventService.Publish(evt);
                }
            }
        }
        
        private void DeleteSelected()
        {
            if (currentSelection == null || currentSelection.Count == 0) return;
            
            // 发送删除事件
            var eventService = MapEditorCore.Instance.EventSystem;
            eventService?.Publish(new DeleteKeyEvent());
        }

        /// <summary>
        /// 设置对象图标（简化版，与 ObjectToolPanel 中实现保持一致风格）
        /// </summary>
        private void SetObjectIcon(VisualElement iconEl, string prefabGuid, bool isMultiSelect = false)
        {
            if (iconEl == null) return;

            // 目前简化为随机色块或多选占位
            if (isMultiSelect)
            {
                iconEl.style.backgroundColor = new StyleColor(new Color(0.3f, 0.3f, 0.3f));
                AddIconText(iconEl, "多", Color.white);
                return;
            }

            // 尝试加载资源图标
            string iconPath = $"ObjectIcons/{prefabGuid}";
            var tex = Resources.Load<Texture2D>(iconPath);
            if (tex != null)
            {
                iconEl.style.backgroundImage = new StyleBackground(tex);
            }
            else
            {
                // 回退为纯色
                iconEl.style.backgroundColor = new StyleColor(new Color(0.8f, 0.4f, 0.2f));
            }
        }

        private void AddIconText(VisualElement iconEl, string text, Color color)
        {
            iconEl.Clear();
            var label = new Label(text);
            label.style.unityTextAlign = TextAnchor.MiddleCenter;
            label.style.color = new StyleColor(color);
            label.style.fontSize = 18;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.width = Length.Percent(100);
            label.style.height = Length.Percent(100);
            iconEl.Add(label);
        }
    }
} 