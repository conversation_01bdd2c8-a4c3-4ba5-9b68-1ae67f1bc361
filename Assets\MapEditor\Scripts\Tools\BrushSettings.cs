using MapEditor.Data;
using UnityEngine;

namespace MapEditor.Tools
{
    /// <summary>
    /// 运行时笔刷设置。
    /// </summary>
    public struct BrushSettings
    {
        public int materialIndex;       // 地表材质全局索引 (>=0)，本地通道映射由 TilemapChunk 动态分配，最多 7 个通道
        public float size;              // 世界单位大小
        public float strength;          // 0-1
        public BrushShapeSO shape;      // 形状蒙版
        public int blurRadius;          // 边缘模糊半径 (0-3)
        public float hardness;          // 笔刷硬度 (UI值 0-1)
        public float bilateralFactor;   // 保边模糊强度 (1-20)

        public BrushSettings(int materialIdx, float size, float strength, BrushShapeSO shape, int blurRadius = 0, float hardness = 0.5f, float bilateralFactor = 10.0f)
        {
            // 支持任意非负全局索引；Chunk 内部最多 7 个本地通道映射。
            this.materialIndex = Mathf.Max(0, materialIdx);
            this.size = size;
            this.strength = Mathf.Clamp01(strength);
            this.shape = shape;
            this.blurRadius = Mathf.Clamp(blurRadius, 0, 3);
            this.hardness = Mathf.Clamp01(hardness);
            this.bilateralFactor = Mathf.Clamp(bilateralFactor, 1.0f, 20.0f);
        }
    }
} 