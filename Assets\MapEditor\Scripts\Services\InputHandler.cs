using UnityEngine;
using UnityEngine.EventSystems;
using MapEditor.Core;
using MapEditor.Event;


namespace MapEditor.Services
{
    /// <summary>
    /// 输入处理器，负责捕获输入事件并路由到相应的工具
    /// </summary>
    public class InputHandler : ServiceBase,IUpdate
    {
 
        private Camera targetCamera;
        private bool blockToolInputOverUI = true;
        private KeyCode toolCancelKey = KeyCode.Escape;
        
        
        // 输入状态
        private bool isLeftMouseDown = false;
        private bool isRightMouseDown = false;
        private bool isMiddleMouseDown = false;
        private Vector2 lastMousePosition;
        
        /// <summary>
        /// 初始化输入处理器
        /// </summary>
        public override void Initialize()
        {
 

        }

        public override void Start()
        {
            targetCamera = core.SceneRenderer.RenderCamera;
        }
        
        /// <summary>
        /// 每帧更新
        /// </summary>
        public void Update()
        {
            // 确保编辑器核心和相机都有效
            if (core == null || targetCamera == null)
                return;
            
            // 处理鼠标输入
            ProcessMouseInput();
            
            // 处理键盘输入
            ProcessKeyboardInput();
            
            // 更新工具预览
            core.GetService<ToolManager>()?.UpdateToolPreview();
        }
        
        /// <summary>
        /// 处理鼠标输入
        /// </summary>
        private void ProcessMouseInput()
        {
            // 获取当前鼠标位置
            Vector2 mousePosition = Input.mousePosition;
            
            // 检查鼠标是否在UI上
            bool isOverUI = IsPointerOverUI(mousePosition);
            
            // 如果启用了UI阻断且鼠标在UI上，不处理工具输入
            if (blockToolInputOverUI && isOverUI)
            {
                // 同步内部鼠标状态，避免在UI操作后状态不同步导致工具卡住
                isLeftMouseDown = Input.GetMouseButton(0);
                isRightMouseDown = Input.GetMouseButton(1);
                isMiddleMouseDown = Input.GetMouseButton(2);
                lastMousePosition = mousePosition;
                return;
            }
            
            // 创建输入上下文
            InputContext context = new()
            {
                ScreenPosition = mousePosition,
                WorldPosition = targetCamera.ScreenToWorldPoint(mousePosition),
                IsLeftMouseDown = Input.GetMouseButton(0),
                IsRightMouseDown = Input.GetMouseButton(1),
                IsMiddleMouseDown = Input.GetMouseButton(2),
                IsShiftDown = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift),
                IsCtrlDown = Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl),
                IsAltDown = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt),
                ScrollDelta = Input.mouseScrollDelta.y
            };
            
            // 检测鼠标状态变化
            bool leftMouseChanged = isLeftMouseDown != context.IsLeftMouseDown;
            bool rightMouseChanged = isRightMouseDown != context.IsRightMouseDown;
            bool middleMouseChanged = isMiddleMouseDown != context.IsMiddleMouseDown;
            bool mousePositionChanged = lastMousePosition != mousePosition;
            bool scrollChanged = Mathf.Abs(context.ScrollDelta) > 0.01f;
            
            // 如果有输入变化，路由输入到当前工具
            if (leftMouseChanged || rightMouseChanged || middleMouseChanged || 
                mousePositionChanged || scrollChanged)
            {
                // 路由输入到当前工具
                core.GetService<ToolManager>()?.ProcessInput(context);
                
                // 更新状态
                isLeftMouseDown = context.IsLeftMouseDown;
                isRightMouseDown = context.IsRightMouseDown;
                isMiddleMouseDown = context.IsMiddleMouseDown;
                lastMousePosition = mousePosition;
            }
        }
        
        /// <summary>
        /// 处理键盘输入
        /// </summary>
        private void ProcessKeyboardInput()
        {
            bool escapeDown = Input.GetKeyDown(toolCancelKey);
            bool deleteDown = Input.GetKeyDown(KeyCode.Delete);
            
            // 发布对应事件（保持原有行为）
            if (escapeDown)
            {
                PublishEvent(new ToolCancelEvent());
            }
            if (deleteDown)
            {
                PublishEvent(new DeleteKeyEvent());
            }
            
            // 如果本帧检测到 Escape 或 Delete，则将按键信息打包到 InputContext 并发送至当前工具
            if (escapeDown || deleteDown)
            {
                Vector2 mousePosition = Input.mousePosition;
                InputContext context = new()
                {
                    ScreenPosition = mousePosition,
                    WorldPosition = targetCamera.ScreenToWorldPoint(mousePosition),
                    IsLeftMouseDown = Input.GetMouseButton(0),
                    IsRightMouseDown = Input.GetMouseButton(1),
                    IsMiddleMouseDown = Input.GetMouseButton(2),
                    IsShiftDown = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift),
                    IsCtrlDown = Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl),
                    IsAltDown = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt),
                    ScrollDelta = Input.mouseScrollDelta.y,
                    IsEscapeDown = escapeDown,
                    IsDeleteDown = deleteDown
                };
                
                core.GetService<ToolManager>()?.ProcessInput(context);
            }
            
            // 工具快捷键（数字键1-9）
            for (int i = 0; i < 9; i++)
            {
                if (Input.GetKeyDown(KeyCode.Alpha1 + i) || Input.GetKeyDown(KeyCode.Keypad1 + i))
                {
                    PublishEvent(new ToolShortcutEvent { ShortcutIndex = i });
                }
            }

            // 画笔大小快捷键 [ / ]
            if (Input.GetKeyDown(KeyCode.LeftBracket))
            {
                PublishEvent(new BrushSizeShortcutEvent { Direction = -1 });
            }
            else if (Input.GetKeyDown(KeyCode.RightBracket))
            {
                PublishEvent(new BrushSizeShortcutEvent { Direction = 1 });
            }
        }
        
        /// <summary>
        /// 检查鼠标指针是否在UI元素上
        /// </summary>
        private bool IsPointerOverUI(Vector2 mousePosition)
        {
            // 统一依赖 Unity 的 EventSystem 判断，无需显式依赖 UIManager 或 UI Toolkit 细节
            return EventSystem.current != null && EventSystem.current.IsPointerOverGameObject();
        }
    }
 
} 