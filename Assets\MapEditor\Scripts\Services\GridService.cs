using UnityEngine;
using MapEditor.Core;
using MapEditor.Config;
using MapEditor.Event;
using System.Collections.Generic;

namespace MapEditor.Services
{
    /// <summary>
    /// 网格服务：负责根据地图尺寸创建/更新网格渲染器，并提供对外配置接口。
    /// </summary>
    public class GridService:ServiceBase
    {
        private  ISceneRenderer _sceneRenderer;

        private IGridRenderer _gridRenderer;

        public override void Initialize()
        {
            _sceneRenderer = core.SceneRenderer;
        }

        public override void Start()
        {
            RegisterEventHandlers();
        }

        /// <summary>
        /// 更新网格显示设置。
        /// </summary>
        public void UpdateGridSettings(int? gridInterval = null, Color? gridColor = null, Color? majorGridColor = null, float? lineWidth = null, float? majorLineWidth = null)
        {
            if (_gridRenderer == null) return;

            if (gridInterval.HasValue) 
            {
                // 根据网格间隔重新计算cellSize和majorInterval
                ApplyGridInterval(gridInterval.Value);
            }
            if (gridColor.HasValue) _gridRenderer.GridColor = gridColor.Value;
            if (majorGridColor.HasValue) _gridRenderer.MajorGridColor = majorGridColor.Value;
            if (lineWidth.HasValue) _gridRenderer.LineWidth = lineWidth.Value;
            if (majorLineWidth.HasValue) _gridRenderer.MajorLineWidth = majorLineWidth.Value;

            _gridRenderer.RebuildGrid();
        }

        /// <summary>
        /// 获取当前网格设置
        /// </summary>
        public (int gridInterval, Color gridColor, Color majorGridColor, float lineWidth, float majorLineWidth) GetCurrentGridSettings()
        {
            if (_gridRenderer == null)
            {
                return (16, new Color(0.6f, 0.6f, 0.6f, 0.6f), new Color(0.9f, 0.9f, 0.9f, 0.8f), 0.05f, 0.1f);
            }

            // 从当前CellSize反推网格间隔（以像素为单位）
            int currentGridInterval = Mathf.RoundToInt(_gridRenderer.CellSize.x * MapEditorConfig.PixelsPerUnit);
            return (currentGridInterval, _gridRenderer.GridColor, _gridRenderer.MajorGridColor, _gridRenderer.LineWidth, _gridRenderer.MajorLineWidth);
        }

        /// <summary>
        /// 应用网格间隔设置
        /// </summary>
        /// <param name="gridIntervalPixels">网格间隔（像素）</param>
        private void ApplyGridInterval(int gridIntervalPixels)
        {
            if (_gridRenderer == null) return;

            // 将像素间隔转换为世界单位
            float cellWorld = gridIntervalPixels / (float)MapEditorConfig.PixelsPerUnit;
            
            // 计算主网格间隔（让主网格与chunk边界对齐）
            var mapData = core.MapDataStore.CurrentMap as MapEditor.Core.MapData;
            int majorInterval = 5; // 默认值
            if (mapData != null)
            {
                majorInterval = Mathf.Max(1, mapData.ChunkSize / gridIntervalPixels);
            }

            _gridRenderer.CellSize = new Vector2(cellWorld, cellWorld);
            _gridRenderer.MajorGridInterval = majorInterval;

            // 更新MapData中的GridCellSize
            if (mapData != null)
            {
                mapData.GridCellSize = new Vector2(cellWorld, cellWorld);
                // 通过强制转换访问具体实现类的MarkMapChanged方法
                if (core.MapDataStore is MapEditor.Data.MapDataStore mapDataStore)
                {
                    mapDataStore.MarkMapChanged();
                }
            }
        }

        /// <summary>
        /// 释放资源并取消事件订阅。
        /// </summary>
        public void Dispose()
        {
            UnregisterEvent<MapCreatedEvent>(OnMapCreated);
            UnregisterEvent<MapLoadedEvent>(OnMapLoaded);
        }

        #region 私有实现

        private void RegisterEventHandlers()
        {
            RegisterEvent<MapCreatedEvent>(OnMapCreated);
            RegisterEvent<MapLoadedEvent>(OnMapLoaded);
        }

        private void OnMapCreated(MapCreatedEvent evt)
        {
            if (evt.MapData != null)
            {
                CreateOrUpdateGrid(evt.MapData.MapSize);

                if (evt.MapData is MapEditor.Core.MapData md)
                {
                    ApplyChunkSizeToGrid(md.ChunkSize);
                }
            }
        }

        private void OnMapLoaded(MapLoadedEvent evt)
        {
            if (evt.MapData != null)
            {
                CreateOrUpdateGrid(evt.MapData.MapSize);

                if (evt.MapData is MapEditor.Core.MapData md)
                {
                    // 优先使用保存的GridCellSize，如果不存在则使用ChunkSize计算
                    if (md.GridCellSize != Vector2.zero)
                    {
                        UpdateGridSettings(gridInterval: Mathf.RoundToInt(md.GridCellSize.x * MapEditorConfig.PixelsPerUnit));
                    }
                    else
                    {
                        ApplyChunkSizeToGrid(md.ChunkSize);
                    }
                }
            }
        }

        /// <summary>
        /// 创建或更新网格渲染器，使其范围与地图大小保持一致。
        /// </summary>
        private void CreateOrUpdateGrid(Vector2Int mapSizePixels)
        {
            if (_sceneRenderer == null) return;

            // 将像素尺寸转换为世界单位，并向上取整到整数格，以避免出现缺少边界的问题
            int worldWidthUnits = Mathf.CeilToInt(mapSizePixels.x / (float)MapEditorConfig.PixelsPerUnit);
            int worldHeightUnits = Mathf.CeilToInt(mapSizePixels.y / (float)MapEditorConfig.PixelsPerUnit);
            float worldWidth = worldWidthUnits;
            float worldHeight = worldHeightUnits;
            Rect bounds = new Rect(0, 0, worldWidth, worldHeight);

            if (_gridRenderer != null)
            {
                _gridRenderer.SetGridBounds(bounds);

                var existingMr = (_gridRenderer as MonoBehaviour)?.GetComponent<MeshRenderer>();
                            if (existingMr != null && existingMr.sortingOrder < SortingOrderConfig.GridFixedOrder)
            {
                existingMr.sortingOrder = SortingOrderConfig.GridFixedOrder;
            }

                _gridRenderer.RebuildGrid();

                // 不在此处冻结，留给调用者在更新完参数后决定

                // 关闭 Update，提高性能
                var mbExisting = _gridRenderer as MonoBehaviour;
                if (mbExisting != null)
                {
                    mbExisting.enabled = false;
                }
                return;
            }

            // 创建新的网格渲染器
            _gridRenderer = _sceneRenderer.CreateGridRenderer(GridType.Square);
            _gridRenderer.CellSize = Vector2.one; // 默认 1 单位 = 1 格
            _gridRenderer.MajorGridInterval = 5;

            // 保持 SquareGridRenderer 的动态更新，以便相机移动或缩放时自动重建网格。
    
            // 增强默认网格颜色可见度
            _gridRenderer.GridColor = new Color(0.6f, 0.6f, 0.6f, 0.6f);          // 一般线条
            _gridRenderer.MajorGridColor = new Color(0.9f, 0.9f, 0.9f, 0.8f);     // 主要线条

            _gridRenderer.SetGridBounds(bounds);

            var mr = (_gridRenderer as MonoBehaviour)?.GetComponent<MeshRenderer>();
            if (mr != null)
            {
                mr.sortingOrder = SortingOrderConfig.GridFixedOrder;
            }

            // 初始完成后再重建一次，确保使用最新边界
            _gridRenderer.RebuildGrid();

            // 注册网格渲染器到SceneRenderer，使可见性控制生效
            _sceneRenderer.RegisterRenderable(_gridRenderer);

            var mb = _gridRenderer as MonoBehaviour;
            if (mb != null)
            {
                mb.enabled = false;
            }
        }

        /// <summary>
        /// 根据 ChunkSize 计算网格的 cellSize 与 majorInterval，确保主网格线正好落在 Chunk 边界。
        /// 逻辑：
        /// 1. 先使用 16 像素作为期望的最小网格间隔，如果 ChunkSize 能被 16 整除，则保持现有策略。
        /// 2. 若不能整除，则退化为 1 像素网格，并将 MajorInterval 直接设置为 ChunkSize，保证精确对齐。
        /// </summary>
        /// <param name="chunkSize">地图 Chunk 的边长(像素)</param>
        private void ApplyChunkSizeToGrid(int chunkSize)
        {
            const int preferredMinorPx = 16;

            int minorPx = (chunkSize % preferredMinorPx == 0) ? preferredMinorPx : 1;

            float cellWorld = minorPx / (float)MapEditorConfig.PixelsPerUnit;
            int majorInterval = Mathf.Max(1, chunkSize / minorPx);

            UpdateGridSettings(gridInterval: minorPx);
        }

        #endregion

        #region 网格坐标转换和占用计算

        /// <summary>
        /// 将世界坐标转换为网格坐标
        /// </summary>
        /// <param name="worldPosition">世界坐标</param>
        /// <returns>网格坐标</returns>
        public Vector2Int WorldToGridCoord(Vector2 worldPosition)
        {
            if (_gridRenderer == null)
            {
                Debug.LogWarning("GridService: 网格渲染器未初始化，使用默认网格尺寸");
                return new Vector2Int(
                    Mathf.FloorToInt(worldPosition.x),
                    Mathf.FloorToInt(worldPosition.y)
                );
            }

            Vector2 cellSize = _gridRenderer.CellSize;
            return new Vector2Int(
                Mathf.FloorToInt(worldPosition.x / cellSize.x),
                Mathf.FloorToInt(worldPosition.y / cellSize.y)
            );
        }

        /// <summary>
        /// 将网格坐标转换为世界坐标（格子中心点）
        /// </summary>
        /// <param name="gridCoord">网格坐标</param>
        /// <returns>世界坐标</returns>
        public Vector2 GridCoordToWorld(Vector2Int gridCoord)
        {
            if (_gridRenderer == null)
            {
                Debug.LogWarning("GridService: 网格渲染器未初始化，使用默认网格尺寸");
                return new Vector2(gridCoord.x + 0.5f, gridCoord.y + 0.5f);
            }

            Vector2 cellSize = _gridRenderer.CellSize;
            return new Vector2(
                (gridCoord.x + 0.5f) * cellSize.x,
                (gridCoord.y + 0.5f) * cellSize.y
            );
        }

        /// <summary>
        /// 根据世界坐标包围盒计算占用的网格格子
        /// </summary>
        /// <param name="worldBounds">世界坐标包围盒</param>
        /// <returns>占用的网格格子坐标列表</returns>
        public List<Vector2Int> GetOccupiedCells(Bounds worldBounds)
        {
            var occupiedCells = new List<Vector2Int>();

            if (_gridRenderer == null)
            {
                Debug.LogWarning("GridService: 网格渲染器未初始化");
                return occupiedCells;
            }

            Vector2 cellSize = _gridRenderer.CellSize;

            // 计算包围盒在网格中的范围
            int minX = Mathf.FloorToInt(worldBounds.min.x / cellSize.x);
            int maxX = Mathf.FloorToInt(worldBounds.max.x / cellSize.x);
            int minY = Mathf.FloorToInt(worldBounds.min.y / cellSize.y);
            int maxY = Mathf.FloorToInt(worldBounds.max.y / cellSize.y);

            // 生成所有占用的格子坐标
            for (int x = minX; x <= maxX; x++)
            {
                for (int y = minY; y <= maxY; y++)
                {
                    occupiedCells.Add(new Vector2Int(x, y));
                }
            }

            return occupiedCells;
        }

        /// <summary>
        /// 根据碰撞体计算占用的网格格子
        /// </summary>
        /// <param name="collider">碰撞体</param>
        /// <returns>占用的网格格子坐标列表</returns>
        public List<Vector2Int> GetOccupiedCells(Collider2D collider)
        {
            if (collider == null)
            {
                Debug.LogWarning("GridService: 传入的碰撞体为空");
                return new List<Vector2Int>();
            }

            return GetOccupiedCells(collider.bounds);
        }

        /// <summary>
        /// 根据碰撞体精确计算占用的网格格子（考虑碰撞体的具体形状）
        /// </summary>
        /// <param name="collider">碰撞体</param>
        /// <returns>占用的网格格子坐标列表</returns>
        public List<Vector2Int> GetOccupiedCellsAccurate(Collider2D collider)
        {
            var occupiedCells = new List<Vector2Int>();

            if (collider == null || _gridRenderer == null)
            {
                Debug.LogWarning("GridService: 碰撞体或网格渲染器为空");
                return occupiedCells;
            }

            Vector2 cellSize = _gridRenderer.CellSize;
            Bounds bounds = collider.bounds;

            // 计算包围盒在网格中的范围
            int minX = Mathf.FloorToInt(bounds.min.x / cellSize.x);
            int maxX = Mathf.FloorToInt(bounds.max.x / cellSize.x);
            int minY = Mathf.FloorToInt(bounds.min.y / cellSize.y);
            int maxY = Mathf.FloorToInt(bounds.max.y / cellSize.y);

            // 遍历每个格子，检查格子中心点是否在碰撞体内
            for (int x = minX; x <= maxX; x++)
            {
                for (int y = minY; y <= maxY; y++)
                {
                    Vector2 cellCenter = new Vector2(
                        (x + 0.5f) * cellSize.x,
                        (y + 0.5f) * cellSize.y
                    );

                    // 检查格子中心点是否在碰撞体内
                    if (collider.OverlapPoint(cellCenter))
                    {
                        occupiedCells.Add(new Vector2Int(x, y));
                    }
                }
            }

            return occupiedCells;
        }

        /// <summary>
        /// 获取当前网格的单元格尺寸
        /// </summary>
        /// <returns>网格单元格尺寸</returns>
        public Vector2 GetCellSize()
        {
            if (_gridRenderer == null)
            {
                Debug.LogWarning("GridService: 网格渲染器未初始化，返回默认尺寸");
                return Vector2.one;
            }

            return _gridRenderer.CellSize;
        }

        #endregion
    }
} 