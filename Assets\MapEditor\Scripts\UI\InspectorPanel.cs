using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// 属性检查器面板，显示选中对象的属性
    /// </summary>
    public class InspectorPanel : UIPanel
    {
        public override string PanelId => "inspector";
        public override string DisplayName => "属性检查器";
        
        private VisualElement propertiesContainer;
        private Label titleLabel;
        private Label selectionInfoLabel;
        
        public InspectorPanel(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
        }
        
        public override void Initialize()
        {
            propertiesContainer = FindElement<VisualElement>("PropertiesContainer");
            titleLabel = FindElement<Label>("TitleLabel");
            selectionInfoLabel = FindElement<Label>("SelectionInfoLabel");
            
            if (propertiesContainer == null)
            {
                Debug.LogError("PropertiesContainer not found in inspector template");
                return;
            }
            
            // 注册选择变更事件
            MapEditorCore.Instance.EventSystem?.Subscribe<SelectionChangedEvent>(OnSelectionChanged);
            
            // 初始状态
            UpdateSelectionInfo(null);
        }
        
        public override void UpdatePanel()
        {
            // 子类可能需要更新属性值显示
        }
        
        private void OnSelectionChanged(SelectionChangedEvent evt)
        {
            UpdateSelectionInfo(evt);
        }
        
        private void UpdateSelectionInfo(SelectionChangedEvent evt)
        {
            // 清除当前属性UI
            propertiesContainer.Clear();
            
            if (evt == null || evt.SelectedObjects == null || evt.SelectedObjects.Count == 0)
            {
                // 无选择
                titleLabel.text = "未选择对象";
                selectionInfoLabel.text = "在场景中选择对象以查看属性";
                return;
            }
            
            // 更新选择信息
            int count = evt.SelectedObjects.Count;
            titleLabel.text = count == 1 ? "对象属性" : $"已选择 {count} 个对象";
            selectionInfoLabel.text = count == 1 ? evt.SelectedObjects[0].name : $"多选: {count} 个对象";
            
            if (count == 1)
            {
                // 单选：显示详细属性
                GameObject selectedObject = evt.SelectedObjects[0];
                CreatePropertyFields(selectedObject);
            }
            else
            {
                // 多选：显示公共属性
                CreateCommonPropertyFields(evt.SelectedObjects);
            }
        }
        
        private void CreatePropertyFields(GameObject selectedObject)
        {
            // 创建通用属性字段
            CreateTextField("名称", selectedObject.name, (newValue) => selectedObject.name = newValue);
            
            // 创建位置属性字段
            Vector3Field("位置", selectedObject.transform.position, (newValue) => selectedObject.transform.position = newValue);
            
            // 创建旋转属性字段
            Vector3Field("旋转", selectedObject.transform.eulerAngles, (newValue) => selectedObject.transform.eulerAngles = newValue);
            
            // 创建缩放属性字段
            Vector3Field("缩放", selectedObject.transform.localScale, (newValue) => selectedObject.transform.localScale = newValue);
            
            // 可以添加更多特定组件的属性...
        }
        
        private void CreateCommonPropertyFields(System.Collections.Generic.List<GameObject> selectedObjects)
        {
            // 创建多选时的公共属性
            // 例如：批量修改位置、旋转等
        }
        
        // UI元素创建辅助方法
        
        private void CreateTextField(string label, string value, System.Action<string> onChange)
        {
            var container = new VisualElement();
            container.AddToClassList("property-field");
            
            var labelElement = new Label(label);
            labelElement.AddToClassList("property-label");

            var field = new TextField
            {
                value = value
            };
            field.AddToClassList("property-input");
            field.RegisterValueChangedCallback(evt => onChange?.Invoke(evt.newValue));
            
            container.Add(labelElement);
            container.Add(field);
            propertiesContainer.Add(container);
        }
        
        private void Vector3Field(string label, Vector3 value, System.Action<Vector3> onChange)
        {
            var container = new VisualElement();
            container.AddToClassList("property-field");
            
            var labelElement = new Label(label);
            labelElement.AddToClassList("property-label");
            container.Add(labelElement);
            
            var fieldsContainer = new VisualElement();
            fieldsContainer.AddToClassList("vector3-container");
            
            // X, Y, Z fields
            var xContainer = new VisualElement();
            xContainer.AddToClassList("vector-component");
            var xLabel = new Label("X");
            var xField = new FloatField { value = value.x };
            xField.RegisterValueChangedCallback(evt => 
            {
                var newVec = value;
                newVec.x = evt.newValue;
                onChange?.Invoke(newVec);
                value = newVec; // 更新本地值
            });
            xContainer.Add(xLabel);
            xContainer.Add(xField);
            
            var yContainer = new VisualElement();
            yContainer.AddToClassList("vector-component");
            var yLabel = new Label("Y");
            var yField = new FloatField { value = value.y };
            yField.RegisterValueChangedCallback(evt => 
            {
                var newVec = value;
                newVec.y = evt.newValue;
                onChange?.Invoke(newVec);
                value = newVec;
            });
            yContainer.Add(yLabel);
            yContainer.Add(yField);
            
            var zContainer = new VisualElement();
            zContainer.AddToClassList("vector-component");
            var zLabel = new Label("Z");
            var zField = new FloatField { value = value.z };
            zField.RegisterValueChangedCallback(evt => 
            {
                var newVec = value;
                newVec.z = evt.newValue;
                onChange?.Invoke(newVec);
                value = newVec;
            });
            zContainer.Add(zLabel);
            zContainer.Add(zField);
            
            fieldsContainer.Add(xContainer);
            fieldsContainer.Add(yContainer);
            fieldsContainer.Add(zContainer);
            
            container.Add(fieldsContainer);
            propertiesContainer.Add(container);
        }
    }
} 