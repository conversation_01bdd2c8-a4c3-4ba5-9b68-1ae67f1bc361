<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" noNamespaceSchemaLocation="UnityEngine.UIElements" editor-extension-mode="False">
    <ui:VisualElement class="brush-settings-panel" style="flex-direction: column; padding: 6px; flex-basis: 100%; align-self: flex-start;">
        <ui:Label text="画笔设置" class="panel-title" style="unity-font-style: bold; margin-bottom: 6px;" />
        <ui:VisualElement style="flex-direction: row; align-items: center; margin-bottom: 4px;">
            <ui:Label text="大小" style="width: 60px;" />
            <ui:Slider name="SizeSlider" low-value="0.5" high-value="10" style="flex-grow: 1; flex-direction: column;" />
        </ui:VisualElement>
        <ui:VisualElement style="flex-direction: row; align-items: center; margin-bottom: 4px;">
            <ui:Label text="强度" style="width: 60px;" />
            <ui:Slider name="StrengthSlider" low-value="0" high-value="1" style="flex-grow: 1; flex-direction: column;" />
            <ui:FloatField name="StrengthInput" value="1" style="width: 43px; margin-left: 4px;" />
        </ui:VisualElement>
        <ui:VisualElement style="flex-direction: row; align-items: center; margin-bottom: 4px;">
            <ui:Label text="边缘模糊" style="width: 60px;" />
            <ui:SliderInt name="BlurRadiusSlider" low-value="0" high-value="3" style="flex-grow: 1; flex-direction: column;" />
            <ui:IntegerField name="BlurRadiusInput" value="0" style="width: 43px; margin-left: 4px;" />
        </ui:VisualElement>
        <ui:VisualElement style="flex-direction: row; align-items: center; margin-bottom: 4px;">
            <ui:Label text="笔刷硬度" style="width: 60px;" />
            <ui:Slider name="HardnessSlider" low-value="0.1" high-value="5" style="flex-grow: 1; flex-direction: column;" />
            <ui:FloatField name="HardnessInput" value="1" style="width: 43px; margin-left: 4px;" />
        </ui:VisualElement>
        <ui:VisualElement style="flex-direction: row; align-items: center; margin-bottom: 4px;">
            <ui:Label text="保边强度" style="width: 60px;" />
            <ui:Slider name="BilateralFactorSlider" low-value="1" high-value="20" style="flex-grow: 1; flex-direction: column;" />
            <ui:FloatField name="BilateralFactorInput" value="10" style="width: 43px; margin-left: 4px;" />
        </ui:VisualElement>
        <!-- 笔刷形状选择 -->
        <ui:Label text="笔刷样式:" style="margin-top: 6px; margin-bottom: 2px;" />
        <ui:VisualElement name="ShapeGrid" class="icon-grid" style="flex-direction: row; flex-wrap: wrap; margin-bottom: 8px;" />

        <!-- 纹理选择 -->
        <ui:Label text="纹理:" style="margin-top: 6px; margin-bottom: 2px;" />
        <ui:VisualElement name="MaterialGrid" class="icon-grid" style="flex-direction: row; flex-wrap: wrap; margin-bottom: 8px;" />
    </ui:VisualElement>
</ui:UXML>
