using System;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 所有数据 Chunk 的抽象基类。
    /// 除数据字段外仅负责脏标记与版本控制。
    /// </summary>
    [Serializable]
    public abstract class ChunkBase
    {
        [SerializeField] private ChunkCoord coord;
        private bool isDirty = true;
        [SerializeField] private uint version;

        public ChunkCoord Coord => coord;
        public bool IsDirty => isDirty;
        public uint Version => version;

        protected ChunkBase(ChunkCoord coord)
        {
            this.coord = coord;
            isDirty = true;
            version = 0;
        }

        /// <summary>
        /// 将 Chunk 标记为脏并递增版本，用于增量渲染。
        /// </summary>
        public void MarkDirty()
        {
            isDirty = true;
            version++;
        }

        /// <summary>
        /// 标记为已清理（渲染器已同步）。
        /// </summary>
        public void ClearDirtyFlag()
        {
            isDirty = false;
        }
    }
} 