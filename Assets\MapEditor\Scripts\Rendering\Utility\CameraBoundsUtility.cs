using MapEditor.Core;
using UnityEngine;
using UnityEngine.Experimental.Rendering.Universal;


namespace MapEditor.Rendering
{
    /// <summary>
    /// 相机边界计算工具类，支持 Pixel Perfect Camera
    /// </summary>
    public static class CameraBoundsUtility
    {
        /// <summary>
        /// 计算相机的视锥体边界，支持像素完美相机
        /// </summary>
        /// <param name="cam">目标相机</param>
        /// <param name="preloadRing">预加载环形区域大小</param>
        /// <returns>相机视锥体边界</returns>
        public static Bounds Compute(Camera cam, float preloadRing)
        {
            if (cam == null) return default;

            var frustumHeight = 2f * cam.orthographicSize;
            var frustumWidth = frustumHeight * cam.aspect;
            var center = cam.transform.position;

            // 检查是否有像素完美相机，如果有则考虑像素对齐
            var pixelPerfectCamera = cam.GetComponent<PixelPerfectCamera>();

            // 若启用了 Pixel Snapping，需要对 frustum 大小和中心进行像素对齐
            if (pixelPerfectCamera != null && pixelPerfectCamera.gridSnapping == PixelPerfectCamera.GridSnapping.PixelSnapping)
            {
                // 对于像素完美相机，确保边界对齐到像素网格
                int ppu = pixelPerfectCamera.assetsPPU;
                if (ppu > 0)
                {
                    float pixelSize = 1f / ppu;
                    
                    // 将边界对齐到像素网格
                    frustumWidth = Mathf.Ceil(frustumWidth / pixelSize) * pixelSize;
                    frustumHeight = Mathf.Ceil(frustumHeight / pixelSize) * pixelSize;
                    
                    // 对齐中心点
                    float alignedCenterX = Mathf.Round(center.x / pixelSize) * pixelSize;
                    float alignedCenterY = Mathf.Round(center.y / pixelSize) * pixelSize;
                    center = new Vector3(alignedCenterX, alignedCenterY, center.z);
                }
            }

            // 扩展 preloadRing 区域
            var size = new Vector3(frustumWidth + preloadRing * 2f, frustumHeight + preloadRing * 2f, 1f);
            return new Bounds(center, size);
        }
        
        /// <summary>
        /// 计算像素完美对齐的边界
        /// </summary>
        /// <param name="bounds">原始边界</param>
        /// <param name="pixelsPerUnit">像素每单位数</param>
        /// <returns>对齐后的边界</returns>
        public static Bounds AlignToPixelGrid(Bounds bounds, int pixelsPerUnit)
        {
            if (pixelsPerUnit <= 0) return bounds;
            
            float pixelSize = 1f / pixelsPerUnit;
            
            // 对齐中心点
            Vector3 alignedCenter = new Vector3(
                Mathf.Round(bounds.center.x / pixelSize) * pixelSize,
                Mathf.Round(bounds.center.y / pixelSize) * pixelSize,
                bounds.center.z
            );
            
            // 对齐尺寸
            Vector3 alignedSize = new Vector3(
                Mathf.Ceil(bounds.size.x / pixelSize) * pixelSize,
                Mathf.Ceil(bounds.size.y / pixelSize) * pixelSize,
                bounds.size.z
            );
            
            return new Bounds(alignedCenter, alignedSize);
        }
    }
} 