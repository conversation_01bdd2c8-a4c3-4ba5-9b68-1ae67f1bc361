using UnityEngine;
using System.Collections.Generic;

namespace MapEditor.Tools
{
    /// <summary>
    /// 距离场生成器，负责计算像素点到曲线的距离场
    /// 用于生成平滑的边缘过渡效果
    /// </summary>
    public static class DistanceFieldGenerator
    {
        /// <summary>
        /// 距离场参数
        /// </summary>
        public struct DistanceFieldParams
        {
            public float strokeWidth;       // 描边宽度
            public float edgeSoftness;      // 边缘柔和度 (0-1)
            public bool enableAntiAliasing; // 是否启用抗锯齿
            public float supersampleScale;  // 超采样比例
            
            public DistanceFieldParams(float strokeWidth, float edgeSoftness = 0.5f, 
                bool enableAntiAliasing = true, float supersampleScale = 2f)
            {
                this.strokeWidth = strokeWidth;
                this.edgeSoftness = Mathf.Clamp01(edgeSoftness);
                this.enableAntiAliasing = enableAntiAliasing;
                this.supersampleScale = Mathf.Max(1f, supersampleScale);
            }
        }
        
        /// <summary>
        /// 计算点到曲线描边的距离权重
        /// </summary>
        /// <param name="worldPosition">世界坐标点</param>
        /// <param name="curveSegments">曲线段列表</param>
        /// <param name="parameters">距离场参数</param>
        /// <returns>权重值 (0-1)</returns>
        public static float CalculateStrokeWeight(Vector2 worldPosition, IReadOnlyList<CurveSegment> curveSegments, 
            DistanceFieldParams parameters)
        {
            if (curveSegments.Count == 0)
            {
                return 0f;
            }
            
            float distance = CalculateDistanceToStroke(worldPosition, curveSegments, parameters.strokeWidth);
            return DistanceToWeight(distance, parameters.strokeWidth, parameters.edgeSoftness);
        }
        
        /// <summary>
        /// 计算点到曲线描边的距离
        /// </summary>
        /// <param name="point">测试点</param>
        /// <param name="curveSegments">曲线段列表</param>
        /// <param name="strokeWidth">描边宽度</param>
        /// <returns>到描边边缘的距离（负值表示在描边内部）</returns>
        public static float CalculateDistanceToStroke(Vector2 point, IReadOnlyList<CurveSegment> curveSegments, float strokeWidth)
        {
            if (curveSegments.Count == 0)
            {
                return float.MaxValue;
            }
            
            float minDistance = float.MaxValue;
            float halfWidth = strokeWidth * 0.5f;
            
            // 计算到曲线中心线的最短距离
            for (int i = 0; i < curveSegments.Count - 1; i++)
            {
                Vector2 segmentStart = curveSegments[i].position;
                Vector2 segmentEnd = curveSegments[i + 1].position;
                
                float distance = DistancePointToLineSegment(point, segmentStart, segmentEnd);
                minDistance = Mathf.Min(minDistance, distance);
            }
            
            // 转换为到描边边缘的距离
            return minDistance - halfWidth;
        }
        
        /// <summary>
        /// 计算点到曲线描边的距离（精确版本，考虑曲线的变宽度）
        /// </summary>
        public static float CalculateDistanceToStrokeAccurate(Vector2 point, IReadOnlyList<CurveSegment> curveSegments, 
            DistanceFieldParams parameters)
        {
            if (curveSegments.Count == 0)
            {
                return float.MaxValue;
            }
            
            float minSignedDistance = float.MaxValue;
            
            // 对每个曲线段计算精确距离
            for (int i = 0; i < curveSegments.Count - 1; i++)
            {
                var segment1 = curveSegments[i];
                var segment2 = curveSegments[i + 1];
                
                float distance = CalculateDistanceToSegment(point, segment1, segment2, parameters.strokeWidth);
                
                if (Mathf.Abs(distance) < Mathf.Abs(minSignedDistance))
                {
                    minSignedDistance = distance;
                }
            }
            
            return minSignedDistance;
        }
        
        /// <summary>
        /// 计算点到单个曲线段的距离
        /// </summary>
        private static float CalculateDistanceToSegment(Vector2 point, CurveSegment segment1, CurveSegment segment2, float strokeWidth)
        {
            Vector2 segmentStart = segment1.position;
            Vector2 segmentEnd = segment2.position;
            Vector2 segmentDir = (segmentEnd - segmentStart).normalized;
            
            // 计算点在线段上的投影
            Vector2 toPoint = point - segmentStart;
            float projection = Vector2.Dot(toPoint, segmentDir);
            float segmentLength = Vector2.Distance(segmentStart, segmentEnd);
            
            // 限制投影在线段范围内
            projection = Mathf.Clamp(projection, 0f, segmentLength);
            
            // 计算最近点
            Vector2 closestPoint = segmentStart + segmentDir * projection;
            
            // 计算该点的描边宽度（如果支持变宽度）
            float t = segmentLength > 0 ? projection / segmentLength : 0f;
            float localWidth = Mathf.Lerp(segment1.width, segment2.width, t) * strokeWidth;
            float halfWidth = localWidth * 0.5f;
            
            // 计算到中心线的距离
            float distanceToCenter = Vector2.Distance(point, closestPoint);
            
            // 返回到描边边缘的有符号距离
            return distanceToCenter - halfWidth;
        }
        
        /// <summary>
        /// 将距离转换为权重
        /// </summary>
        /// <param name="distance">到描边边缘的距离</param>
        /// <param name="strokeWidth">描边宽度</param>
        /// <param name="edgeSoftness">边缘柔和度</param>
        /// <returns>权重值 (0-1)</returns>
        public static float DistanceToWeight(float distance, float strokeWidth, float edgeSoftness)
        {
            if (distance <= 0f)
            {
                // 在描边内部
                return 1f;
            }
            
            // 计算柔和边缘的宽度
            float softEdgeWidth = strokeWidth * edgeSoftness * 0.5f;
            
            if (softEdgeWidth <= 0f)
            {
                // 硬边缘
                return 0f;
            }
            
            // 使用平滑步函数创建柔和过渡
            float normalizedDistance = distance / softEdgeWidth;
            return 1f - Mathf.SmoothStep(0f, 1f, normalizedDistance);
        }
        
        /// <summary>
        /// 生成曲线描边的距离场纹理
        /// </summary>
        /// <param name="curveSegments">曲线段列表</param>
        /// <param name="parameters">距离场参数</param>
        /// <param name="bounds">生成区域</param>
        /// <param name="resolution">纹理分辨率</param>
        /// <returns>距离场纹理</returns>
        public static Texture2D GenerateDistanceFieldTexture(IReadOnlyList<CurveSegment> curveSegments, 
            DistanceFieldParams parameters, Rect bounds, int resolution)
        {
            var texture = new Texture2D(resolution, resolution, TextureFormat.R8, false);
            texture.filterMode = FilterMode.Bilinear;
            texture.wrapMode = TextureWrapMode.Clamp;
            
            var pixels = new Color[resolution * resolution];
            
            float pixelSize = Mathf.Max(bounds.width, bounds.height) / resolution;
            Vector2 boundsMin = bounds.min;
            
            // 超采样抗锯齿
            int sampleCount = parameters.enableAntiAliasing ? 
                Mathf.RoundToInt(parameters.supersampleScale * parameters.supersampleScale) : 1;
            float sampleOffset = 1f / parameters.supersampleScale;
            
            for (int y = 0; y < resolution; y++)
            {
                for (int x = 0; x < resolution; x++)
                {
                    float totalWeight = 0f;
                    
                    if (sampleCount == 1)
                    {
                        // 单采样
                        Vector2 worldPos = boundsMin + new Vector2(x + 0.5f, y + 0.5f) * pixelSize;
                        totalWeight = CalculateStrokeWeight(worldPos, curveSegments, parameters);
                    }
                    else
                    {
                        // 超采样抗锯齿
                        for (int sy = 0; sy < parameters.supersampleScale; sy++)
                        {
                            for (int sx = 0; sx < parameters.supersampleScale; sx++)
                            {
                                Vector2 sampleOffset2D = new Vector2(sx + 0.5f, sy + 0.5f) * sampleOffset;
                                Vector2 worldPos = boundsMin + new Vector2(x + sampleOffset2D.x, y + sampleOffset2D.y) * pixelSize;
                                totalWeight += CalculateStrokeWeight(worldPos, curveSegments, parameters);
                            }
                        }
                        totalWeight /= sampleCount;
                    }
                    
                    // 转换为颜色值
                    float intensity = Mathf.Clamp01(totalWeight);
                    pixels[y * resolution + x] = new Color(intensity, intensity, intensity, intensity);
                }
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        /// <summary>
        /// 生成基于GPU的距离场数据
        /// </summary>
        /// <param name="curveSegments">曲线段列表</param>
        /// <param name="parameters">距离场参数</param>
        /// <param name="chunkBounds">Chunk边界</param>
        /// <param name="chunkSize">Chunk像素尺寸</param>
        /// <returns>距离场数组</returns>
        public static float[] GenerateDistanceFieldData(IReadOnlyList<CurveSegment> curveSegments, 
            DistanceFieldParams parameters, Rect chunkBounds, int chunkSize)
        {
            var distanceField = new float[chunkSize * chunkSize];
            
            float pixelSize = chunkBounds.width / chunkSize;
            Vector2 boundsMin = chunkBounds.min;
            
            for (int y = 0; y < chunkSize; y++)
            {
                for (int x = 0; x < chunkSize; x++)
                {
                    Vector2 worldPos = boundsMin + new Vector2(x + 0.5f, y + 0.5f) * pixelSize;
                    float weight = CalculateStrokeWeight(worldPos, curveSegments, parameters);
                    distanceField[y * chunkSize + x] = weight;
                }
            }
            
            return distanceField;
        }
        
        /// <summary>
        /// 计算点到线段的距离
        /// </summary>
        private static float DistancePointToLineSegment(Vector2 point, Vector2 lineStart, Vector2 lineEnd)
        {
            Vector2 line = lineEnd - lineStart;
            float lineLength = line.magnitude;
            
            if (lineLength < 0.001f)
            {
                return Vector2.Distance(point, lineStart);
            }
            
            Vector2 lineDirection = line / lineLength;
            Vector2 toPoint = point - lineStart;
            
            float projectionLength = Vector2.Dot(toPoint, lineDirection);
            projectionLength = Mathf.Clamp(projectionLength, 0f, lineLength);
            
            Vector2 closestPoint = lineStart + lineDirection * projectionLength;
            return Vector2.Distance(point, closestPoint);
        }
        
        /// <summary>
        /// 应用高斯模糊到距离场
        /// </summary>
        public static void ApplyGaussianBlur(float[] distanceField, int width, int height, float blurRadius)
        {
            if (blurRadius <= 0f) return;
            
            var tempField = new float[distanceField.Length];
            int kernelSize = Mathf.RoundToInt(blurRadius * 3f) | 1; // 确保为奇数
            int halfKernel = kernelSize / 2;
            
            // 生成高斯核
            var kernel = GenerateGaussianKernel(kernelSize, blurRadius);
            
            // 水平模糊
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    float sum = 0f;
                    float weightSum = 0f;
                    
                    for (int k = -halfKernel; k <= halfKernel; k++)
                    {
                        int sampleX = Mathf.Clamp(x + k, 0, width - 1);
                        float weight = kernel[k + halfKernel];
                        sum += distanceField[y * width + sampleX] * weight;
                        weightSum += weight;
                    }
                    
                    tempField[y * width + x] = sum / weightSum;
                }
            }
            
            // 垂直模糊
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    float sum = 0f;
                    float weightSum = 0f;
                    
                    for (int k = -halfKernel; k <= halfKernel; k++)
                    {
                        int sampleY = Mathf.Clamp(y + k, 0, height - 1);
                        float weight = kernel[k + halfKernel];
                        sum += tempField[sampleY * width + x] * weight;
                        weightSum += weight;
                    }
                    
                    distanceField[y * width + x] = sum / weightSum;
                }
            }
        }
        
        /// <summary>
        /// 生成高斯核
        /// </summary>
        private static float[] GenerateGaussianKernel(int size, float sigma)
        {
            var kernel = new float[size];
            int halfSize = size / 2;
            float sum = 0f;
            
            for (int i = 0; i < size; i++)
            {
                float x = i - halfSize;
                kernel[i] = Mathf.Exp(-(x * x) / (2f * sigma * sigma));
                sum += kernel[i];
            }
            
            // 归一化
            for (int i = 0; i < size; i++)
            {
                kernel[i] /= sum;
            }
            
            return kernel;
        }
    }
} 