using System;
using MapEditor.Core;

namespace MapEditor.Data
{
    /// <summary>
    /// 添加图层对话框数据结构
    /// </summary>
    [Serializable]
    public class AddLayerDialogData
    {
        /// <summary>
        /// 图层名称
        /// </summary>
        public string layerName;
        
        /// <summary>
        /// 图层类型
        /// </summary>
        public LayerType layerType;
        
        /// <summary>
        /// 是否可见
        /// </summary>
        public bool isVisible = true;
        
        /// <summary>
        /// 是否锁定
        /// </summary>
        public bool isLocked = false;
        
        public AddLayerDialogData()
        {
            layerName = "新图层";
            layerType = LayerType.Object;
            isVisible = true;
            isLocked = false;
        }
        
        public AddLayerDialogData(string name, LayerType type)
        {
            layerName = name;
            layerType = type;
            isVisible = true;
            isLocked = false;
        }
    }
}