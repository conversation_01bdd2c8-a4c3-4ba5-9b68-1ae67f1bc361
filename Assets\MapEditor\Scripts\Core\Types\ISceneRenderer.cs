using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 可渲染对象接口，所有需要在场景中渲染的对象需实现此接口
    /// </summary>
    public interface IRenderable
    {
        /// <summary>
        /// 渲染优先级
        /// </summary>
        int RenderOrder { get; }
        
        /// <summary>
        /// 渲染层级
        /// </summary>
        RenderLayer Layer { get; }
        
        /// <summary>
        /// 是否可见
        /// </summary>
        bool IsVisible { get; set; }
        
        /// <summary>
        /// 更新渲染状态
        /// </summary>
        void UpdateRender();
    }
    
    /// <summary>
    /// 渲染层级枚举
    /// </summary>
    public enum RenderLayer
    {
        Ground = 0,      // 地表层
        Grid = 1,        // 网格层
        Object = 2,      // 对象层
        Preview = 3,     // 预览层
        UI = 4           // UI层
    }
    
    /// <summary>
    /// 场景渲染器接口，负责管理和渲染所有场景内容
    /// </summary>
    public interface ISceneRenderer
    {
        /// <summary>
        /// 获取渲染用的相机
        /// </summary>
        Camera RenderCamera { get; }
        
        /// <summary>
        /// 注册一个可渲染对象
        /// </summary>
        void RegisterRenderable(IRenderable renderable);
        
        /// <summary>
        /// 注销一个可渲染对象
        /// </summary>
        void UnregisterRenderable(IRenderable renderable);
        
        /// <summary>
        /// 设置渲染层级的可见性
        /// </summary>
        void SetLayerVisibility(RenderLayer layer, bool isVisible);
        
        /// <summary>
        /// 获取渲染层级的可见性
        /// </summary>
        bool GetLayerVisibility(RenderLayer layer);
        
        /// <summary>
        /// 强制更新所有渲染对象
        /// </summary>
        void RefreshRender();
        
        /// <summary>
        /// 创建网格渲染器
        /// </summary>
        /// <param name="gridType">网格类型</param>
        /// <returns>创建的网格渲染器实例</returns>
        IGridRenderer CreateGridRenderer(GridType gridType = GridType.Square);
    }
} 