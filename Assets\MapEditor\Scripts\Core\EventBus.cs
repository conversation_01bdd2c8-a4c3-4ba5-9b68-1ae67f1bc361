using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using System.Collections.Concurrent;
using System.Threading;

namespace MapEditor.Core
{
    /// <summary>
    /// 事件总线实现，用于系统间的事件通信
    /// 线程安全版本，支持异步事件队列
    /// </summary>
    public class EventBus : IEventSystem
    {
        // 订阅记录内部类，包含处理器和优先级
        private class SubscriptionRecord
        {
            public Delegate Handler { get; set; }
            public int Priority { get; set; }
        }
        
        // 事件字典，每种事件类型对应一个订阅记录列表
        private readonly Dictionary<Type, List<SubscriptionRecord>> subscriptions = new();
        
        // 读写锁，支持多个读取者和单个写入者
        private readonly ReaderWriterLockSlim subscriptionLock = new();
        
        // 主线程ID，用于判断当前是否在主线程
        private readonly int mainThreadId;
        
        // 待处理的异步事件队列
        private readonly ConcurrentQueue<Action> pendingEvents = new();
        
        // 是否已释放
        private volatile bool isDisposed = false;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public EventBus()
        {
            mainThreadId = Thread.CurrentThread.ManagedThreadId;
        }
        
        /// <summary>
        /// 订阅事件
        /// </summary>
        public void Subscribe<T>(Action<T> handler, int priority = 0)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));
            
            if (isDisposed)
                throw new ObjectDisposedException(nameof(EventBus));
            
            Type eventType = typeof(T);
            
            subscriptionLock.EnterWriteLock();
            try
            {
                if (!subscriptions.TryGetValue(eventType, out var handlers))
                {
                    handlers = new List<SubscriptionRecord>();
                    subscriptions[eventType] = handlers;
                }
                
                // 检查是否已订阅
                if (handlers.Any(r => r.Handler.Equals(handler)))
                {
                    Debug.LogWarning($"Handler already subscribed for event type {eventType.Name}");
                    return;
                }
                
                // 添加新订阅
                handlers.Add(new SubscriptionRecord 
                { 
                    Handler = handler, 
                    Priority = priority 
                });
                
                // 按优先级排序（降序）
                handlers.Sort((a, b) => b.Priority.CompareTo(a.Priority));
            }
            finally
            {
                subscriptionLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        public void Unsubscribe<T>(Action<T> handler)
        {
            if (handler == null)
                return;
            
            if (isDisposed)
                return;
            
            Type eventType = typeof(T);
            
            subscriptionLock.EnterWriteLock();
            try
            {
                if (!subscriptions.TryGetValue(eventType, out var handlers))
                {
                    return;
                }
                
                // 查找并移除订阅
                var recordToRemove = handlers.FirstOrDefault(r => r.Handler.Equals(handler));
                if (recordToRemove != null)
                {
                    handlers.Remove(recordToRemove);
                    
                    // 如果没有订阅者了，移除整个条目
                    if (handlers.Count == 0)
                    {
                        subscriptions.Remove(eventType);
                    }
                }
            }
            finally
            {
                subscriptionLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 发布事件（同步）
        /// </summary>
        public void Publish<T>(T eventData)
        {
            if (isDisposed)
                return;
            
            Type eventType = typeof(T);
            List<SubscriptionRecord> handlersToProcess = null;
            
            // 读取订阅者列表
            subscriptionLock.EnterReadLock();
            try
            {
                if (subscriptions.TryGetValue(eventType, out var handlers) && handlers.Count > 0)
                {
                    // 复制处理器列表，避免在遍历过程中修改
                    handlersToProcess = new List<SubscriptionRecord>(handlers);
                }
            }
            finally
            {
                subscriptionLock.ExitReadLock();
            }
            
            // 如果没有订阅者，直接返回
            if (handlersToProcess == null)
                return;
            
            // 按优先级顺序调用所有处理器
            foreach (var record in handlersToProcess)
            {
                try
                {
                    ((Action<T>)record.Handler).Invoke(eventData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error processing event {eventType.Name}: {ex.Message}\n{ex.StackTrace}");
                }
            }
        }
        
        /// <summary>
        /// 异步发布事件
        /// 注意：事件将被排队并在主线程中处理
        /// </summary>
        public void PublishAsync<T>(T eventData)
        {
            if (isDisposed)
                return;
            
            // 将事件加入队列，等待主线程处理
            pendingEvents.Enqueue(() => Publish(eventData));
            
            // 如果当前在主线程，立即处理队列
            if (Thread.CurrentThread.ManagedThreadId == mainThreadId)
            {
                ProcessPendingEvents();
            }
        }
        
        /// <summary>
        /// 处理待处理的异步事件
        /// 必须在主线程调用
        /// </summary>
        public void ProcessPendingEvents()
        {
            if (Thread.CurrentThread.ManagedThreadId != mainThreadId)
            {
                Debug.LogError("ProcessPendingEvents must be called from main thread");
                return;
            }
            
            // 处理所有待处理事件
            while (pendingEvents.TryDequeue(out var action))
            {
                try
                {
                    action?.Invoke();
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error processing pending event: {ex.Message}\n{ex.StackTrace}");
                }
            }
        }
        
        /// <summary>
        /// 清除所有事件订阅
        /// </summary>
        public void ClearAllSubscriptions()
        {
            if (isDisposed)
                return;
            
            subscriptionLock.EnterWriteLock();
            try
            {
                subscriptions.Clear();
            }
            finally
            {
                subscriptionLock.ExitWriteLock();
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (isDisposed)
                return;
            
            isDisposed = true;
            
            ClearAllSubscriptions();
            
            // 清空待处理事件
            while (pendingEvents.TryDequeue(out _)) { }
            
            subscriptionLock?.Dispose();
        }
    }
} 