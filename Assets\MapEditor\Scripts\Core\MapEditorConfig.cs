namespace MapEditor.Core
{
    /// <summary>
    /// 全局配置：用于统一坐标与像素之间的换算比例。
    /// </summary>
    public static class MapEditorConfig
    {
        /// <summary>
        /// 每 1 个世界单位包含的像素数量（Pixels Per Unit）。
        /// 修改此值即可整体缩放地图的世界尺寸表现。
        /// 典型值：16、32、64…
        /// </summary>
        public static int PixelsPerUnit = 32;

        /// <summary>
        /// 同层基于 Y 坐标的深度系数。实际 Z = -Y * YSortDepthFactor。
        /// 调整该值可控制同层对象间的前后遮挡精度。
        /// </summary>
        public static float YSortDepthFactor = 0.001f;
    }
} 