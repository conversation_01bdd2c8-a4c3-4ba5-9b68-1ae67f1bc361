using System;
using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 表示 Chunk 在图层中的整数网格坐标。
    /// 该结构不可变，可作为 Dictionary / HashSet 键。
    /// </summary>
    [Serializable]
    public struct ChunkCoord : IEquatable<ChunkCoord>
    {
        // 注意：Unity 无法序列化 readonly 字段，导致反序列化后坐标丢失。
        // 因此改为可序列化的私有字段，并提供只读属性暴露给外部。
        [SerializeField] private int x;
        [SerializeField] private int y;

        public int X => x;
        public int Y => y;

        public ChunkCoord(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        public bool Equals(ChunkCoord other) => x == other.x && y == other.y;
        public override bool Equals(object obj) => obj is ChunkCoord other && Equals(other);
        public override int GetHashCode() => (x * 397) ^ y;
        public override string ToString() => $"({x}, {y})";
    }
} 