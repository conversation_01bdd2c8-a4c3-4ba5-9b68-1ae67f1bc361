using UnityEngine;
using MapEditor.Core;
using System.Collections.Generic;
using MapEditor.Services;

namespace MapEditor.Rendering.RenderProxy
{
    /// <summary>
    /// 对象标识符，用于在场景中标识对象实例
    /// </summary>
    public class ObjectIdentifier : MonoBehaviour
    {
        [SerializeField] private string instanceId;
        [SerializeField] private ChunkCoord chunkCoord;

        private ObjectColliderAdapter colliderAdapter;

        /// <summary>
        /// 对象实例ID
        /// </summary>
        public string InstanceId
        {
            get => instanceId;
            set => instanceId = value;
        }

        /// <summary>
        /// 所属Chunk坐标
        /// </summary>
        public ChunkCoord ChunkCoord
        {
            get => chunkCoord;
            set => chunkCoord = value;
        }

        void Awake()
        {
            // 尝试获取碰撞体适配器；如无则保持为空（允许无碰撞设置）
            colliderAdapter = GetComponent<ObjectColliderAdapter>();
        }

        /// <summary>
        /// 检查点是否在选择区域内
        /// </summary>
        /// <param name="worldPoint">世界坐标点</param>
        /// <returns>是否在选择区域内</returns>
        public bool IsPointInSelectionArea(Vector2 worldPoint)
        {
            if (colliderAdapter != null)
            {
                return colliderAdapter.IsPointInSelectionArea(worldPoint);
            }

            // 回退：直接检查 Collider2D
            var col = GetSelectionCollider();
            return col != null && col.OverlapPoint(worldPoint);
        }

        /// <summary>
        /// 获取物理区域的包围盒
        /// </summary>
        /// <returns>物理区域包围盒</returns>
        public Bounds GetPhysicsBounds()
        {
            if (colliderAdapter != null)
            {
                return colliderAdapter.GetPhysicsBounds();
            }

            // 回退：直接返回选择碰撞体包围盒或零尺寸
            var col = GetSelectionCollider();
            return col != null ? col.bounds : new Bounds(transform.position, Vector3.zero);
        }

        /// <summary>
        /// 获取占用的网格格子
        /// </summary>
        /// <param name="gridService">网格服务</param>
        /// <returns>占用的网格格子列表</returns>
        public List<Vector2Int> GetOccupiedCells(GridService gridService)
        {
            if (colliderAdapter != null)
            {
                return colliderAdapter.GetOccupiedCells(gridService);
            }

            // 回退：使用选择碰撞体估算占用单元
            var col = GetSelectionCollider();
            return col != null ? gridService.GetOccupiedCells(col) : new List<Vector2Int>();
        }

        /// <summary>
        /// 获取碰撞体适配器
        /// </summary>
        /// <returns>碰撞体适配器</returns>
        public ObjectColliderAdapter GetColliderAdapter()
        {
            return colliderAdapter;
        }

        /// <summary>
        /// 标记物理包围盒需要重新计算（当对象变换时调用）
        /// </summary>
        public void MarkBoundsDirty()
        {
            if (colliderAdapter != null)
            {
                colliderAdapter.MarkPhysicsBoundsDirty();
            }
        }

        /// <summary>
        /// 获取选择用碰撞体（向下兼容）
        /// </summary>
        /// <returns>选择用碰撞体</returns>
        public Collider2D GetSelectionCollider()
        {
            return GetComponent<Collider2D>() ?? GetComponentInChildren<Collider2D>();
        }

        /// <summary>
        /// 获取物理碰撞体（向下兼容，实际返回物理包围盒信息）
        /// </summary>
        /// <returns>选择用碰撞体（作为降级处理）</returns>
        [System.Obsolete("使用GetPhysicsBounds()替代")]
        public Collider2D GetPhysicsCollider()
        {
            // 向下兼容：返回选择碰撞体
            return GetSelectionCollider();
        }
    }
} 