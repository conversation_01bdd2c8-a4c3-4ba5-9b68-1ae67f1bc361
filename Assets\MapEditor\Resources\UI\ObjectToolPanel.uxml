<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:VisualElement name="ObjectToolPanel" class="tool-panel">
        <ui:Label text="对象绘制工具" display-tooltip-when-elided="true" class="panel-title" />

        <!-- 绘制模式面板 -->
        <ui:VisualElement name="PaintModePanel" class="mode-panel">
            
            <!-- 绘制设置 -->
            <ui:VisualElement name="PaintSettingsContainer" class="section">
                <ui:Label text="绘制设置" class="subsection-title" />
                
                <!-- 当前选中的预制体 -->
                <ui:VisualElement name="SelectedPrefabContainer" class="selected-prefab-container">
                    <ui:VisualElement name="SelectedPrefabIcon" class="selected-prefab-icon" />
                    <ui:Label name="SelectedPrefabName" text="未选择预制体" class="selected-prefab-name" />
                </ui:VisualElement>
                
                <ui:VisualElement name="PaintSettings">
                    <ui:VisualElement name="PaintRotationContainer" class="property-row">
                        <ui:Label text="旋转" class="property-label" />
                        <ui:VisualElement class="property-value">
                            <ui:FloatField name="PaintRotationField" />
                        </ui:VisualElement>
                    </ui:VisualElement>
                    
                    <ui:VisualElement name="PaintScaleContainer" class="property-row">
                        <ui:Label text="缩放" class="property-label" />
                        <ui:VisualElement class="property-value">
                            <ui:FloatField name="PaintScaleX" label="X" value="1" class="vector-component" />
                            <ui:FloatField name="PaintScaleY" label="Y" value="1" class="vector-component" />
                        </ui:VisualElement>
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
            
            <!-- 预制体列表 -->
            <ui:VisualElement name="PrefabListContainer" class="section">
                <ui:Label text="可用对象" class="subsection-title" />
                
                <!-- 搜索框 -->
                <ui:VisualElement name="SearchContainer" class="search-container">
                    <ui:TextField name="PrefabSearchField" placeholder="搜索预制体..." class="search-field" />
                </ui:VisualElement>
                
                <!-- 树形视图替代原来的滚动视图 -->
                <ui:TreeView name="PrefabTreeView" class="prefab-tree-view" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML> 