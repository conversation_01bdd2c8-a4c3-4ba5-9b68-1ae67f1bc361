using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Data;
using MapEditor.Tools;
using MapEditor.Services;
using MapEditor.Event;

namespace MapEditor.UI
{
    /// <summary>
    /// 主UI面板，管理和协调所有子面板
    /// </summary>
    public class MainPanel : UIPanel
    {
        public override string PanelId => "mainPanel";
        public override string DisplayName => "主界面";
        
        // 子面板
        private ToolbarPanel toolbarPanel;
        private LayerPanel layerPanel;
        private InspectorPanel inspectorPanel;
        private BrushSettingsPanel brushSettingsPanel;
        private ObjectToolPanel objectToolPanel;
        private CurveToolPanel curveToolPanel;
        
        // 当前显示的右侧面板
        private UIPanel currentRightPanel;
        
        // UI容器元素
        private VisualElement leftPanel;
        private VisualElement rightPanel;
        private VisualElement toolbarContainer;
        private ScrollView propertiesContainer;
        private Button showPanelButton; // 显示面板按钮
        // 右侧面板拖拽调整相关字段（可视化拖拽按钮）
        private VisualElement rightResizeHandle;
        private bool isRightResizing;
        private float rightResizeStartMouseX;
        private float rightResizeStartWidth;
        private const float RightPanelMinWidth = 200f;
        private const float RightPanelMaxWidth = 600f;
        private VisualElement editorArea; // 缓存 EditorArea 方便调整
        
        public MainPanel(IUIManager uiManager, VisualTreeAsset template)
            : base(uiManager, template)
        {
        }
        
        public override void Initialize()
        {
            // 查找主要容器元素
            leftPanel = FindElement<VisualElement>("LeftPanel");
            rightPanel = FindElement<VisualElement>("RightPanel");
            toolbarContainer = FindElement<VisualElement>("Toolbar");
            propertiesContainer = FindElement<ScrollView>("PropertiesContainer");
            editorArea = FindElement<VisualElement>("EditorArea");
            
            if (leftPanel == null || rightPanel == null || toolbarContainer == null || propertiesContainer == null)
            {
                Debug.LogError("MainPanel: 无法找到必要的UI容器元素");
                return;
            }
            
            // 初始化子面板
            InitializeToolbarPanel();
            InitializeLayerPanel();
            InitializeInspectorPanel();
            InitializeToolPanels();
            
            // 设置右侧面板拖拽手柄
            SetupRightPanelResizer();
            
            // 监听工具切换事件
            MapEditorCore.Instance.EventSystem?.Subscribe<ToolChangedEvent>(OnToolChanged);
            
            // 监听图层面板可见性变更事件
            MapEditorCore.Instance.EventSystem?.Subscribe<LayerPanelVisibilityChangedEvent>(OnLayerPanelVisibilityChanged);
            
            Debug.Log("MainPanel 初始化完成");
        }
        
        /// <summary>
        /// 初始化工具栏面板
        /// </summary>
        private void InitializeToolbarPanel()
        {
            // 创建工具栏面板
            toolbarPanel = new ToolbarPanel(uiManager, null);
            
            // 重要：将工具栏面板根元素设置为已有的工具栏容器
            // 这样工具栏面板就不会创建新元素，而是使用已有的UI元素
            toolbarPanel.SetContainer(toolbarContainer);
            
            // 传递依赖注入容器
            toolbarPanel.SetContainer();
            
            // 初始化面板
            toolbarPanel.Initialize();
        }
        
        /// <summary>
        /// 初始化图层面板
        /// </summary>
        private void InitializeLayerPanel()
        {
            // 创建图层面板
            layerPanel = new LayerPanel(uiManager, null);
            
            // 设置图层面板的容器为左侧面板
            layerPanel.SetContainer(leftPanel);
            
            // 传递依赖注入容器
            layerPanel.SetContainer();
            
            // 初始化面板
            layerPanel.Initialize();
        }
        
        /// <summary>
        /// 初始化属性面板
        /// </summary>
        private void InitializeInspectorPanel()
        {
            // 创建属性面板
        //    inspectorPanel = new InspectorPanel(uiManager, null);
            
            // 设置属性面板的容器为右侧面板
          //  inspectorPanel.SetContainer(rightPanel);
            
            // 初始化面板
            //inspectorPanel.Initialize();
        }
        
        /// <summary>
        /// 初始化工具面板
        /// </summary>
        private void InitializeToolPanels()
        {
            // 初始化画笔设置面板
            var brushTemplate = Resources.Load<VisualTreeAsset>("UI/BrushSettingsPanel");
            if (brushTemplate != null)
            {
                brushSettingsPanel = new BrushSettingsPanel(uiManager, brushTemplate);
                // 直接添加到容器，而不是使用SetContainer
                propertiesContainer.contentContainer.Add(brushSettingsPanel.Root);
                // 传递依赖注入容器
                brushSettingsPanel.SetContainer();
                brushSettingsPanel.Initialize();
                brushSettingsPanel.IsVisible = false; // 默认隐藏
            }
            
            // 初始化对象工具面板
            var objectTemplate = Resources.Load<VisualTreeAsset>("UI/ObjectToolPanel");
            if (objectTemplate != null)
            {
                objectToolPanel = new ObjectToolPanel(uiManager, objectTemplate);
                // 直接添加到容器，而不是使用SetContainer
                propertiesContainer.contentContainer.Add(objectToolPanel.Root);
                // 传递依赖注入容器
                objectToolPanel.SetContainer();
                objectToolPanel.Initialize();
                objectToolPanel.IsVisible = false; // 默认隐藏
            }
            
            // 初始化曲线工具面板
            var curveTemplate = Resources.Load<VisualTreeAsset>("UI/CurveToolPanel");
            if (curveTemplate != null)
            {
                curveToolPanel = new CurveToolPanel(uiManager, curveTemplate);
                // 直接添加到容器，而不是使用SetContainer
                propertiesContainer.contentContainer.Add(curveToolPanel.Root);
                // 传递依赖注入容器
                curveToolPanel.SetContainer();
                curveToolPanel.Initialize();
                curveToolPanel.IsVisible = false; // 默认隐藏
            }
            
            Debug.Log("工具面板初始化完成");
        }
        
        /// <summary>
        /// 处理工具切换事件
        /// </summary>
        private void OnToolChanged(ToolChangedEvent evt)
        {
            // 隐藏所有工具面板
            if (brushSettingsPanel != null)
                brushSettingsPanel.IsVisible = false;
            if (objectToolPanel != null)
                objectToolPanel.IsVisible = false;
            if (curveToolPanel != null)
                curveToolPanel.IsVisible = false;
            
            // 清理选择工具的UI（如果存在）
            var selectionTool = MapEditorCore.Instance.GetService<ToolManager>().GetTool("SelectionTool") as SelectionTool;
            if (selectionTool != null)
            {
                var selectionUI = selectionTool.GetCurrentUI();
                selectionUI?.ClearSelectionPanel();
            }
            
            // 根据新工具显示对应面板
            switch (evt.NewToolId)
            {
                case "BrushTool":
                    if (brushSettingsPanel != null)
                    {
                        brushSettingsPanel.IsVisible = true;
                        currentRightPanel = brushSettingsPanel;
                    }
                    break;
                    
                case "ObjectToolPaint":
                    if (objectToolPanel != null)
                    {
                        objectToolPanel.IsVisible = true;
                        currentRightPanel = objectToolPanel;
                    }
                    break;
                    
                case "CurveTool":
                    if (curveToolPanel != null)
                    {
                        curveToolPanel.IsVisible = true;
                        currentRightPanel = curveToolPanel;
                    }
                    break;
                    
                case "SelectionTool":
                    // 选择工具使用动态UI，通过ISelectionUI接口创建
                    if (selectionTool != null)
                    {
                        var selectionUI = selectionTool.GetCurrentUI();
                        if (selectionUI != null)
                        {
                            // 创建选择面板
                            selectionUI.CreateSelectionPanel(propertiesContainer.contentContainer, new System.Collections.Generic.List<ISelectable>());
                        }
                    }
                    currentRightPanel = null; // 选择工具不使用固定面板
                    break;
                    
                default:
                    currentRightPanel = null;
                    break;
            }
            
            Debug.Log($"切换到工具面板: {evt.NewToolId}");
        }
        
        public override void UpdatePanel()
        {
            // 更新所有子面板
            toolbarPanel?.UpdatePanel();
            layerPanel?.UpdatePanel();
            inspectorPanel?.UpdatePanel();
            
            // 更新当前活动的右侧面板
            currentRightPanel?.UpdatePanel();
        }
        
        /// <summary>
        /// 图层面板可见性变更事件处理
        /// </summary>
        private void OnLayerPanelVisibilityChanged(LayerPanelVisibilityChangedEvent evt)
        {
            AdjustLayoutForLayerPanel(evt.IsHidden);
        }
        
        /// <summary>
        /// 根据图层面板的可见性调整布局
        /// </summary>
        private void AdjustLayoutForLayerPanel(bool isHidden)
        {
            if (leftPanel == null) return;
            
            if (isHidden)
            {
                // 隐藏图层面板：向左滑出
                leftPanel.style.left = -250f;
                
                // 编辑区域扩展到左边
                if (editorArea != null)
                {
                    editorArea.style.left = 0;
                }
                
                // 创建显示面板按钮
                CreateShowPanelButton();
            }
            else
            {
                // 显示图层面板：从左侧滑入
                leftPanel.style.left = 0;
                
                // 编辑区域恢复正常位置
                if (editorArea != null)
                {
                    editorArea.style.left = 250f;
                }
                
                // 移除显示面板按钮
                RemoveShowPanelButton();
            }
            
            Debug.Log($"布局调整完成，图层面板{(isHidden ? "隐藏" : "显示")}");
        }
        
        /// <summary>
        /// 创建显示面板按钮
        /// </summary>
        private void CreateShowPanelButton()
        {
            if (showPanelButton != null) return; // 避免重复创建
            
            showPanelButton = new Button()
            {
                text = "▶"
            };
            showPanelButton.style.position = Position.Absolute;
            showPanelButton.style.left = 5;
            showPanelButton.style.top = 50; // 距离顶部50px
            showPanelButton.style.width = 30;
            showPanelButton.style.height = 80;
            showPanelButton.style.fontSize = 14;
            
            // 使用与UI主题一致的颜色
            showPanelButton.style.backgroundColor = new StyleColor(new Color(0.29f, 0.29f, 0.29f, 0.95f)); // --color-background (#2D2D2D)
            showPanelButton.style.borderTopRightRadius = 8;
            showPanelButton.style.borderBottomRightRadius = 8;
            showPanelButton.style.borderLeftWidth = 0;
            showPanelButton.style.borderTopWidth = 1;
            showPanelButton.style.borderRightWidth = 1;
            showPanelButton.style.borderBottomWidth = 1;
            showPanelButton.style.borderTopColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f)); // --color-border (#555555)
            showPanelButton.style.borderRightColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
            showPanelButton.style.borderBottomColor = new StyleColor(new Color(0.333f, 0.333f, 0.333f, 1f));
            showPanelButton.style.color = new StyleColor(new Color(0.878f, 0.878f, 0.878f, 1f)); // --color-text (#E0E0E0)
            
            // 添加鼠标悬停效果
            showPanelButton.RegisterCallback<MouseEnterEvent>(evt => {
                showPanelButton.style.backgroundColor = new StyleColor(new Color(0.286f, 0.565f, 0.886f, 0.2f)); // accent color with opacity
            });
            showPanelButton.RegisterCallback<MouseLeaveEvent>(evt => {
                showPanelButton.style.backgroundColor = new StyleColor(new Color(0.29f, 0.29f, 0.29f, 0.95f)); // restore original
            });
            
            // 注册点击事件
            showPanelButton.clicked += OnShowPanelButtonClicked;
            
            // 添加到根容器
            root.Add(showPanelButton);
        }
        
        /// <summary>
        /// 移除显示面板按钮
        /// </summary>
        private void RemoveShowPanelButton()
        {
            if (showPanelButton != null)
            {
                showPanelButton.clicked -= OnShowPanelButtonClicked;
                // 销毁元素时会自动清理事件监听器
                root.Remove(showPanelButton);
                showPanelButton = null;
            }
        }
        
        /// <summary>
        /// 显示面板按钮点击事件处理
        /// </summary>
        private void OnShowPanelButtonClicked()
        {
            // 发布显示图层面板的事件
            MapEditorCore.Instance.EventSystem?.Publish(new LayerPanelVisibilityChangedEvent(false));
        }
        
        /// <summary>
        /// 创建并初始化右侧面板拖拽手柄，实现宽度可调整
        /// </summary>
        private void SetupRightPanelResizer()
        {
            if (rightPanel == null) return;

            // 若已创建则跳过
            if (rightResizeHandle != null) return;

            // 创建一个可视化的拖拽按钮（带有三条横线图案）
            rightResizeHandle = new VisualElement
            {
                name = "RightPanelDragButton"
            };

            rightResizeHandle.style.position = Position.Absolute;
            rightResizeHandle.style.left = 0;
            rightResizeHandle.style.top = 0;
            rightResizeHandle.style.bottom = 0;
            rightResizeHandle.style.width = 12;
            // 不使用背景色和描边，只保留字符装饰
            rightResizeHandle.style.backgroundColor = new StyleColor(new Color(0, 0, 0, 0));
            rightResizeHandle.style.borderRightWidth = 0;
            rightResizeHandle.style.cursor = new StyleCursor(); // 默认系统光标
            rightResizeHandle.pickingMode = PickingMode.Position;

            // 在拖拽按钮中添加装饰性条纹
            var stripes = new Label("≡")
            {
                pickingMode = PickingMode.Ignore
            };
            stripes.style.unityTextAlign = TextAnchor.MiddleCenter;
            stripes.style.fontSize = 14;
            stripes.style.color = new StyleColor(new Color(0.7f, 0.7f, 0.7f, 1f));
            stripes.style.width = Length.Percent(100);
            stripes.style.height = Length.Percent(100);
            rightResizeHandle.Add(stripes);

            rightPanel.Add(rightResizeHandle);

            // 注册事件（使用 Pointer 事件在运行时更可靠）
            rightResizeHandle.RegisterCallback<PointerDownEvent>(OnRightResizePointerDown);
            rightResizeHandle.RegisterCallback<PointerMoveEvent>(OnRightResizePointerMove);
            rightResizeHandle.RegisterCallback<PointerUpEvent>(OnRightResizePointerUp);
        }

        private void OnRightResizePointerDown(PointerDownEvent evt)
        {
            // 仅主鼠标（ID=0）
            if (evt.button != (int)MouseButton.LeftMouse) return;

            isRightResizing = true;
            rightResizeStartMouseX = evt.position.x; // 面板坐标系
            rightResizeStartWidth = rightPanel.resolvedStyle.width;

            // 捕获指针保证 PointerUp 一定触发
            rightResizeHandle.CapturePointer(evt.pointerId);

            evt.StopPropagation();
        }

        private void OnRightResizePointerMove(PointerMoveEvent evt)
        {
            if (!isRightResizing || !rightResizeHandle.HasPointerCapture(evt.pointerId)) return;

            float delta = evt.position.x - rightResizeStartMouseX;
            float newWidth = Mathf.Clamp(rightResizeStartWidth - delta, RightPanelMinWidth, RightPanelMaxWidth);

            rightPanel.style.width = newWidth;
            if (editorArea != null)
            {
                editorArea.style.right = newWidth;
            }

            evt.StopPropagation();
        }

        private void OnRightResizePointerUp(PointerUpEvent evt)
        {
            if (!isRightResizing) return;

            isRightResizing = false;
            if (rightResizeHandle.HasPointerCapture(evt.pointerId))
                rightResizeHandle.ReleasePointer(evt.pointerId);

            evt.StopPropagation();
        }
        
        public override void ClosePanel()
        {
            // 关闭所有子面板
            toolbarPanel?.ClosePanel();
            layerPanel?.ClosePanel();
            inspectorPanel?.ClosePanel();
            
            base.ClosePanel();
        }
    }
} 