using System;
using System.Collections.Generic;
using MapEditor.Core;

namespace MapEditor.Core
{
    /// <summary>
    /// 图层管理器接口，负责管理当前活动图层和图层操作
    /// </summary>
    public interface ILayerDataStore
    {
        /// <summary>
        /// 当前活动图层
        /// </summary>
        IMapLayer ActiveLayer { get; set; }
        
        /// <summary>
        /// 活动图层变更事件
        /// </summary>
        event Action<IMapLayer> OnActiveLayerChanged;
        
        /// <summary>
        /// 图层添加事件
        /// </summary>
        event Action<IMapLayer> OnLayerAdded;
        
        /// <summary>
        /// 图层移除事件
        /// </summary>
        event Action<string> OnLayerRemoved;
        
        /// <summary>
        /// 设置活动图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        void SetActiveLayer(string layerId);
        
        /// <summary>
        /// 创建新图层
        /// </summary>
        /// <param name="name">图层名称</param>
        /// <param name="type">图层类型</param>
        /// <returns>创建的图层</returns>
        IMapLayer CreateLayer(string name, LayerType type);


        IMapLayer CreateLayer(IMapLayer layer);
        
        /// <summary>
        /// 删除图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        void DeleteLayer(string layerId);
        
        /// <summary>
        /// 重命名图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        /// <param name="newName">新名称</param>
        void RenameLayer(string layerId, string newName);
        
        /// <summary>
        /// 检查是否可以在指定图层上绘制
        /// </summary>
        /// <param name="layer">要检查的图层</param>
        /// <returns>如果可以绘制返回true，否则返回false</returns>
        bool CanDrawOnLayer(IMapLayer layer);
        
        /// <summary>
        /// 根据图层ID获取图层
        /// </summary>
        /// <param name="layerId">图层ID</param>
        /// <returns>找到的图层，如果不存在返回null</returns>
        IMapLayer GetLayerById(string layerId);
        
        /// <summary>
        /// 根据图层类型获取所有图层
        /// </summary>
        /// <param name="layerType">图层类型</param>
        /// <returns>指定类型的所有图层</returns>
        IEnumerable<IMapLayer> GetLayersByType(LayerType layerType);
    }
}