using System.IO;
using System.Threading.Tasks;
using UnityEngine;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using System;

namespace MapEditor.Utility
{
    /// <summary>
    /// Runtime RAW-RGBA32 贴图读写工具。
    /// 存储格式: [int32 width][int32 height][byte[] raw RGBA32]
    /// 说明:
    /// 1. RAW 数据无需解压, 直接 <see cref="Texture2D.LoadRawTextureData(byte[])"/> 后 Apply(), 性能最佳。
    /// 2. 该工具类不依赖 UnityEditor API, 可在 Editor / Player 通用。
    /// 3. 如需进一步压缩, 请改用 DXT/ASTC 等离线方案。
    /// </summary>
    public static class RawTextureUtility
    {
        #region 同步 API

        /// <summary>
        /// 将 <paramref name="texture"/> 以 RAW-RGBA32 格式保存到 <paramref name="filePath"/>。
        /// 文件格式: [width(int)] [height(int)] [raw bytes]
        /// </summary>
        public static void SaveTexture(Texture2D texture, string filePath)
        {
            if (texture == null)
            {
                Debug.LogError("RawTextureUtility.SaveTexture: texture is null.");
                return;
            }

            // 确保父目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 将像素数据取出为 NativeArray<byte> —— 0 拷贝
            var rawData = texture.GetRawTextureData<byte>();
            using var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
            using var bw = new BinaryWriter(fs);

            bw.Write(texture.width);
            bw.Write(texture.height);
            // 直接写入 NativeArray 内容
            unsafe
            {
                void* ptr = NativeArrayUnsafeUtility.GetUnsafeReadOnlyPtr(rawData);
                var span = new ReadOnlySpan<byte>(ptr, rawData.Length);
                bw.Write(span);
            }
        }

        /// <summary>
        /// 从 <paramref name="filePath"/> 读取 RAW-RGBA32 贴图。
        /// </summary>
        public static Texture2D LoadTexture(string filePath, FilterMode filterMode = FilterMode.Point)
        {
            if (!File.Exists(filePath))
            {
//                Debug.LogWarning($"RawTextureUtility.LoadTexture: file not found -> {filePath}");
                return null;
            }

            using var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var br = new BinaryReader(fs);

            int width = br.ReadInt32();
            int height = br.ReadInt32();

            int expectedBytes = width * height * 4; // RGBA32 每像素 4 字节
            byte[] bytes = br.ReadBytes(expectedBytes);
            if (bytes.Length != expectedBytes)
            {
                Debug.LogError($"RawTextureUtility.LoadTexture: data length mismatch. expected {expectedBytes} != actual {bytes.Length}");
                return null;
            }

            var tex = new Texture2D(width, height, TextureFormat.RGBA32, false)
            {
                filterMode = filterMode
            };
            tex.LoadRawTextureData(bytes);
            tex.Apply();
            return tex;
        }
        #endregion

        #region 异步 API
        /// <summary>
        /// 异步保存贴图。
        /// </summary>
        public static async Task SaveTextureAsync(Texture2D texture, string filePath)
        {
            if (texture == null)
            {
                Debug.LogError("RawTextureUtility.SaveTextureAsync: texture is null.");
                return;
            }
            var rawData = texture.GetRawTextureData<byte>();
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await Task.Run(() =>
            {
                using var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
                using var bw = new BinaryWriter(fs);
                bw.Write(texture.width);
                bw.Write(texture.height);
                unsafe
                {
                    void* ptr = NativeArrayUnsafeUtility.GetUnsafeReadOnlyPtr(rawData);
                    var span = new ReadOnlySpan<byte>(ptr, rawData.Length);
                    bw.Write(span);
                }
            });
        }

        /// <summary>
        /// 异步加载贴图 (主线程 Apply)。
        /// </summary>
        public static async Task<Texture2D> LoadTextureAsync(string filePath, FilterMode filterMode = FilterMode.Point)
        {
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"RawTextureUtility.LoadTextureAsync: file not found -> {filePath}");
                return null;
            }

            // 先在线程池读取到内存
            var data = await Task.Run(() => File.ReadAllBytes(filePath));
            if (data.Length < 8)
            {
                Debug.LogError("RawTextureUtility.LoadTextureAsync: data too small.");
                return null;
            }

            // 从数据中解析宽高
            int width = System.BitConverter.ToInt32(data, 0);
            int height = System.BitConverter.ToInt32(data, 4);
            int expectedBytes = width * height * 4;
            if (data.Length - 8 != expectedBytes)
            {
                Debug.LogError($"RawTextureUtility.LoadTextureAsync: data length mismatch. expected {expectedBytes} != actual {data.Length - 8}");
                return null;
            }

            var tex = new Texture2D(width, height, TextureFormat.RGBA32, false)
            {
                filterMode = filterMode
            };
            // 将像素数据视图偏移 8 字节后传入, 避免 unsafe 指针调用
            tex.LoadRawTextureData(data.AsSpan(8).ToArray());
            tex.Apply();
            return tex;
        }
        #endregion

        #region ASTC 压缩 API
        /// <summary>
        /// 将已经是 ASTC 格式的 <paramref name="texture"/> 保存到磁盘。
        /// 文件结构: [width(int)] [height(int)] [format(byte)] [raw blocks]
        /// 注意: 本函数<strong>不会</strong>在运行时执行 RGBA32 → ASTC 的压缩转换, 因为 Unity 未暴露快速 ASTC 编码器。
        /// 因此请保证传入的 <paramref name="texture"/> 的 <see cref="Texture2D.format"/> 已经是 <see cref="TextureFormat.ASTC_4x4"/> / 6x6 等。
        /// 若项目需要在 Runtime 压缩, 建议通过外部工具或 Editor 扩展预处理。
        /// </summary>
        public static void SaveTextureAstc(Texture2D texture, string filePath)
        {
            if (texture == null)
            {
                Debug.LogError("RawTextureUtility.SaveTextureAstc: texture is null.");
                return;
            }
            if (!texture.format.ToString().StartsWith("ASTC"))
            {
                Debug.LogError($"RawTextureUtility.SaveTextureAstc: texture.format={texture.format} 不是 ASTC 格式, 请先转换后再保存。");
                return;
            }

            var raw = texture.GetRawTextureData<byte>();
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            using var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
            using var bw = new BinaryWriter(fs);
            bw.Write(texture.width);
            bw.Write(texture.height);
            bw.Write((byte)texture.format); // 记录格式
            unsafe
            {
                void* ptr = NativeArrayUnsafeUtility.GetUnsafeReadOnlyPtr(raw);
                var span = new ReadOnlySpan<byte>(ptr, raw.Length);
                bw.Write(span);
            }
        }

        /// <summary>
        /// 读取 ASTC 压缩贴图。需要硬件 / 平台支持对应 ASTC block 大小。
        /// </summary>
        public static Texture2D LoadTextureAstc(string filePath, FilterMode filterMode = FilterMode.Point)
        {
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"RawTextureUtility.LoadTextureAstc: file not found -> {filePath}");
                return null;
            }
            using var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var br = new BinaryReader(fs);
            int width = br.ReadInt32();
            int height = br.ReadInt32();
            byte fmtByte = br.ReadByte();
            var texFormat = (TextureFormat)fmtByte;
            int dataSize = (int)(fs.Length - fs.Position);
            byte[] blocks = br.ReadBytes(dataSize);

            var tex = new Texture2D(width, height, texFormat, false)
            {
                filterMode = filterMode
            };
            tex.LoadRawTextureData(blocks);
            // 对于压缩格式, Apply(readOnly: false, makeNoLongerReadable: true) 可节省内存
            tex.Apply(false, true);
            return tex;
        }
        #endregion

        /// <summary>
        /// 创建一张指定尺寸的空白透明贴图 (RGBA32)。
        /// </summary>
        public static Texture2D CreateBlank(int width, int height, FilterMode filterMode = FilterMode.Point)
        {
            var tex = new Texture2D(width, height, TextureFormat.RGBA32, false)
            {
                filterMode = filterMode
            };
            // 直接使用 NativeArray 快速清零
            var pixels = tex.GetRawTextureData<byte>();
            unsafe
            {
                void* ptr = NativeArrayUnsafeUtility.GetUnsafeBufferPointerWithoutChecks(pixels);
                UnsafeUtility.MemClear(ptr, (long)pixels.Length);
            }
            tex.Apply();
            return tex;
        }
    }
} 