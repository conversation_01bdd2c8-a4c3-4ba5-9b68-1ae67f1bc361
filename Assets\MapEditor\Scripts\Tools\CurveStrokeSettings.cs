using MapEditor.Data;
using UnityEngine;

namespace MapEditor.Tools
{
    /// <summary>
    /// 曲线描边设置参数
    /// 定义了曲线绘制时的宽度、材质、边缘柔和度等属性
    /// </summary>
    [System.Serializable]
    public struct CurveStrokeSettings
    {
        public float width;              // 描边宽度 (世界单位)
        public float edgeSoftness;       // 边缘柔和度 (0-1)
        public int materialIndex;        // 材质索引
        public float strength;           // 绘制强度 (0-1)
        public CurveType curveType;      // 曲线类型
        public float resolution;         // 曲线分辨率 (每世界单位的采样点数)
        public BrushShapeSO shape;       // 形状蒙版 (可选，用于沿曲线变化的笔刷形状)
        public float hardness;           // 新增：硬度 (0.1-5.0)，用于控制边缘的锐利程度

        /// <summary>
        /// 构造函数
        /// </summary>
        public CurveStrokeSettings(float width = 1.0f, float edgeSoftness = 0.5f, int materialIndex = 0, 
            float strength = 1.0f, CurveType curveType = CurveType.CatmullRom, float resolution = 10.0f, 
            BrushShapeSO shape = null, float hardness = 1.0f)
        {
            this.width = Mathf.Max(0.1f, width);
            this.edgeSoftness = Mathf.Clamp01(edgeSoftness);
            this.materialIndex = Mathf.Max(0, materialIndex);
            this.strength = Mathf.Clamp01(strength);
            this.curveType = curveType;
            this.resolution = Mathf.Max(1.0f, resolution);
            this.shape = shape;
            this.hardness = Mathf.Clamp(hardness, 0.1f, 5.0f);
        }
    }

    /// <summary>
    /// 曲线类型枚举
    /// </summary>
    public enum CurveType
    {
        Line,           // 直线
        CatmullRom,     // Catmull-Rom样条曲线
        Bezier          // 贝塞尔曲线
    }

    /// <summary>
    /// 曲线段数据结构
    /// 存储曲线上每个点的位置、切线、法向量等信息
    /// </summary>
    [System.Serializable]
    public struct CurveSegment
    {
        public Vector2 position;         // 曲线点位置
        public Vector2 tangent;          // 切线方向 (归一化)
        public Vector2 normal;           // 法向量 (归一化)
        public float distance;           // 沿曲线距离
        public float width;              // 局部宽度 (用于变宽度曲线)

        public CurveSegment(Vector2 position, Vector2 tangent, Vector2 normal, float distance, float width = 1.0f)
        {
            this.position = position;
            this.tangent = tangent.normalized;
            this.normal = normal.normalized;
            this.distance = distance;
            this.width = width;
        }
    }
} 