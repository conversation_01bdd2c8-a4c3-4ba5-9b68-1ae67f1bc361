using UnityEngine;

namespace MapEditor.Core
{
    /// <summary>
    /// 事件系统更新器，负责在主线程处理异步事件
    /// </summary>
    public class EventSystemUpdater : MonoBehaviour
    {
        private IEventSystem eventSystem;
        
        /// <summary>
        /// 初始化更新器
        /// </summary>
        public void Initialize(IEventSystem eventSystem)
        {
            this.eventSystem = eventSystem;
        }
        
        /// <summary>
        /// 每帧更新，处理待处理的异步事件
        /// </summary>
        private void Update()
        {
            if (eventSystem != null)
            {
                eventSystem.ProcessPendingEvents();
            }
        }
        
        /// <summary>
        /// 在销毁时清理
        /// </summary>
        private void OnDestroy()
        {
            eventSystem = null;
        }
    }
} 