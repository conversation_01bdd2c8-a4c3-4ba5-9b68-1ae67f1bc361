<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns="UnityEngine.UIElements">
    <ui:VisualElement name="ObjectSelectionPanel" class="selection-panel" style="flex-direction: column;">
        <!-- 预览区域 -->
        <ui:VisualElement name="PreviewContainer" class="object-preview-container" style="flex-direction: row; align-items: center; margin-bottom: 12px; padding-top: 8px; padding-bottom: 8px; padding-left: 8px; padding-right: 8px;">
            <ui:VisualElement name="ObjectIcon" class="object-preview-icon" style="width: 48px; height: 48px; margin-right: 12px; border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; background-color: rgb(204,102,51);" />
            <ui:VisualElement style="flex-direction: column; flex-grow: 1;">
                <ui:Label name="ObjectName" class="object-name-label" style="font-size: 16px; -unity-font-style: bold; color: white; margin-bottom: 4px;" />
                <ui:Label name="SelectionInfo" style="font-size: 12px; color: rgb(179,179,179);" />
            </ui:VisualElement>
        </ui:VisualElement>
        <!-- 动态属性容器，将由代码填充 -->
        <ui:VisualElement name="PropertiesContainer" class="properties-container" style="flex-direction: column;" />
    </ui:VisualElement>
</ui:UXML>