# Tilemap 地表纹理 **Texture2DArray** 重构方案

> 版本：v1.0  作者：AI Draft   日期：2025-07-01

---

## 0. 摘要
当前地表系统 1 个 TilemapLayer 被限制为最多 **7 张**表面纹理，导致大地图需要将所有可能的材质塞进同一个列表，既浪费槽位，又在跨 Chunk 绘制时产生冲突。

重构目标：
1. **每-Chunk** 最多 7 张材质；图层甚至整张地图可引用数百材质。
2. 保持跨 Chunk 绘制无缝。
3. 对现有权重 RT、画笔 Compute、存盘流程影响最小。
4. 兼容现有地图文件，可一键迁移。

核心思路：
* 使用 **Texture2DArray** 收纳全部地表贴图。
* Chunk 保存一张「**索引表**」（7 int），权重仍写在两张 ARGB32 RT 中。
* Shader 改为根据索引表到 TextureArray 中采样，而非固定采样 _Tex0-6。

---

## 1. 现状回顾
```
┌─ TilemapLayer               ─┐
│  _surfaceMaterialIndices[7] │  // 图层全局、只有 7 槽
└────────────────────────────┘
┌─ TilemapChunkRenderProxy ─┐   // 每块创建 SpriteRenderer
│  _Tex0-6 (材质)
│  _Splat0/1 (权重)          │
└────────────────────────────┘
```
优点：跨 Chunk 共享同一通道号；缺点：全局 7 限制 + UI 操作受阻。

---

## 2. 新架构概览
```
                 Texture2DArray  (N ≤ 2048)
                ────────────────────────────
┌─Chunk A──────────────────────┐  ┌─Chunk B─────────────┐
│ IndexTable[7]  = {3,14,9,…}  │  │ {3,14,25,…}         │  // per-chunk
│ WeightRT0/1  (RGBA32)        │  │ WeightRT0/1         │  // 原格式
│                              │  │                     │
└──────────────────────────────┘  └─────────────────────┘
```
* **IndexTable**：长度固定 7 的 `int[]`，记录该 Chunk 的「通道→TextureArrayLayer」映射。
* **权重 RT**：含 7+coverage 的权重；算法不变。
* **Shader**：`SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, index, uv)` 读取贴图；`index` 来自常量缓冲中的两组 `int4`（下节细述）。

---

## 3. 详细设计
### 3.1 Texture2DArray 生成
1. **离线预烘焙**：启动时扫描 `Resources/GroundTextures/*.asset` → 聚合成 `GroundTexturesArray.asset` (ScriptableObject 携带 Texture2DArray)。
2. **运行时回退**：若新增 SO 数量超出数组层数，动态重建 Texture2DArray 并热更新 `MaterialPropertyBlock`。
3. **层索引** = `GroundTextureSO.textureIndex`（逻辑不变），保证唯一性。

### 3.2 数据结构调整
```csharp
// Data/Chunks/TilemapChunk.cs新增
[SerializeField] private int[] materialIndexTable = new int[7]; // -1 表示空槽
public IReadOnlyList<int> MaterialIndexTable => materialIndexTable;
public int GetOrAssignChannel(int globalIndex) { /* per-chunk 版本 */ }
```
* `TilemapLayer` 去掉 `_surfaceMaterialIndices` 与 `_materialToChannelMap`；保留兼容包装，内部委托到 **任意一个目标 Chunk**。

### 3.3 渲染代理修改
* **TilemapChunkRenderProxy**
  * 删除 _Tex0-6 纹理字段。
  * 挂载 `SplatMixArrayLit` Shader（新）。
  * 通过 `MaterialPropertyBlock` 设置：
    ```csharp
    // int4 (x,y,z,w)  → 通道 0-3, 4-6(-1),dummy
    Vector4 indices0 = new(materials[0], materials[1], materials[2], materials[3]);
    Vector4 indices1 = new(materials[4], materials[5], materials[6], -1);
    mpb.SetVector("_IndexTable0", indices0);
    mpb.SetVector("_IndexTable1", indices1);
    ```

### 3.4 Shader `SplatMixArrayLit.shader`
```hlsl
TEXTURE2D_ARRAY(_SurfaceArray);
float4 _IndexTable0; // 通道0-3 索引
float4 _IndexTable1; // 通道4-6 索引 + 填充
...
float indices[7] = { _IndexTable0.x, _IndexTable0.y, _IndexTable0.z, _IndexTable0.w,
                     _IndexTable1.x, _IndexTable1.y, _IndexTable1.z };
float3 col = 0;
col += SAMPLE_TEXTURE2D_ARRAY(_SurfaceArray, sampler_SurfaceArray, uv, indices[0]).rgb * w0.r;
...
```
其他逻辑（归一化、coverage）保持一致。

### 3.5 Compute Shader
* `SplatBrush.compute`、`SplatBlur.compute` **无需改动**；权重通道与之前一致。

### 3.6 绘制逻辑 TilemapLayerRenderer
1. **GetOrAssignChannel(chunk, globalIndex)**：
   * 若 chunk.Table 已含 index → 返回通道号。
   * 否则若空槽 → 写入并返回。
   * 若满：采用 **LRU/权重和最小** 的通道置换策略；替换后 _WeightRT_ 中整通道数据可直接覆盖，不需要迁移像素。
2. **跨 Chunk 同步**：当在 A、B 之间绘制材质 X 时，如 A 已有通道 t，但 B 无；B 也尝试把 X 放到 **同一 t 槽**，若 t 被占用 → 先执行上述置换。
   * 逻辑：遍历涉及 Chunk，先统计「主 Chunk」拥有的通道号，然后对每个 Chunk 强制对齐。
3. 更新后调用 `proxy.ApplyIndexTable()` 刷新 MPB。

### 3.7 序列化格式
* `TilemapChunk` 在 `OnBeforeSerialize` 时同步 `materialIndexTable`；老地图文件读取时若表全 -1 → 迁移器自动把旧 `TilemapLayer._surfaceMaterialIndices` 内容复制到每个 Chunk。 
* RAW 权重、颜色贴图保存路径 **不变**；兼容旧版本。

### 3.8 Editor / UI 影响
* 「刷子材质选择面板」从全局 7 个 → 全量列表（GroundTextureProvider）。
* 「图层材质占用」视图变更：显示光标所在 Chunk 的映射表供调试。

---

## 4. 任务拆解 & 里程碑
| 阶段 | 内容 | 负责人 | 预计 | 备注 |
|------|------|--------|------|------|
| M1 | Texture2DArray 生成 & Provider 重构 |  | 1d | 仅数据层 |
| M2 | TilemapChunk 数据类 & 序列化 |  | 1d | 兼容旧地图 |
| M3 | 新 Shader & Material 升级脚本 |  | 0.5d | 包含 URP keywords |
| M4 | RenderProxy + LayerRenderer 通道管理 |  | 2d | 含同步算法 |


---

## 5. 风险 & 缓解
| 风险 | 影响 | 缓解 |
|------|------|------|
| TextureArray 不被部分目标平台支持 | 黑屏/降级 | 限制平台 = Windows；保留旧管线分支|
| 替换通道迁移导致权重丢失 | 图块颜色异常 | 置换前统计「被踢材质」权重和为 0 时再回收 |
| 旧地图迁移 bug | 运行期崩溃 | 提供批处理工具 + 回滚备份 |

---

## 6. 参考实现
* 《Substance Painter 使用的索引贴图技术》
* Unity Manual – Texture2DArray
* GDC-2016 《The Order:1886 Terrain System》

---

## 7. 附录
### 7.1 新增文件一览
```
Shaders/SplatMixArrayLit.shader
Scripts/Core/Map/IndexTableUtility.cs
Scripts/Data/Chunks/TilemapChunk_IndexTable.cs (partial)
Migration/LayerToChunkIndexTableUpgrader.cs (Editor only)
```

### 7.2 Uniform Layout
```
CBUFFER_START(UnityPerMaterial)
    int4 _IndexTable0; // 通道 0-3
    int4 _IndexTable1; // 通道 4-6 + dummy
CBUFFER_END
```

---

> 以上设计确保渲染/编辑逻辑的最小侵入，同时彻底解除 7 张材质限制。若有疑问或需进一步拆分，请在实施前先沟通。 