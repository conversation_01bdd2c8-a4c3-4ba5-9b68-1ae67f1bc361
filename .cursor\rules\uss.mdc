---
description: USS常用样式属性说明，当你在制作新的基于UI Tookit的UI或优化现有UI样式时，尽量先阅读此文件。
globs: 
alwaysApply: false
---

## 4. USS 属性 (Properties)

USS属性用于定义UI元素的具体样式和行为。每个属性接受特定类型的值。

### 4.1 属性数据类型

USS支持多种数据类型，包括：

*   **数值 (Numeric)**: 如 `10px` (像素), `1.5em` (相对单位), `90deg` (角度), `50%` (百分比)。
*   **颜色 (Color)**: 如 `#RRGGBB`, `#RGBA`, `rgb()`, `rgba()`, 以及颜色关键字如 `blue`, `transparent`。
*   **字符串 (String)**: 用引号包裹，如 `"Hello World"`。
*   **资源路径 (Resource Path)**: 使用 `url()` 函数引用项目中的资源，如 `url('project://database/Assets/MyImage.png')`。
*   **关键字 (Keywords)**: 预定义的标识符，如 `flex`, `none`, `bold`, `absolute`。

### 4.2 布局属性 (Layout Properties)

布局属性控制元素在屏幕上的位置、大小和对齐方式。UI Toolkit主要使用Flexbox模型进行布局。

#### 4.2.1 Display

*   `display`: 控制元素是否可见以及如何参与布局。
    *   `flex` (默认): 元素可见，并作为flex容器。
    *   `none`: 元素完全不可见，且不占用任何空间。

#### 4.2.2 Flexbox

*   `flex-direction`: 设置主轴方向。
    *   `column` (默认): 垂直排列。
    *   `row`: 水平排列。
    *   `column-reverse`, `row-reverse`: 反向排列。
*   `flex-grow`: 定义元素的放大比例，使其填充可用空间。值为 `1` 时，元素会拉伸以填充父容器。
*   `flex-shrink`: 定义元素的缩小比例。
*   `flex-basis`: 定义元素在分配多余空间之前的默认大小。
*   `align-items`: 定义元素在交叉轴（与主轴垂直的轴）上的对-齐方式 (`flex-start`, `flex-end`, `center`, `stretch`)。
*   `justify-content`: 定义元素在主轴上的对齐方式 (`flex-start`, `flex-end`, `center`, `space-between`, `space-around`)。

#### 4.2.3 定位 (Positioning)

*   `position`: 设置元素的定位方式。
    *   `relative` (默认): 相对其正常位置进行定位。
    *   `absolute`: 相对其第一个非`static`的祖先元素进行定位。
*   `left`, `top`, `right`, `bottom`: 当`position`为`absolute`或`relative`时，用于指定元素的偏移量。

#### 4.2.4 尺寸、边距和内边距 (Sizing, Margin, and Padding)

*   `width`, `height`: 设置元素的宽度和高度。
*   `min-width`, `min-height`, `max-width`, `max-height`: 设置元素的最小/最大尺寸。
*   `margin`: 设置元素的外边距。
*   `padding`: 设置元素的内边距。
*   `border-width`: 设置边框宽度。

### 4.3 文本属性 (Text Properties)

*   `-unity-font`: 指定字体资源。
*   `-unity-font-style`: 设置字体样式 (`normal`, `italic`, `bold`)。
*   `font-size`: 设置字体大小。
*   `color`: 设置文本颜色。
*   `-unity-text-align`: 设置文本对齐方式 (`upper-left`, `middle-center`, `lower-right` 等)。
*   `white-space`: 控制如何处理元素内的空白 (`normal`, `nowrap`)。

### 4.4 背景和图像 (Background &amp; Images)

*   `background-color`: 设置背景颜色。
*   `background-image`: 设置背景图像。
*   `-unity-background-scale-mode`: 设置背景图像的缩放模式 (`stretch-to-fill`, `scale-and-crop`, `scale-to-fit`)。
*   `-unity-slice-left`, `-unity-slice-top`, `-unity-slice-right`, `-unity-slice-bottom`: 用于9切片图像，定义边框的宽度。

### 4.5 Transform 属性

Transform属性可以在不影响布局的情况下对元素进行2D变换。

*   `translate`: 移动元素 (`translate: 10px 5px;`)。
*   `rotate`: 旋转元素 (`rotate: 45deg;`)。
*   `scale`: 缩放元素 (`scale: 1.5 1.5;`)。
*   `transform-origin`: 设置变换的原点。

### 4.6 Transition 属性

Transition（过渡）可以在属性值发生变化时创建平滑的动画效果。

*   `transition-property`: 指定要应用过渡效果的USS属性（或`all`）。
*   `transition-duration`: 指定过渡效果的持续时间（例如 `0.5s`）。
*   `transition-timing-function`: 指定过渡的速度曲线（例如 `ease-in-out`, `linear`）。
*   `transition-delay`: 指定过渡效果开始前的延迟时间。

**示例**:

```css
Button {
    background-color: blue;
    transition-property: background-color;
    transition-duration: 0.3s;
    transition-timing-function: ease-out;
}

Button:hover {
    background-color: red;
}
```

### 4.7 属性参考

UI Toolkit支持大量的USS属性。有关所有可用属性、其语法和值的完整列表，请参阅官方的 **[USS属性参考](mdc:https:/docs.unity3d.com/Manual/UIE-USS-Properties-Reference.html)**。

---
## 5. USS 变量 (自定义属性)

USS支持变量，也称为自定义属性，这使得样式的管理和主题化变得非常方便。当一个变量的值被更新时，所有使用该变量的属性都会自动更新。

### 5.1 创建和使用变量

*   **声明变量**: 变量名必须以两个连字符 `--` 开头。通常在 `:root` 伪类中声明全局变量，这样它们就可以在整个文档中被访问。
*   **使用变量**: 使用 `var()` 函数来引用一个变量。

**示例**:

```css
:root {
    --main-brand-color: #007bff;
    --default-font-size: 12px;
    --border-radius: 5px;
}

Button {
    background-color: var(--main-brand-color);
    font-size: var(--default-font-size);
    border-radius: var(--border-radius);
}

.special-button {
    /* 也可以在特定选择器中覆盖变量 */
    --main-brand-color: #dc3545; 
    background-color: var(--main-brand-color);
}
```

### 5.2 备用值 (Fallback Values)

`var()` 函数可以接受第二个参数作为备用值。如果第一个参数引用的变量未定义，则会使用备用值。

**示例**:

```css
.my-element {
    /* 如果--undefined-color未定义，则使用红色 */
    background-color: var(--undefined-color, red);
}
```

