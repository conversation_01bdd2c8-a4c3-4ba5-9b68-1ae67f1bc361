<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xmlns="UnityEngine.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" noNamespaceSchemaLocation="../../UIElementsSchema/UnityEngine.UIElements.xsd" editor-extension-mode="False">
    <ui:VisualElement name="Root" style="position: absolute; left: 0; right: 0; top: 200px; bottom: 0;min-width: 360px; min-height: 600px; max-width: 100%; max-height: 100%; justify-content: center; align-items: center; flex-direction: column;">
        <ui:VisualElement name="Dialog" class="layer-settings-dialog" style="width: 360px; min-width: 360px; padding: 16px 24px 20px 24px; background-color: rgb(60, 60, 60); border-width: 1px; border-radius: 6px; flex-direction: column; align-items: stretch;">
            <ui:Label text="图层设置" style="-unity-font-style: bold; font-size: 16px; color: rgb(224, 224, 224); margin-bottom: 8px;" />
            <ui:VisualElement style="flex-direction: row; align-items: center; margin-bottom: 8px; width:100%;">
                <ui:Label text="渲染排序:" style="min-width: 90px; color: rgb(187, 187, 187);" />
                <ui:IntegerField name="OrderField" style="flex-grow: 1; min-width: 0px;flex-shrink: 1;" />
            </ui:VisualElement>
            <ui:Label name="RangeLabel" text="范围" style="font-size: 12px; color: rgb(187, 187, 187); margin-bottom: 12px;" />
            <!-- 扩展设置容器，按图层类型动态加载额外模板 -->
            <ui:VisualElement name="ExtraContainer" style="flex-direction: column; margin-bottom: 12px;" />
            <ui:VisualElement style="flex-direction: row; justify-content: flex-end; margin-top: 12px;">
                <ui:Button name="ConfirmButton" text="确定" style="margin-left: 4px;" />
                <ui:Button name="CancelButton" text="取消" style="margin-left: 4px;" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
