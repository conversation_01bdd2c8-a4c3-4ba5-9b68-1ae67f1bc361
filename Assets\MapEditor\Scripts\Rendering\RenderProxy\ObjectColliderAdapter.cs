using UnityEngine;
using System.Collections.Generic;
using MapEditor.Services;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace MapEditor.Rendering.RenderProxy
{
    /// <summary>
    /// 碰撞体模式
    /// </summary>
    public enum ColliderMode
    {
        Same = 0,      // 选择和物理使用相同区域
        Adaptive = 1,  // 基于选择碰撞体自动调整物理区域
        Custom = 2,    // 使用自定义虚拟碰撞体
        None = 3       // 禁用碰撞/选择与物理计算
    }

    /// <summary>
    /// 虚拟碰撞体形状类型
    /// </summary>
    public enum VirtualColliderShape
    {
        Box,
        Circle,
        Capsule,
        Polygon // 自定义多边形
    }

    /// <summary>
    /// 虚拟碰撞体数据
    /// </summary>
    [System.Serializable]
    public class VirtualCollider
    {
        [Header("形状设置")]
        public VirtualColliderShape shape = VirtualColliderShape.Box;
        
        [Header("位置和尺寸")]
        public Vector2 center = Vector2.zero;
        public Vector2 size = Vector2.one;
        public float radius = 0.5f; // 用于圆形
        
        [Header("胶囊体设置")]
        public CapsuleDirection2D direction = CapsuleDirection2D.Vertical;

        [Header("多边形设置")]
        [Tooltip("多边形顶点列表，局部坐标系下。至少需要3个点。")]
        public List<Vector2> points = new List<Vector2>() { new Vector2(-0.5f,-0.5f), new Vector2(0.5f,-0.5f), new Vector2(0.5f,0.5f), new Vector2(-0.5f,0.5f) };

        /// <summary>
        /// 检查点是否在虚拟碰撞体内
        /// </summary>
        public bool OverlapPoint(Vector2 localPoint)
        {
            switch (shape)
            {
                case VirtualColliderShape.Box:
                    return OverlapPointBox(localPoint);
                case VirtualColliderShape.Circle:
                    return OverlapPointCircle(localPoint);
                case VirtualColliderShape.Capsule:
                    return OverlapPointCapsule(localPoint);
                case VirtualColliderShape.Polygon:
                    return OverlapPointPolygon(localPoint);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取世界坐标包围盒
        /// </summary>
        public Bounds GetBounds(Transform transform)
        {
            Vector3 worldCenter = transform.TransformPoint(center);
            Vector3 worldSize;

            switch (shape)
            {
                case VirtualColliderShape.Box:
                    worldSize = transform.TransformVector(size);
                    // 确保尺寸为正值
                    worldSize = new Vector3(Mathf.Abs(worldSize.x), Mathf.Abs(worldSize.y), Mathf.Abs(worldSize.z));
                    break;
                case VirtualColliderShape.Circle:
                    float worldRadius = radius * Mathf.Max(transform.lossyScale.x, transform.lossyScale.y);
                    worldSize = new Vector3(worldRadius * 2, worldRadius * 2, 0);
                    break;
                case VirtualColliderShape.Capsule:
                    Vector2 scaledSize = Vector2.Scale(size, transform.lossyScale);
                    worldSize = new Vector3(Mathf.Abs(scaledSize.x), Mathf.Abs(scaledSize.y), 0);
                    break;
                case VirtualColliderShape.Polygon:
                    if (points == null || points.Count == 0)
                    {
                        worldSize = Vector3.one;
                    }
                    else
                    {
                        Vector2 min = Vector2.positiveInfinity;
                        Vector2 max = Vector2.negativeInfinity;
                        foreach (var p in points)
                        {
                            Vector3 wp = transform.TransformPoint(center + p);
                            min = Vector2.Min(min, wp);
                            max = Vector2.Max(max, wp);
                        }
                        worldCenter = (min + max) * 0.5f;
                        Vector2 size2d = max - min;
                        worldSize = new Vector3(size2d.x, size2d.y, 0);
                    }
                    break;
                default:
                    worldSize = Vector3.one;
                    break;
            }

            return new Bounds(worldCenter, worldSize);
        }

        private bool OverlapPointBox(Vector2 localPoint)
        {
            Vector2 halfSize = size * 0.5f;
            Vector2 relativePoint = localPoint - center;
            return Mathf.Abs(relativePoint.x) <= halfSize.x && Mathf.Abs(relativePoint.y) <= halfSize.y;
        }

        private bool OverlapPointCircle(Vector2 localPoint)
        {
            Vector2 relativePoint = localPoint - center;
            return relativePoint.sqrMagnitude <= radius * radius;
        }

        private bool OverlapPointCapsule(Vector2 localPoint)
        {
            Vector2 relativePoint = localPoint - center;
            Vector2 halfSize = size * 0.5f;

            if (direction == CapsuleDirection2D.Vertical)
            {
                // 垂直胶囊体
                float capsuleRadius = halfSize.x;
                float capsuleHeight = halfSize.y;

                if (Mathf.Abs(relativePoint.y) <= capsuleHeight - capsuleRadius)
                {
                    // 在中间矩形区域
                    return Mathf.Abs(relativePoint.x) <= capsuleRadius;
                }
                else
                {
                    // 在圆形端部
                    float circleY = relativePoint.y > 0 ? capsuleHeight - capsuleRadius : -(capsuleHeight - capsuleRadius);
                    Vector2 circleCenter = new Vector2(0, circleY);
                    return (relativePoint - circleCenter).sqrMagnitude <= capsuleRadius * capsuleRadius;
                }
            }
            else
            {
                // 水平胶囊体
                float capsuleRadius = halfSize.y;
                float capsuleWidth = halfSize.x;

                if (Mathf.Abs(relativePoint.x) <= capsuleWidth - capsuleRadius)
                {
                    // 在中间矩形区域
                    return Mathf.Abs(relativePoint.y) <= capsuleRadius;
                }
                else
                {
                    // 在圆形端部
                    float circleX = relativePoint.x > 0 ? capsuleWidth - capsuleRadius : -(capsuleWidth - capsuleRadius);
                    Vector2 circleCenter = new Vector2(circleX, 0);
                    return (relativePoint - circleCenter).sqrMagnitude <= capsuleRadius * capsuleRadius;
                }
            }
        }

        private bool OverlapPointPolygon(Vector2 localPoint)
        {
            if (points == null || points.Count < 3) return false;
            return PointInPolygon(localPoint);
        }

        // 射线交叉法判断点是否在多边形内(局部坐标)
        private bool PointInPolygon(Vector2 p)
        {
            bool inside = false;
            int count = points.Count;
            for (int i = 0, j = count - 1; i < count; j = i++)
            {
                Vector2 pi = points[i] + center;
                Vector2 pj = points[j] + center;

                bool intersect = ((pi.y > p.y) != (pj.y > p.y)) &&
                                  (p.x < (pj.x - pi.x) * (p.y - pi.y) / (pj.y - pi.y + Mathf.Epsilon) + pi.x);
                if (intersect) inside = !inside;
            }
            return inside;
        }
    }

    /// <summary>
    /// 对象碰撞体适配器，统一管理选择和物理碰撞检测
    /// </summary>
    public class ObjectColliderAdapter : MonoBehaviour
    {
        [Header("碰撞体配置")]
        [Tooltip("碰撞体模式")]
        public ColliderMode mode = ColliderMode.Adaptive;

        [Header("自适应模式设置")]
        [Tooltip("物理区域相对于选择区域的缩放")]
        public Vector2 physicsScale = Vector2.one;
        [Tooltip("物理区域相对于选择区域的偏移")]
        public Vector2 physicsOffset = Vector2.zero;

        [Header("自定义模式设置")]
        [Tooltip("自定义物理碰撞体")]
        public VirtualCollider customPhysicsCollider = new VirtualCollider();

        [Header("调试选项")]
        [Tooltip("是否在Scene视图中绘制物理区域")]
        public bool showPhysicsRegion = true;

        private Collider2D selectionCollider;
        private Bounds cachedPhysicsBounds;
        private bool physicsBoundsDirty = true;

        void Awake()
        {
            selectionCollider = GetComponent<Collider2D>();
            if (selectionCollider == null)
            {
                selectionCollider = GetComponentInChildren<Collider2D>();
            }
        }

        void Start()
        {
            // 确保有选择碰撞体
            if (selectionCollider == null)
            {
                Debug.LogWarning($"ObjectColliderAdapter: 对象 {gameObject.name} 没有找到选择用的碰撞体");
            }
        }

        /// <summary>
        /// 检查点是否在选择区域内
        /// </summary>
        public bool IsPointInSelectionArea(Vector2 worldPoint)
        {
            // None 模式直接返回 false
            if (mode == ColliderMode.None) return false;

            if (selectionCollider == null) return false;
            return selectionCollider.OverlapPoint(worldPoint);
        }

        /// <summary>
        /// 获取物理区域的世界坐标包围盒
        /// </summary>
        public Bounds GetPhysicsBounds()
        {
            if (physicsBoundsDirty)
            {
                RecalculatePhysicsBounds();
                physicsBoundsDirty = false;
            }
            return cachedPhysicsBounds;
        }

        /// <summary>
        /// 获取占用的网格格子
        /// </summary>
        public List<Vector2Int> GetOccupiedCells(GridService gridService)
        {
            if (gridService == null)
            {
                Debug.LogWarning("ObjectColliderAdapter: GridService为空");
                return new List<Vector2Int>();
            }

            switch (mode)
            {
                case ColliderMode.Same:
                    return gridService.GetOccupiedCells(selectionCollider);
                case ColliderMode.Adaptive:
                case ColliderMode.Custom:
                    return gridService.GetOccupiedCells(GetPhysicsBounds());
                case ColliderMode.None:
                default:
                    return new List<Vector2Int>();
            }
        }

        /// <summary>
        /// 标记物理包围盒需要重新计算
        /// </summary>
        public void MarkPhysicsBoundsDirty()
        {
            physicsBoundsDirty = true;
        }

        /// <summary>
        /// 重新计算物理包围盒
        /// </summary>
        private void RecalculatePhysicsBounds()
        {
            switch (mode)
            {
                case ColliderMode.Same:
                    cachedPhysicsBounds = selectionCollider != null ? selectionCollider.bounds : new Bounds(transform.position, Vector3.one);
                    break;
                case ColliderMode.Adaptive:
                    cachedPhysicsBounds = CalculateAdaptiveBounds();
                    break;
                case ColliderMode.Custom:
                    cachedPhysicsBounds = customPhysicsCollider.GetBounds(transform);
                    break;
                case ColliderMode.None:
                    cachedPhysicsBounds = new Bounds(transform.position, Vector3.zero); // 尺寸为0
                    break;
                default:
                    cachedPhysicsBounds = new Bounds(transform.position, Vector3.one);
                    break;
            }
        }

        /// <summary>
        /// 计算自适应物理包围盒
        /// </summary>
        private Bounds CalculateAdaptiveBounds()
        {
            if (selectionCollider == null)
            {
                return new Bounds(transform.position, Vector3.one);
            }

            Bounds selectionBounds = selectionCollider.bounds;
            
            // 应用偏移
            Vector3 newCenter = selectionBounds.center + (Vector3)physicsOffset;
            
            // 应用缩放
            Vector3 newSize = Vector3.Scale(selectionBounds.size, physicsScale);
            
            return new Bounds(newCenter, newSize);
        }

        /// <summary>
        /// 在Scene视图中绘制调试信息（仅编辑模式）
        /// </summary>
        void OnDrawGizmos()
        {
            if (!showPhysicsRegion || mode == ColliderMode.None) return;
            
            try
            {
                // 绘制物理区域或Polygon
                Gizmos.color = Color.red;

                if (mode == ColliderMode.Custom && customPhysicsCollider.shape == VirtualColliderShape.Polygon && customPhysicsCollider.points.Count >= 2)
                {
                    // 按顺序绘制多边形边
                    for (int i = 0; i < customPhysicsCollider.points.Count; i++)
                    {
                        Vector3 wp1 = transform.TransformPoint(customPhysicsCollider.center + customPhysicsCollider.points[i]);
                        Vector3 wp2 = transform.TransformPoint(customPhysicsCollider.center + customPhysicsCollider.points[(i + 1) % customPhysicsCollider.points.Count]);
                        Gizmos.DrawLine(wp1, wp2);
                    }
                }
                else
                {
                    Bounds physicsBounds = GetPhysicsBounds();
                    if (physicsBounds.size.magnitude > 0.001f)
                    {
                        Gizmos.DrawWireCube(physicsBounds.center, physicsBounds.size);
                        Gizmos.color = Color.yellow;
                        Gizmos.DrawWireSphere(physicsBounds.center, 0.1f);
                    }
                }

                // 绘制选择区域
                if (selectionCollider != null)
                {
                    Gizmos.color = Color.green;
                    Bounds selectionBounds = selectionCollider.bounds;
                    if (selectionBounds.size.magnitude > 0.001f)
                    {
                        Gizmos.DrawWireCube(selectionBounds.center, selectionBounds.size);
                        
                        // 绘制中心点
                        Gizmos.color = Color.cyan;
                        Gizmos.DrawWireSphere(selectionBounds.center, 0.05f);
                    }
                }
                
                // 绘制模式标识
                Gizmos.color = Color.white;
                Vector3 labelPos = transform.position + Vector3.up * 0.5f;
                
                // 在Scene视图中显示模式信息（仅在选中时）
                #if UNITY_EDITOR
                if (UnityEditor.Selection.activeGameObject == gameObject)
                {
                    UnityEditor.Handles.Label(labelPos, $"Mode: {mode}");
                }
                #endif
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"ObjectColliderAdapter Gizmos绘制错误: {e.Message}");
            }
        }

        /// <summary>
        /// 在Scene视图中绘制选中时的调试信息
        /// </summary>
        void OnDrawGizmosSelected()
        {
            if (!showPhysicsRegion || mode == ColliderMode.None) return;

            try
            {
                // 根据类型绘制边框
                Gizmos.color = new Color(1, 0, 0, 0.8f);

                if (mode == ColliderMode.Custom && customPhysicsCollider.shape == VirtualColliderShape.Polygon && customPhysicsCollider.points.Count >= 2)
                {
                    // 绘制Polygon线框
                    for (int i = 0; i < customPhysicsCollider.points.Count; i++)
                    {
                        Vector3 wp1 = transform.TransformPoint(customPhysicsCollider.center + customPhysicsCollider.points[i]);
                        Vector3 wp2 = transform.TransformPoint(customPhysicsCollider.center + customPhysicsCollider.points[(i + 1) % customPhysicsCollider.points.Count]);
                        Gizmos.DrawLine(wp1, wp2);
                    }

                    // 顶点拖拽编辑
                    bool changed = false;
                    for (int i = 0; i < customPhysicsCollider.points.Count; i++)
                    {
                        Vector3 wp = transform.TransformPoint(customPhysicsCollider.center + customPhysicsCollider.points[i]);

                        float size = HandleUtility.GetHandleSize(wp) * 0.1f;
                        Handles.color = Color.yellow;
                        var fmh_452_29_638868414254574755 = Quaternion.identity; Vector3 newWp = Handles.FreeMoveHandle(
                            wp,
                            size,
                            Vector3.zero,
                            Handles.CircleHandleCap);

                        if (wp != newWp)
                        {
                            Undo.RecordObject(this, "Move Polygon Point");
                            Vector2 local = (Vector2)transform.InverseTransformPoint(newWp) - customPhysicsCollider.center;
                            customPhysicsCollider.points[i] = local;
                            changed = true;
                        }
                    }
                    if (changed)
                    {
                        MarkPhysicsBoundsDirty();
                        EditorUtility.SetDirty(this);
                    }
                }
                else
                {
                    Bounds physicsBounds = GetPhysicsBounds();
                    if (physicsBounds.size.magnitude > 0.001f)
                    {
                        Gizmos.DrawWireCube(physicsBounds.center, physicsBounds.size);
                        Gizmos.color = new Color(1, 0, 0, 0.1f);
                        Gizmos.DrawCube(physicsBounds.center, physicsBounds.size);
                    }
                }

                if (selectionCollider != null)
                {
                    Gizmos.color = new Color(0, 1, 0, 0.8f);
                    Bounds selectionBounds = selectionCollider.bounds;
                    if (selectionBounds.size.magnitude > 0.001f)
                    {
                        Gizmos.DrawWireCube(selectionBounds.center, selectionBounds.size);
                        
                        // 绘制填充区域（半透明）
                        Gizmos.color = new Color(0, 1, 0, 0.1f);
                        Gizmos.DrawCube(selectionBounds.center, selectionBounds.size);
                    }
                }
                
                // 显示详细信息
                #if UNITY_EDITOR
                Vector3 infoPos = transform.position + Vector3.up * 1f;
                string info = $"Mode: {mode}\n";
                
                if (mode == ColliderMode.Adaptive)
                {
                    info += $"Scale: {physicsScale}\nOffset: {physicsOffset}";
                }
                else if (mode == ColliderMode.Custom)
                {
                    info += $"Shape: {customPhysicsCollider.shape}\nSize: {customPhysicsCollider.size}";
                }
                
                UnityEditor.Handles.Label(infoPos, info);
                #endif
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"ObjectColliderAdapter Selected Gizmos绘制错误: {e.Message}");
            }
        }

        /// <summary>
        /// 强制重新绘制Gizmos（调试用）
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public void ForceRedrawGizmos()
        {
            #if UNITY_EDITOR
            UnityEditor.SceneView.RepaintAll();
            #endif
        }

        /// <summary>
        /// 调试信息输出
        /// </summary>
        [ContextMenu("输出调试信息")]
        public void PrintDebugInfo()
        {
            Debug.Log($"=== ObjectColliderAdapter Debug Info ===");
            Debug.Log($"GameObject: {gameObject.name}");
            Debug.Log($"Show Physics Region: {showPhysicsRegion}");
            Debug.Log($"Mode: {mode}");
            Debug.Log($"Selection Collider: {(selectionCollider != null ? selectionCollider.GetType().Name : "null")}");
            
            if (selectionCollider != null)
            {
                Debug.Log($"Selection Bounds: {selectionCollider.bounds}");
            }
            
            Bounds physicsBounds = GetPhysicsBounds();
            Debug.Log($"Physics Bounds: {physicsBounds}");
            Debug.Log($"Physics Bounds Size: {physicsBounds.size}");
            Debug.Log($"Is Playing: {Application.isPlaying}");
            
            #if UNITY_EDITOR
            Debug.Log($"Gizmos Enabled in Scene: {UnityEditor.SceneView.lastActiveSceneView != null}");
            #endif
        }

        /// <summary>
        /// 清理运行时可视化组件
        /// </summary>
        void OnDestroy()
        {
            // 清理运行时可视化组件
        }

        /// <summary>
        /// 在Transform变化时更新缓存
        /// </summary>
        void OnTransformParentChanged()
        {
            MarkPhysicsBoundsDirty();
        }

        void OnValidate()
        {
            MarkPhysicsBoundsDirty();
        }
    }
} 