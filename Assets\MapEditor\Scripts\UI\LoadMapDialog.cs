using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using MapEditor.Core;
using MapEditor.Services;

namespace MapEditor.UI
{
    /// <summary>
    /// 加载地图对话框：列出持久化目录中所有已保存地图供用户选择。
    /// </summary>
    public class LoadMapDialog : UIPanel
    {
        public override string PanelId => "loadMapDialog";
        public override string DisplayName => "加载地图";

        public event Action<string> OnMapSelected; // mapId
        public event Action OnCancelled;

        private VisualElement listContainer;
        private Button cancelButton;
        private MapService _mapService;

        public LoadMapDialog(IUIManager uiManager, VisualTreeAsset template) : base(uiManager, template)
        {
        }

        public override void Initialize()
        {
            _mapService = MapEditorCore.Instance.GetService<MapService>();

            if (listContainer == null)
            {
                // 如果没有模板，则创建简易 UI
                root.style.flexDirection = FlexDirection.Column;
                root.style.paddingLeft = 10;
                root.style.paddingRight = 10;
                root.style.paddingTop = 10;
                root.style.paddingBottom = 10;

                listContainer = new ScrollView();
                root.Add(listContainer);

                cancelButton = new Button(() => { Hide(); OnCancelled?.Invoke(); }) { text = "取消" };
                cancelButton.style.marginTop = 8;
                root.Add(cancelButton);
            }
            else
            {
                if (cancelButton != null)
                {
                    cancelButton.clicked += () =>
                    {
                        Hide();
                        OnCancelled?.Invoke();
                    };
                }
            }

            PopulateList();
        }

        private void PopulateList()
        {
            if (listContainer == null) return;
            listContainer.Clear();

            IEnumerable<MapInfo> maps = _mapService.GetAllSavedMaps();
            foreach (var map in maps)
            {
                string path = map.FilePath;
                Button item = new Button(() => { OnMapSelected?.Invoke(path); Hide(); })
                {
                    text = $"{map.MapName}  (修改:{map.LastModified:yyyy-MM-dd HH:mm})",
                    style = { unityTextAlign = TextAnchor.MiddleLeft }
                };
                item.AddToClassList("map-item-button");

                listContainer.Add(item);
            }
        }

        private void Hide()
        {
            IsVisible = false;
        }

        public void ShowDialog()
        {
            IsVisible = true;
            PopulateList();
        }
    }
} 