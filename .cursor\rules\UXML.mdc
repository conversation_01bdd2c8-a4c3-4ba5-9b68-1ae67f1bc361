---
description: UXML 使用指南，当你在编辑uxml遇到困难时，请阅读此文件。
globs: 
alwaysApply: false
---
# UXML 权威指南

## 1. UXML 简介

Unity 可扩展标记语言 (UXML) 是一种基于 XML 的文件格式，用于定义用户界面的结构。它与 Unity 的样式表 (USS) 协同工作，实现了 UI 结构与样式的分离，这使得非技术背景的团队成员（如设计师）也能够轻松地参与到 UI 的布局和风格设计中，而开发者则可以更专注于实现技术性的任务，如资源导入、逻辑编写和数据处理。


## 2. UXML 语法

### 2.1 元素与属性

UXML 通过一系列嵌套的元素来定义 UI 的层级结构。每个元素对应一个 C# 类，元素的名称就是类的名称。

元素的属性 (Attribute) 会被映射到对应 C# 类的属性 (Property)。`VisualElement` 是所有 UI 元素的基类，它提供了一些通用属性，例如：

*   `name`: 元素的唯一标识符。
*   `class`: 用于应用样式的类名，多个类名用空格隔开。
*   `tooltip`: 鼠标悬停时显示的提示信息。
*   `picking-mode`: 定义元素是否响应鼠标事件 (`Position` 或 `Ignore`)。
*   `tabindex`: 定义元素在 Tab 键导航中的顺序。
*   `focusable`: 定义元素是否可以被聚焦。

**示例：**

```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <ui:Box name="container">
        <ui:Toggle name="boots" label="Boots" value="false" />
        <ui:Button name="ok" text="OK" />
    </ui:Box>
</ui:UXML>
```

### 2.2 基本元素

*   [`VisualElement`](mdc:UIE-uxml-element-VisualElement.html): 所有元素的基类，可以作为容器来组织其他元素。
*   [`BindableElement`](mdc:UIE-uxml-element-BindableElement.html): 可数据绑定的元素的基类。

---

## 3. 在 UXML 中使用样式 (USS)

### 3.1 内联样式

可以直接在 UXML 元素的 `style` 属性中编写内联样式，但这通常不推荐，因为它破坏了结构与样式的分离。

```xml
<ui:VisualElement style="width: 200px; height: 200px; background-color: red;" />
```

### 3.2 引用外部 USS 文件

推荐的方式是使用 `<Style>` 标签来引用外部的 USS 文件。

```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <ui:Style src="path/to/your/styles.uss" />
    <ui:VisualElement name="root" />
</ui:UXML>
```

### 3.3 路径规则

在 `src` 属性中，可以使用绝对路径或相对路径：

*   **绝对路径**: 从项目的 `Assets` 目录开始，以 `/` 开头。例如：`/Assets/UI/Styles/main.uss`。
*   **相对路径**: 相对于当前的 UXML 文件。例如：`../Styles/main.uss`。
*   **引用包内资源**: 使用 `project://database/Packages/com.unity.package-name/path/to/file.uss` 的格式。

---

## 4. UXML 文件复用

### 4.1 使用模板

通过 `<Template>` 和 `<Instance>` 元素，可以像使用 Prefab 一样复用 UXML 文件。

1.  **定义模板**: 使用 `<Template>` 标签引用一个 UXML 文件作为模板，并为其命名。
2.  **实例化模板**: 使用 `<Instance>` 标签，并通过 `template` 属性指定要实例化的模板名称。

**示例 (`Portrait.uxml`):**
```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <ui:VisualElement class="portrait">
        <ui:Image name="portaitImage" />
        <ui:Label name="nameLabel" text="Name"/>
    </ui:VisualElement>
</ui:UXML>
```

**示例 (在另一个 UXML 中使用):**
```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <ui:Template src="/Assets/Portrait.uxml" name="Portrait"/>
    <ui:VisualElement name="players">
        <ui:Instance template="Portrait" name="player1"/>
        <ui:Instance template="Portrait" name="player2"/>
    </ui:VisualElement>
</ui:UXML>
```

### 4.2 覆盖模板属性

在实例化模板时，可以使用 `<AttributeOverrides>` 元素来覆盖模板中元素的默认属性值。

```xml
<ui:Instance name="player1" template="Portrait">
    <!-- 覆盖名为 nameLabel 的元素的 text 属性 -->
    <ui:AttributeOverrides element-name="nameLabel" text="Alice" /> 
</ui:Instance>
```

**注意**: 不能使用此方法覆盖 `class`, `name`, 或 `style` 属性。

### 4.3 指定子元素容器

使用 `content-container` 属性，可以指定模板中的某个元素作为容器，用于接收在 `<Instance>` 标签内定义的子元素。

**示例 (`MyTemplate.uxml`):**
```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <ui:VisualElement name="group-container" content-container="anyValue">
         <!-- 子元素将被添加到这里 -->
    </ui:VisualElement>
</ui:UXML>
```

**示例 (使用模板):**
```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <ui:Template path="Assets/MyTemplate.uxml" name="my-template"/>
    <ui:Instance template="my-template">
        <!-- 这个 Label 会被添加到 group-container 元素内部 -->
        <ui:Label text="Test"/> 
    </ui:Instance>
</ui:UXML>
```

---

## 5. 从 C# 与 UXML 交互

### 5.1 加载和实例化 UXML

在 C# 脚本中，UXML 文件被表示为 `VisualTreeAsset` 对象。你需要先加载这个资源，然后实例化它来创建 UI 的视觉树。

```csharp
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

public class MyWindow : EditorWindow
{
    [MenuItem("Window/My Window")]
    public static void ShowWindow()
    {
        EditorWindow w = GetWindow<MyWindow>();

        // 1. 加载 UXML 文件
        VisualTreeAsset uiAsset = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>("Assets/MyWindow.uxml");
        
        // 2. 实例化 UXML
        VisualElement ui = uiAsset.Instantiate();

        // 3. 将 UI 添加到窗口的根元素
        w.rootVisualElement.Add(ui);
    }
}
```

### 5.2 使用 UQuery 查找元素

实例化 UXML 后，可以使用 UQuery 来查询和获取视觉树中的特定元素。

```csharp
// 获取根 VisualElement
var root = myEditorWindow.rootVisualElement;

// 按名称查找按钮
var myButton = root.Q<Button>("my-button-name");

// 按类名查找所有 Label
var allLabels = root.Query<Label>(className: "my-label-class").ToList();
```

---

## 6. 数据绑定 (Data Binding)

UI Toolkit 提供了强大的数据绑定系统，可以将 UI 元素直接绑定到 C# 对象的序列化属性 (`SerializedProperty`)。这意味着你的 UI 可以自动与底层数据同步。

### 6.1 绑定的核心概念

*   **数据源**: 任何与 Unity 序列化系统兼容的对象都可以作为数据源，例如 `MonoBehaviour`、`ScriptableObject` 或其他可序列化的 C# 类。
*   **绑定目标**: 任何实现了 `INotifyValueChanged<T>` 接口的 UI 元素都可以作为绑定目标。通常，我们绑定到元素的 `value` 属性。
*   **绑定路径 (`binding-path`)**: 一个字符串，用于指定要绑定到数据源对象的哪个属性。这个路径就是你在 C# 脚本中看到的序列化属性的名称（例如 `m_Name`）。

### 6.2 创建绑定

有多种方式可以创建数据绑定：

#### 6.2.1 在 UXML 中设置绑定路径

最直接的方式是在 UXML 文件中为可绑定元素设置 `binding-path` 属性。

```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <!-- 将 TextField 绑定到名为 "m_Name" 的属性 -->
    <ui:TextField label="Name" binding-path="m_Name"/>
</ui:UXML>
```

#### 6.2.2 在 C# 中调用 `Bind()`

在 UXML 中设置好 `binding-path` 后，你需要在 C# 脚本中创建一个 `SerializedObject`，然后调用根元素的 `Bind()` 方法。`Bind()` 方法会自动将所有设置了 `binding-path` 的子元素绑定到对应的属性。

```csharp
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

public class SimpleBindingExample : EditorWindow
{
    public VisualTreeAsset visualTree;

    public void CreateGUI()
    {
        visualTree.CloneTree(rootVisualElement);
        OnSelectionChange();
    }

    public void OnSelectionChange()
    {
        GameObject selectedObject = Selection.activeObject as GameObject;
        if (selectedObject != null)
        {
            // 1. 从当前选择的对象创建 SerializedObject
            SerializedObject so = new SerializedObject(selectedObject);
            
            // 2. 将 SerializedObject 绑定到根元素
            rootVisualElement.Bind(so);
        }
        else
        {
            rootVisualElement.Unbind();
        }
    }
}
```

#### 6.2.3 绑定到模板

你甚至可以将整个模板实例 (`<Instance>`) 绑定到一个属性上。如果该属性是一个复杂的对象（如一个 `struct` 或另一个 `class`），模板内部的元素可以继续使用 `binding-path` 来绑定到这个复杂对象的内部属性。

**模板 (`game_switch.uxml`):**
```xml
<UXML xmlns="UnityEngine.UIElements">
    <Box style="flex-direction: row;">
        <Toggle binding-path="enabled" />
        <TextField binding-path="name" readonly="true" />
    </Box>
</UXML>
```

**使用模板的 UXML:**
```xml
<UXML xmlns="UnityEngine.UIElements">
    <Template name="switch" src="../game_switch.uxml"/>
    
    <!-- 将第一个实例绑定到 useLocalServer 属性 -->
    <Instance template="switch" binding-path="useLocalServer" />
    
    <!-- 将第二个实例绑定到 showDebugMenu 属性 -->
    <Instance template="switch" binding-path="showDebugMenu" />
</UXML>
```

---

## 7. 创建自定义控件

除了使用内置控件，UI Toolkit 还允许你创建自己的可复用 UI 组件。一个好的自定义控件应该是抽象的、自包含的，并且可以在多个地方重复使用。

### 7.1 创建自定义控件的步骤

1.  **创建 C# 类**: 创建一个新的 C# 类，并让它继承自 `VisualElement` 或其任何子类。
2.  **添加 `UxmlFactory`**: 在你的自定义控件类内部，添加一个继承自 `UxmlFactory<T>` 的内部类，其中 `T` 是你的自定义控件类。这会将你的控件暴露给 UXML 和 UI Builder。
3.  **定义 UXML 结构 (可选)**: 你可以创建一个单独的 UXML 文件来定义自定义控件的内部结构。然后在控件的 C# 构造函数中加载并克隆这个 UXML 模板。
4.  **添加自定义属性**: 使用 `[UxmlAttribute]` 特性来为你的控件定义可以在 UXML 中设置的属性。
5.  **添加逻辑**: 在 C# 类中编写逻辑来处理事件、操作子元素等。

### 7.2 示例：创建一个简单的自定义控件

**C# 脚本 (`StatusIndicator.cs`):**
```csharp
using UnityEngine.UIElements;

public class StatusIndicator : VisualElement
{
    // 将控件暴露给 UXML
    public new class UxmlFactory : UxmlFactory<StatusIndicator, UxmlTraits> { }

    // 为 UXML 属性定义 Traits
    public new class UxmlTraits : VisualElement.UxmlTraits
    {
        // 定义一个名为 "status" 的字符串属性
        UxmlStringAttributeDescription m_Status =
            new UxmlStringAttributeDescription { name = "status", defaultValue = "unknown" };

        public override void Init(VisualElement ve, IUxmlAttributes bag, CreationContext cc)
        {
            base.Init(ve, bag, cc);
            var ate = ve as StatusIndicator;

            // 从 UXML 中获取属性值并应用
            ate.status = m_Status.GetValueFromBag(bag, cc);
        }
    }

    private Label m_Label;

    public string status
    {
        get => m_Label.text;
        set => m_Label.text = value;
    }

    public StatusIndicator()
    {
        m_Label = new Label();
        Add(m_Label);
    }
}
```

**在 UXML 中使用:**
```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements"
         xmlns:custom="MyProject.Editor.UI">
    
    <!-- 使用自定义控件并设置 status 属性 -->
    <custom:StatusIndicator status="Connected" />
    
</ui:UXML>
```

### 7.3 自定义 UXML 标签名和属性名

*   **自定义标签名**: 在 `UxmlFactory` 的 `UxmlElement` 特性中设置 `name` 参数，可以改变在 UXML 中使用的标签名。
*   **自定义属性名**: 在 `UxmlStringAttributeDescription` (或其他类型) 的构造函数中设置 `name` 参数，可以改变在 UXML 中使用的属性名。

---


## 10. UXML 元素参考

以下是 UI Toolkit 中可用的内置 UXML 元素的摘要。

### 6.1 基本元素
| Element | Namespace | C# Class |
|---|---|---|
| **[`BindableElement`](mdc:UIE-uxml-element-BindableElement.html)** | `UnityEngine.UIElements` | `UnityEngine.UIElements.BindableElement` |
| **[`VisualElement`](mdc:UIE-uxml-element-VisualElement.html)** | `UnityEngine.UIElements` | `UnityEngine.UIElements.VisualElement` |

### 6.2 内置控件
| Element | Bindable | Data Type | Namespace | C# Class |
|---|---|---|---|---|
| **[`BoundsField`](mdc:UIE-uxml-element-BoundsField.html)** | Yes | `UnityEngine.Bounds` | `UnityEngine.UIElements` | `UnityEngine.UIElements.BoundsField` |
| **[`BoundsIntField`](mdc:UIE-uxml-element-BoundsIntField.html)** | Yes | `UnityEngine.BoundsInt` | `UnityEngine.UIElements` | `UnityEngine.UIElements.BoundsIntField` |
| **[`Box`](mdc:UIE-uxml-element-Box.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.Box` |
| **[`Button`](mdc:UIE-uxml-element-Button.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Button` |
| **[`ColorField`](mdc:UIE-uxml-element-ColorField.html)** | Yes | `UnityEngine.Color` | `UnityEditor.UIElements` | `UnityEditor.UIElements.ColorField` |
| **[`CurveField`](mdc:UIE-uxml-element-CurveField.html)** | Yes | `UnityEngine.AnimationCurve` | `UnityEditor.UIElements` | `UnityEditor.UIElements.CurveField` |
| **[`DoubleField`](mdc:UIE-uxml-element-DoubleField.html)** | Yes | `double` | `UnityEngine.UIElements` | `UnityEngine.UIElements.DoubleField` |
| **[`DropdownField`](mdc:UIE-uxml-element-DropdownField.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.DropdownField` |
| **[`EnumField`](mdc:UIE-uxml-element-EnumField.html)** | Yes | `Enum` | `UnityEngine.UIElements` | `UnityEngine.UIElements.EnumField` |
| **[`EnumFlagsField`](mdc:UIE-uxml-element-EnumFlagsField.html)** | Yes | `Enum` | `UnityEditor.UIElements` | `UnityEditor.UIElements.EnumFlagsField` |
| **[`FloatField`](mdc:UIE-uxml-element-FloatField.html)** | Yes | `float` | `UnityEngine.UIElements` | `UnityEngine.UIElements.FloatField` |
| **[`Foldout`](mdc:UIE-uxml-element-Foldout.html)** | Yes | `boolean` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Foldout` |
| **[`GradientField`](mdc:UIE-uxml-element-GradientField.html)** | Yes | `UnityEngine.Gradient` | `UnityEditor.UIElements` | `UnityEditor.UIElements.GradientField` |
| **[`GroupBox`](mdc:UIE-uxml-element-GroupBox.html)** | Yes | | `UnityEngine.UIElements` | `UnityEngine.UIElements.GroupBox` |
| **[`Hash128Field`](mdc:UIE-uxml-element-Hash128Field.html)** | Yes | `UnityEngine.Hash128` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Hash128Field` |
| **[`HelpBox`](mdc:UIE-uxml-element-HelpBox.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.HelpBox` |
| **[`IMGUIContainer`](mdc:UIE-uxml-element-IMGUIContainer.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.IMGUIContainer` |
| **[`Image`](mdc:UIE-uxml-element-Image.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.Image` |
| **[`InspectorElement`](mdc:UIE-uxml-element-InspectorElement.html)** | Yes | | `UnityEditor.UIElements` | `UnityEditor.UIElements.InspectorElement` |
| **[`IntegerField`](mdc:UIE-uxml-element-IntegerField.html)** | Yes | `int` | `UnityEngine.UIElements` | `UnityEngine.UIElements.IntegerField` |
| **[`Label`](mdc:UIE-uxml-element-Label.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Label` |
| **[`LayerField`](mdc:UIE-uxml-element-LayerField.html)** | Yes | `int` | `UnityEditor.UIElements` | `UnityEditor.UIElements.LayerField` |
| **[`LayerMaskField`](mdc:UIE-uxml-element-LayerMaskField.html)** | Yes | `int` | `UnityEditor.UIElements` | `UnityEditor.UIElements.LayerMaskField` |
| **[`ListView`](mdc:UIE-uxml-element-ListView.html)** | Yes | `IList` | `UnityEngine.UIElements` | `UnityEngine.UIElements.ListView` |
| **[`LongField`](mdc:UIE-uxml-element-LongField.html)** | Yes | `long` | `UnityEngine.UIElements` | `UnityEngine.UIElements.LongField` |
| **[`MaskField`](mdc:UIE-uxml-element-MaskField.html)** | Yes | `int` | `UnityEditor.UIElements` | `UnityEditor.UIElements.MaskField` |
| **[`MinMaxSlider`](mdc:UIE-uxml-element-MinMaxSlider.html)** | Yes | `UnityEngine.Vector2` | `UnityEngine.UIElements` | `UnityEngine.UIElements.MinMaxSlider` |
| **[`MultiColumnListView`](mdc:UIE-uxml-element-MultiColumnListView.html)** | Yes | | `UnityEngine.UIElements` | `UnityEngine.UIElements.MultiColumnListView` |
| **[`MultiColumnTreeView`](mdc:UIE-uxml-element-MultiColumnTreeView.html)** | Yes | | `UnityEngine.UIElements` | `UnityEngine.UIElements.MultiColumnTreeView` |
| **[`ObjectField`](mdc:UIE-uxml-element-ObjectField.html)** | Yes | `UnityEngine.Object` | `UnityEditor.UIElements` | `UnityEditor.UIElements.ObjectField` |
| **[`PopupWindow`](mdc:UIE-uxml-element-PopupWindow.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.PopupWindow` |
| **[`ProgressBar`](mdc:UIE-uxml-element-ProgressBar.html)** | Yes | `float` | `UnityEngine.UIElements` | `UnityEngine.UIElements.ProgressBar` |
| **[`PropertyField`](mdc:UIE-uxml-element-PropertyField.html)** | | | `UnityEditor.UIElements` | `UnityEditor.UIElements.PropertyField` |
| **[`RadioButton`](mdc:UIE-uxml-element-RadioButton.html)** | Yes | `boolean` | `UnityEngine.UIElements` | `UnityEngine.UIElements.RadioButton` |
| **[`RadioButtonGroup`](mdc:UIE-uxml-element-RadioButtonGroup.html)** | Yes | `int` | `UnityEngine.UIElements` | `UnityEngine.UIElements.RadioButtonGroup` |
| **[`RectField`](mdc:UIE-uxml-element-RectField.html)** | Yes | `UnityEngine.Rect` | `UnityEngine.UIElements` | `UnityEngine.UIElements.RectField` |
| **[`RectIntField`](mdc:UIE-uxml-element-RectIntField.html)** | Yes | `UnityEngine.RectInt` | `UnityEngine.UIElements` | `UnityEngine.UIElements.RectIntField` |
| **[`RepeatButton`](mdc:UIE-uxml-element-RepeatButton.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.RepeatButton` |
| **[`ScrollView`](mdc:UIE-uxml-element-ScrollView.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.ScrollView` |
| **[`Scroller`](mdc:UIE-uxml-element-Scroller.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.Scroller` |
| **[`Slider`](mdc:UIE-uxml-element-Slider.html)** | Yes | `float` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Slider` |
| **[`SliderInt`](mdc:UIE-uxml-element-SliderInt.html)** | Yes | `int` | `UnityEngine.UIElements` | `UnityEngine.UIElements.SliderInt` |
| **[`Tab`](mdc:UIE-uxml-element-Tab.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.Tab` |
| **[`TabView`](mdc:UIE-uxml-element-TabView.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.TabView` |
| **[`TagField`](mdc:UIE-uxml-element-TagField.html)** | Yes | `string` | `UnityEditor.UIElements` | `UnityEditor.UIElements.TagField` |
| **[`TemplateContainer`](mdc:UIE-uxml-element-TemplateContainer.html)** | Yes | | `UnityEngine.UIElements` | `UnityEngine.UIElements.TemplateContainer` |
| **[`TextElement`](mdc:UIE-uxml-element-TextElement.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.TextElement` |
| **[`TextField`](mdc:UIE-uxml-element-TextField.html)** | Yes | `string` | `UnityEngine.UIElements` | `UnityEngine.UIElements.TextField` |
| **[`Toggle`](mdc:UIE-uxml-element-Toggle.html)** | Yes | `boolean` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Toggle` |
| **[`Toolbar`](mdc:UIE-uxml-element-Toolbar.html)** | | | `UnityEditor.UIElements` | `UnityEditor.UIElements.Toolbar` |
| **[`TreeView`](mdc:UIE-uxml-element-TreeView.html)** | Yes | | `UnityEngine.UIElements` | `UnityEngine.UIElements.TreeView` |
| **[`TwoPaneSplitView`](mdc:UIE-uxml-element-TwoPaneSplitView.html)** | | | `UnityEngine.UIElements` | `UnityEngine.UIElements.TwoPaneSplitView` |
| **[`UnsignedIntegerField`](mdc:UIE-uxml-element-UnsignedIntegerField.html)** | Yes | `System.UInt32` | `UnityEngine.UIElements` | `UnityEngine.UIElements.UnsignedIntegerField` |
| **[`UnsignedLongField`](mdc:UIE-uxml-element-UnsignedLongField.html)** | Yes | `System.UInt64` | `UnityEngine.UIElements` | `UnityEngine.UIElements.UnsignedLongField` |
| **[`Vector2Field`](mdc:UIE-uxml-element-Vector2Field.html)** | Yes | `UnityEngine.Vector2` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Vector2Field` |
| **[`Vector2IntField`](mdc:UIE-uxml-element-Vector2IntField.html)** | Yes | `UnityEngine.Vector2Int` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Vector2IntField` |
| **[`Vector3Field`](mdc:UIE-uxml-element-Vector3Field.html)** | Yes | `UnityEngine.Vector3` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Vector3Field` |
| **[`Vector3IntField`](mdc:UIE-uxml-element-Vector3IntField.html)** | Yes | `UnityEngine.Vector3Int` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Vector3IntField` |
| **[`Vector4Field`](mdc:UIE-uxml-element-Vector4Field.html)** | Yes | `UnityEngine.Vector4` | `UnityEngine.UIElements` | `UnityEngine.UIElements.Vector4Field` |

### 6.3 模板元素
| Element | Description | Namespace | Allowed Children | Attributes |
|---|---|---|---|---|
| `Template` | 引用另一个可使用 `Instance` 元素进行实例化的 UXML 模板。 | `UnityEngine.UIElements` | None | `name`: 唯一标识符<br>`path`: UXML 文件路径 |

| `Instance` | `Template` 的实例。 | `UnityEngine.UIElements` | None | `template`: 要实例化的 `Template` 的 `name` |