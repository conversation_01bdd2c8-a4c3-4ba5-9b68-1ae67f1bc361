using System;
using System.Collections.Generic;

namespace MapEditor.Core
{
    /// <summary>
    /// 事件系统接口，负责管理和分发事件
    /// </summary>
    public interface IEventSystem : IDisposable
    {
        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理方法</param>
        /// <param name="priority">优先级，数值越大优先级越高</param>
        void Subscribe<T>(Action<T> handler, int priority = 0);
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理方法</param>
        void Unsubscribe<T>(Action<T> handler);
        
        /// <summary>
        /// 发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        void Publish<T>(T eventData);
        
        /// <summary>
        /// 异步发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        void PublishAsync<T>(T eventData);
        
        /// <summary>
        /// 处理待处理的异步事件
        /// 必须在主线程定期调用
        /// </summary>
        void ProcessPendingEvents();
        
        /// <summary>
        /// 清除所有事件订阅
        /// </summary>
        void ClearAllSubscriptions();
    }
 
} 