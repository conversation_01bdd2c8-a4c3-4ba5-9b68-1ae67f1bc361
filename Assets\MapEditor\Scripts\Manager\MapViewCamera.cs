using UnityEngine;
using MapEditor.Core;
using UnityEngine.EventSystems;
using UnityEngine.Experimental.Rendering.Universal;


namespace MapEditor.Manager
{
    /// <summary>
    /// 地图视图相机控制器，负责处理相机的平移、缩放等操作
    /// 内部管理 Pixel Perfect Camera 组件
    /// </summary>
    public class MapViewCamera : MonoBehaviour
    {
        [Header("相机设置")]
        [SerializeField] private Camera targetCamera;

        
        [Header("缩放设置")]
        [SerializeField] private float minZoom = 0.25f;  // 调小minZoom以支持更高PPU (32/0.25=128)
        [SerializeField] private float maxZoom = 20f;
        [SerializeField] private float zoomSpeed = 1f;
        [SerializeField] private float zoomSmoothTime = 0.2f;
        [SerializeField] private bool usePixelPerfectZoom = true;  // 使用PPU调整实现像素完美缩放
        [SerializeField] private bool invertZoom = true;  // 反转滚轮缩放方向
        
        [Header("平移设置")]
        [SerializeField] private float panSpeed = 4f;
        [SerializeField] private float panSmoothTime = 0.1f;
        [SerializeField] private bool invertPan = false;
        
        [Header("边界设置")]
        [SerializeField] private bool useBounds = true;
        [SerializeField] private Vector2 boundsPadding = new(20f, 20f); // 边界缓冲，允许相机移出边界以便观察边缘
        [SerializeField] private Vector2 boundsMin = new(-100f, -100f);
        [SerializeField] private Vector2 boundsMax = new(100f, 100f);
        
        [Header("Pixel Perfect设置")]
        [SerializeField] private bool enablePixelPerfectCamera = true;
        [SerializeField] private bool enablePixelSnapping = true;
        [SerializeField] private bool enablePixelPerfectZoom = false;  // 控制是否对缩放应用像素完美约束
        [SerializeField] private int pixelsPerUnit = 32;
        [SerializeField] private int basePPU = 32;  // 基础PPU值，用作缩放倍数1.0的参考
        [SerializeField] private int refResolutionX = 1920;
        [SerializeField] private int refResolutionY = 1080;
        [SerializeField] private PixelPerfectCamera.GridSnapping gridSnappingMode = PixelPerfectCamera.GridSnapping.PixelSnapping;
        [SerializeField] private PixelPerfectCamera.CropFrame cropFrameMode = PixelPerfectCamera.CropFrame.None;
        
        // 内部状态
        private float currentZoom;
        private Vector3 targetPosition;
        private float targetZoom;
        private Vector3 panVelocity;
        private float zoomVelocity;
        private Vector3 lastMousePosition;
        private bool isPanning = false;
        
        // Pixel Perfect Camera 引用
        private PixelPerfectCamera pixelPerfectCamera;
        
        // 事件系统引用
        private IEventSystem eventSystem;
        
        /// <summary>
        /// 初始化相机控制器
        /// </summary>
        public void Initialize(IEventSystem eventSystem)
        {
            this.eventSystem = eventSystem;
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void Awake()
        {
            // 如果未指定相机，使用附加的相机组件
            if (targetCamera == null)
            {
                targetCamera = GetComponent<Camera>();
            }
            
            if (targetCamera == null)
            {
                Debug.LogError("No camera assigned to MapViewCamera!");
                enabled = false;
                return;
            }
            
            // 确保相机是正交的
           // targetCamera.orthographic = true;
            
            // 确保basePPU被正确设置
            if (basePPU <= 0)
            {
                basePPU = pixelsPerUnit > 0 ? pixelsPerUnit : MapEditorConfig.PixelsPerUnit;
            }

            
            // 配置或创建 Pixel Perfect Camera 组件
            ConfigurePixelPerfectCamera();


            // 配置相机基础设置
            ConfigureCamera();

            // 初始化状态
            currentZoom = targetCamera.orthographicSize;
            targetZoom = currentZoom;
            targetPosition = transform.position;
            
            // 显示PPU范围信息
            var (minPPU, maxPPU, currentPPU) = GetPPURange();
            Debug.Log($"MapViewCamera initialized - PPU Range: {minPPU:F1} to {maxPPU:F1}, Current: {currentPPU}, Base: {basePPU}");
            
            // 事件系统将通过Initialize方法注入
        }
        
        /// <summary>
        /// 配置相机基础设置
        /// </summary>
        private void ConfigureCamera()
        {
            if (targetCamera == null) return;
            
            targetCamera.orthographic = true;
            targetCamera.nearClipPlane = 0.1f;
            targetCamera.farClipPlane = 100.0f;
            targetCamera.clearFlags = CameraClearFlags.SolidColor;
            targetCamera.backgroundColor = new Color(0.2f, 0.2f, 0.2f);
            
            Debug.Log("Camera basic settings configured");
        }
        
        /// <summary>
        /// 配置 Pixel Perfect Camera 组件
        /// </summary>
        private void ConfigurePixelPerfectCamera()
        {
            if (!enablePixelPerfectCamera || targetCamera == null) return;
            
            // 检查是否已有 Pixel Perfect Camera 组件（同 GameObject 或其它对象绑定到当前相机）
            pixelPerfectCamera = targetCamera.GetComponent<PixelPerfectCamera>();
            
            if (pixelPerfectCamera == null)
            {
                // 在场景中查找已绑定到 targetCamera 的 PixelPerfectCamera（例如预制体提前挂载的情况）
                foreach (var ppc in Object.FindObjectsOfType<PixelPerfectCamera>())
                {
                    if (ppc != null && ppc.GetComponent<Camera>() == targetCamera)
                    {
                        pixelPerfectCamera = ppc;
                        break;
                    }
                }
            }
            
            if (pixelPerfectCamera == null)
            {
                // 添加 Pixel Perfect Camera 组件
                pixelPerfectCamera = targetCamera.gameObject.AddComponent<PixelPerfectCamera>();
                Debug.Log("Added Pixel Perfect Camera component");
            }
            
            // 配置像素完美设置
            pixelPerfectCamera.assetsPPU = pixelsPerUnit > 0 ? pixelsPerUnit : MapEditorConfig.PixelsPerUnit;
            pixelPerfectCamera.refResolutionX = refResolutionX;
            pixelPerfectCamera.refResolutionY = refResolutionY;
            
            // 使用配置的 gridSnapping 模式
            pixelPerfectCamera.gridSnapping = enablePixelSnapping ? gridSnappingMode : PixelPerfectCamera.GridSnapping.None;
            
            // 使用配置的 cropFrame 模式
            pixelPerfectCamera.cropFrame = cropFrameMode;
            
            Debug.Log($"Pixel Perfect Camera configured - PPU: {pixelPerfectCamera.assetsPPU}, Resolution: {pixelPerfectCamera.refResolutionX}x{pixelPerfectCamera.refResolutionY}, GridSnapping: {pixelPerfectCamera.gridSnapping}, CropFrame: {pixelPerfectCamera.cropFrame}");
        }
        
        /// <summary>
        /// 启用组件时的处理
        /// </summary>
        private void OnEnable()
        {
            // 可以在这里订阅事件
        }
        
        /// <summary>
        /// 停用组件时的处理
        /// </summary>
        private void OnDisable()
        {
            // 可以在这里取消订阅事件
        }
        
        /// <summary>
        /// 更新相机控制
        /// </summary>
        private void LateUpdate()
        {
            HandleInput();
            UpdateCameraTransform();
        }
        
        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            // 处理鼠标滚轮缩放
            float scrollDelta = Input.mouseScrollDelta.y;
            if (scrollDelta != 0)
            {
                // 如果指针位于 UI 上，则忽略滚轮事件
                if (!IsPointerOverUI())
                {
                    if (usePixelPerfectZoom && pixelPerfectCamera != null)
                    {
                        // 使用PPU调整实现像素完美缩放
                        float currentPPU = pixelPerfectCamera.assetsPPU;
                        
                        // 根据invertZoom设置确定缩放方向
                        float zoomDirection = invertZoom ? 1f : -1f;
                        float newPPU = currentPPU + scrollDelta * zoomDirection * zoomSpeed * 2f;
                        
                        // 将PPU限制在合理范围内（基于缩放限制计算）
                        float minPPU = basePPU / maxZoom;  // 最大缩放对应最小PPU
                        float maxPPU = basePPU / minZoom;  // 最小缩放对应最大PPU
                        newPPU = Mathf.Clamp(newPPU, minPPU, maxPPU);
                        
                        pixelPerfectCamera.assetsPPU = Mathf.RoundToInt(newPPU);
                        
                        //Debug.Log($"PPU Zoom: PPU={pixelPerfectCamera.assetsPPU} (range: {minPPU:F1}-{maxPPU:F1}), Scale={basePPU/(float)pixelPerfectCamera.assetsPPU:F2}x");
                    }
                    else
                    {
                        // 传统缩放方式
                        float zoomDirection = invertZoom ? 1f : -1f;
                        float newZoom = targetZoom + scrollDelta * zoomDirection * zoomSpeed;
                        
                        // 只有在启用像素完美缩放时才应用像素完美约束
                        if (pixelPerfectCamera != null && enablePixelPerfectZoom)
                        {
                            newZoom = GetPixelPerfectZoom(newZoom);
                        }
                        
                        targetZoom = Mathf.Clamp(newZoom, minZoom, maxZoom);
                        
                        //Debug.Log($"Traditional Zoom: scrollDelta={scrollDelta}, newZoom={newZoom}, targetZoom={targetZoom}");
                    }
                }
            }
            
            // 处理鼠标中键拖拽平移
            if (Input.GetMouseButtonDown(2)) // 鼠标中键按下
            {
                // 如果按下时指针在 UI 上，则不开始拖拽
                if (!IsPointerOverUI())
                {
                    isPanning = true;
                    lastMousePosition = Input.mousePosition;
                }
            }
            else if (Input.GetMouseButtonUp(2)) // 鼠标中键释放
            {
                isPanning = false;
            }
            
            // 拖拽过程中，如果之前成功进入拖拽状态才处理位移
            if (isPanning)
            {
                // 若当前指针移到 UI 之上，则暂停位移
                if (IsPointerOverUI())
                {
                    lastMousePosition = Input.mousePosition;
                    return;
                }
                
                Vector3 delta = Input.mousePosition - lastMousePosition;
                Vector3 panDelta = new Vector3(delta.x, delta.y, 0) * panSpeed * currentZoom / 1000f;
                
                if (invertPan)
                {
                    panDelta = -panDelta;
                }
                
                targetPosition -= panDelta;
                
                // 应用边界限制
                if (useBounds)
                {
                    float vertExtent = currentZoom;
                    float horizExtent = vertExtent * targetCamera.aspect;

                    // 水平边界处理
                    float boundsWidth = boundsMax.x - boundsMin.x;
                    if (horizExtent * 2 >= boundsWidth)
                    {
                        // 当视口大于或等于边界时，将相机水平居中
                        targetPosition.x = (boundsMin.x + boundsMax.x) / 2f;
                    }
                    else
                    {
                        // 否则，正常限制相机位置
                        float minX = boundsMin.x + horizExtent - boundsPadding.x;
                        float maxX = boundsMax.x - horizExtent + boundsPadding.x;
                        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
                    }

                    // 垂直边界处理
                    float boundsHeight = boundsMax.y - boundsMin.y;
                    if (vertExtent * 2 >= boundsHeight)
                    {
                        // 当视口大于或等于边界时，将相机垂直居中
                        targetPosition.y = (boundsMin.y + boundsMax.y) / 2f;
                    }
                    else
                    {
                        // 否则，正常限制相机位置
                        float minY = boundsMin.y + vertExtent - boundsPadding.y;
                        float maxY = boundsMax.y - vertExtent + boundsPadding.y;
                        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
                    }
                }
                
                lastMousePosition = Input.mousePosition;
            }
        }
        
        /// <summary>
        /// 更新相机变换
        /// </summary>
        private void UpdateCameraTransform()
        {
            // 平滑过渡到目标位置和缩放
            Vector3 smoothPosition = Vector3.SmoothDamp(transform.position, targetPosition, ref panVelocity, panSmoothTime);
            currentZoom = Mathf.SmoothDamp(currentZoom, targetZoom, ref zoomVelocity, zoomSmoothTime);
            
            // 不再执行自定义像素对齐
            
            transform.position = smoothPosition;
            targetCamera.orthographicSize = currentZoom;
        }
        
        /// <summary>
        /// 获取适配像素完美相机的缩放值
        /// </summary>
        private float GetPixelPerfectZoom(float desiredZoom)
        {
            if (pixelPerfectCamera == null) return desiredZoom;
            
            // 对于像素完美相机，我们仍然允许自由缩放
            // PixelPerfectCamera会自动处理像素完美渲染
            // 只需要确保缩放值在合理范围内
            return desiredZoom;
        }
        
        /// <summary>
        /// 设置相机位置
        /// </summary>
        public void SetPosition(Vector2 position, bool immediate = false)
        {
            targetPosition = new Vector3(position.x, position.y, transform.position.z);
            
            // 不再执行自定义像素对齐
            
            if (immediate)
            {
                transform.position = targetPosition;
                panVelocity = Vector3.zero;
            }
        }
        
        /// <summary>
        /// 设置相机缩放
        /// </summary>
        public void SetZoom(float zoom, bool immediate = false)
        {
            // 只有在启用像素完美缩放时才应用像素完美约束
            if (pixelPerfectCamera != null && enablePixelPerfectZoom)
            {
                zoom = GetPixelPerfectZoom(zoom);
            }
            
            targetZoom = Mathf.Clamp(zoom, minZoom, maxZoom);
            
            if (immediate)
            {
                currentZoom = targetZoom;
                targetCamera.orthographicSize = currentZoom;
                zoomVelocity = 0f;
            }
        }
        
        /// <summary>
        /// 设置相机边界
        /// </summary>
        public void SetBounds(Vector2 min, Vector2 max)
        {
            boundsMin = min;
            boundsMax = max;
            
            // 立即应用边界限制
            if (useBounds)
            {
                float vertExtent = currentZoom;
                float horizExtent = vertExtent * targetCamera.aspect;

                // 水平边界处理
                float boundsWidth = boundsMax.x - boundsMin.x;
                if (horizExtent * 2 >= boundsWidth)
                {
                    targetPosition.x = (boundsMin.x + boundsMax.x) / 2f;
                }
                else
                {
                    float minX = boundsMin.x + horizExtent - boundsPadding.x;
                    float maxX = boundsMax.x - horizExtent + boundsPadding.x;
                    targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
                }

                // 垂直边界处理
                float boundsHeight = boundsMax.y - boundsMin.y;
                if (vertExtent * 2 >= boundsHeight)
                {
                    targetPosition.y = (boundsMin.y + boundsMax.y) / 2f;
                }
                else
                {
                    float minY = boundsMin.y + vertExtent - boundsPadding.y;
                    float maxY = boundsMax.y - vertExtent + boundsPadding.y;
                    targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
                }
            }
        }
        
        /// <summary>
        /// 屏幕坐标转换为世界坐标
        /// </summary>
        public Vector2 ScreenToWorldPoint(Vector2 screenPoint)
        {
            return targetCamera.ScreenToWorldPoint(new Vector3(screenPoint.x, screenPoint.y, 0));
        }
        
        /// <summary>
        /// 世界坐标转换为屏幕坐标
        /// </summary>
        public Vector2 WorldToScreenPoint(Vector2 worldPoint)
        {
            Vector3 screenPoint = targetCamera.WorldToScreenPoint(new Vector3(worldPoint.x, worldPoint.y, 0));
            return new Vector2(screenPoint.x, screenPoint.y);
        }

        private bool IsPointerOverUI()
        {
            // 如果场景中没有 EventSystem, 默认认为不在 UI 上
            if (EventSystem.current == null) return false;
            // 对于旧输入系统与新 InputSystem + UI Toolkit 均适用
            return EventSystem.current.IsPointerOverGameObject();
        }
        
        #region Pixel Perfect Camera 支持
        
        /// <summary>
        /// 获取是否启用像素对齐
        /// </summary>
        public bool IsPixelSnappingEnabled => enablePixelSnapping && pixelPerfectCamera != null;
        
        /// <summary>
        /// 获取像素完美相机组件
        /// </summary>
        public PixelPerfectCamera PixelPerfectCamera => pixelPerfectCamera;
        
        /// <summary>
        /// 设置像素对齐开关
        /// </summary>
        public void SetPixelSnapping(bool enabled)
        {
            enablePixelSnapping = enabled;
            if (pixelPerfectCamera != null)
            {
                if (enabled)
                {
                    pixelPerfectCamera.gridSnapping = gridSnappingMode;
                }
                else
                {
                    pixelPerfectCamera.gridSnapping = PixelPerfectCamera.GridSnapping.None;
                }
            }
            
            // 不再执行自定义像素对齐
        }
        
        /// <summary>
        /// 获取当前的像素每单位数
        /// </summary>
        public int GetPixelsPerUnit()
        {
            return pixelPerfectCamera != null ? pixelPerfectCamera.assetsPPU : pixelsPerUnit;
        }
        
        /// <summary>
        /// 设置像素完美相机的PPU
        /// </summary>
        public void SetPixelsPerUnit(int ppu)
        {
            if (ppu <= 0) return;
            
            pixelsPerUnit = ppu;
            if (pixelPerfectCamera != null)
            {
                pixelPerfectCamera.assetsPPU = ppu;
            }
        }
        
        /// <summary>
        /// 设置参考分辨率
        /// </summary>
        public void SetReferenceResolution(int width, int height)
        {
            if (width <= 0 || height <= 0) return;
            
            refResolutionX = width;
            refResolutionY = height;
            
            if (pixelPerfectCamera != null)
            {
                pixelPerfectCamera.refResolutionX = width;
                pixelPerfectCamera.refResolutionY = height;
            }
        }
        
        /// <summary>
        /// 设置网格对齐模式
        /// </summary>
        public void SetGridSnappingMode(PixelPerfectCamera.GridSnapping mode)
        {
            gridSnappingMode = mode;
            if (pixelPerfectCamera != null)
            {
                pixelPerfectCamera.gridSnapping = enablePixelSnapping ? mode : PixelPerfectCamera.GridSnapping.None;
            }
        }
        
        /// <summary>
        /// 设置裁剪帧模式
        /// </summary>
        public void SetCropFrameMode(PixelPerfectCamera.CropFrame mode)
        {
            cropFrameMode = mode;
            if (pixelPerfectCamera != null)
            {
                pixelPerfectCamera.cropFrame = mode;
            }
        }
        
        /// <summary>
        /// 获取当前网格对齐模式
        /// </summary>
        public PixelPerfectCamera.GridSnapping GetGridSnappingMode()
        {
            return pixelPerfectCamera != null ? pixelPerfectCamera.gridSnapping : gridSnappingMode;
        }
        
        /// <summary>
        /// 获取当前裁剪帧模式
        /// </summary>
        public PixelPerfectCamera.CropFrame GetCropFrameMode()
        {
            return pixelPerfectCamera != null ? pixelPerfectCamera.cropFrame : cropFrameMode;
        }
        
        /// <summary>
        /// 设置PPU缩放模式的开关
        /// </summary>
        public void SetUsePixelPerfectZoom(bool enabled)
        {
            usePixelPerfectZoom = enabled;
        }
        
        /// <summary>
        /// 设置滚轮缩放方向反转
        /// </summary>
        public void SetInvertZoom(bool invert)
        {
            invertZoom = invert;
        }
        
        /// <summary>
        /// 获取当前的缩放倍数（基于PPU）
        /// </summary>
        public float GetCurrentZoomScale()
        {
            if (pixelPerfectCamera != null && usePixelPerfectZoom)
            {
                return (float)basePPU / pixelPerfectCamera.assetsPPU;
            }
            return 1f;
        }
        
        /// <summary>
        /// 获取PPU范围信息
        /// </summary>
        public (float minPPU, float maxPPU, float currentPPU) GetPPURange()
        {
            float minPPU = basePPU / maxZoom;
            float maxPPU = basePPU / minZoom;
            float currentPPU = pixelPerfectCamera != null ? pixelPerfectCamera.assetsPPU : basePPU;
            
            return (minPPU, maxPPU, currentPPU);
        }
        
        /// <summary>
        /// 获取缩放倍数范围信息
        /// </summary>
        public (float minScale, float maxScale, float currentScale) GetZoomScaleRange()
        {
            return (minZoom, maxZoom, GetCurrentZoomScale());
        }
        
        /// <summary>
        /// 设置PPU缩放倍数
        /// </summary>
        public void SetPPUZoomScale(float scale)
        {
            if (pixelPerfectCamera == null || !usePixelPerfectZoom) return;
            
            scale = Mathf.Clamp(scale, minZoom, maxZoom);
            int newPPU = Mathf.RoundToInt(basePPU / scale);
            pixelPerfectCamera.assetsPPU = newPPU;
            
            Debug.Log($"Set PPU zoom scale: {scale}x, PPU: {newPPU}");
        }
        
        /// <summary>
        /// 重置PPU到基础值
        /// </summary>
        public void ResetPPUZoom()
        {
            if (pixelPerfectCamera != null)
            {
                pixelPerfectCamera.assetsPPU = basePPU;
            }
        }
        
        /// <summary>
        /// 启用或禁用像素完美相机
        /// </summary>
        public void SetPixelPerfectCameraEnabled(bool enabled)
        {
            enablePixelPerfectCamera = enabled;
            
            if (enabled)
            {
                ConfigurePixelPerfectCamera();
            }
            else
            {
                if (pixelPerfectCamera != null)
                {
                    DestroyImmediate(pixelPerfectCamera);
                    pixelPerfectCamera = null;
                }
            }
        }
        
        #endregion
    }
} 