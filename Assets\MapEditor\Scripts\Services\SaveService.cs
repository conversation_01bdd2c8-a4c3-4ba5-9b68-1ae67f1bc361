using System;
using System.Collections.Generic;
using UnityEngine;
using MapEditor.Core;
using MapEditor.Event;

namespace MapEditor.Services
{
    /// <summary>
    /// 统一保存服务：订阅 SaveRequestEvent，遍历所有已注册 ISavable 执行保存。
    /// 不继承 MonoBehaviour，生命周期由依赖容器管理。
    /// </summary>
    public class SaveService:ServiceBase
    {

        private readonly List<ISavable> _savables = new();



        /// <summary>
        /// 构造函数，在创建时设置单例引用。
        /// </summary>
        public override void Initialize()
        {

        }

        public override void Start()
        {
            RegisterEvent<SaveRequestEvent>(OnSaveRequest, priority: 100);
            if (core.MapDataStore is ISavable savable)
            {
                RegisterSavable(savable);
            }
        }

        /// <summary>
        /// 外部模块注册。
        /// </summary>
        public void RegisterSavable(ISavable savable)
        {
            if (savable == null) return;
            if (!_savables.Contains(savable)) _savables.Add(savable);
        }

        public void UnregisterSavable(ISavable savable)
        {
            _savables.Remove(savable);
        }

        private void OnSaveRequest(SaveRequestEvent evt)
        {
            string mapDir = string.Empty;
            if (core.MapDataStore is IMapDataStore ds)
            {
                mapDir = ds.CurrentMapDirectory;
            }
            var ctx = new SaveContext(evt.IsAuto, mapDir);

            foreach (var s in _savables)
            {
                try
                {
                    s.Save(ctx);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[SaveService] Error while saving {s}: {ex.Message}\n{ex.StackTrace}");
                }
            }

            Debug.Log($"[SaveService] Save completed. Auto: {evt.IsAuto}, Savable count: {_savables.Count}");
        }
    }
} 